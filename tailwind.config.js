/** @type {import('tailwindcss').Config} */
export const content = [
  './Barret.Web.Server/**/*.{razor,html,cshtml}',
];
export const theme = {
  extend: {
    colors: {
      gray: {
        50: '#f9fafb',
        100: '#f3f4f6',
        200: '#e5e7eb',
        300: '#d1d5db',
        400: '#9ca3af',
        500: '#6b7280',
        600: '#4b5563',
        700: '#374151',
        800: '#1f2937',
        900: '#111827',
      },
    },
    borderRadius: {
      'xl': '0.75rem',
    },
    maxWidth: {
      '4xl': '56rem',
    },
  },
};
export const plugins = [];
export const safelist = [
  // Add classes that might be used dynamically
  'bg-white',
  'bg-gray-50',
  'bg-gray-100',
  'bg-gray-200',
  'bg-gray-800',
  'bg-gray-900',
  'text-white',
  'text-gray-500',
  'text-gray-700',
  'text-gray-900',
  'hover:bg-gray-800',
  'hover:shadow-md',
  'border-0',
  'border-gray-100',
  'border-gray-200',
  'rounded-xl',
  'rounded-full',
  'shadow-sm',
  'transition-shadow',
  'transition-colors',
  'duration-300',
  'h-full',
  'h-8',
  'h-12',
  'h-16',
  'w-8',
  'w-12',
  'w-16',
  'animate-spin',
  'border-t-gray-800',
  'border-4',
  'text-sm',
  'text-base',
  'text-lg',
  'text-xl',
  'text-2xl',
  'text-3xl',
  'font-normal',
  'font-medium',
  'max-w-[1200px]',
  'max-w-4xl',
  'grid-cols-1',
  'md:grid-cols-2',
  'lg:grid-cols-4',
  'gap-6',
  'mb-2',
  'mb-6',
  'mb-8',
  'md:mb-16',
  'mt-2',
  'mt-4',
  'mt-auto',
  'px-4',
  'px-6',
  'py-2',
  'py-8',
  'p-8',
  'flex-1',
  'flex-col',
  'items-center',
  'justify-center',
  'text-center',
  'overflow-hidden',
  'cursor-pointer',
];
