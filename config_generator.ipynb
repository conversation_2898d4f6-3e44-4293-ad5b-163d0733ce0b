{"cells": [{"cell_type": "code", "execution_count": 20, "id": "aa4a43e2-6018-4838-ab02-133949233d1e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: pandas in /home/<USER>/.local/share/pipx/venvs/notebook/lib/python3.12/site-packages (2.2.3)\n", "Requirement already satisfied: numpy>=1.26.0 in /home/<USER>/.local/share/pipx/venvs/notebook/lib/python3.12/site-packages (from pandas) (2.2.1)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in /home/<USER>/.local/share/pipx/venvs/notebook/lib/python3.12/site-packages (from pandas) (2.9.0.post0)\n", "Requirement already satisfied: pytz>=2020.1 in /home/<USER>/.local/share/pipx/venvs/notebook/lib/python3.12/site-packages (from pandas) (2024.2)\n", "Requirement already satisfied: tzdata>=2022.7 in /home/<USER>/.local/share/pipx/venvs/notebook/lib/python3.12/site-packages (from pandas) (2024.2)\n", "Requirement already satisfied: six>=1.5 in /home/<USER>/.local/share/pipx/venvs/notebook/lib/python3.12/site-packages (from python-dateutil>=2.8.2->pandas) (1.17.0)\n", "Requirement already satisfied: openpyxl in /home/<USER>/.local/share/pipx/venvs/notebook/lib/python3.12/site-packages (3.1.5)\n", "Requirement already satisfied: et-xmlfile in /home/<USER>/.local/share/pipx/venvs/notebook/lib/python3.12/site-packages (from openpyxl) (2.0.0)\n"]}], "source": ["import sys\n", "!{sys.executable} -m pip install pandas\n", "!{sys.executable} -m pip install openpyxl\n"]}, {"cell_type": "code", "execution_count": 35, "id": "19f419b2", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import uuid\n", "import json"]}, {"cell_type": "code", "execution_count": 36, "id": "87c9e11d", "metadata": {}, "outputs": [], "source": ["allowed_propulsion_action_ids = ['ControlAction', 'ControlFeedback', 'ActionDirection', 'FeedbackDirection', 'ActionPower', 'FeedbackPower', 'FeedbackRpm', 'ActionRot', 'RequestedDirection', 'RequestedPower', 'KeepAlivePolling', 'ActionTogglePropulsion', 'PropulsionStatusFeedback', 'ToggleActionPulse', 'LocalControlRequest', 'PilotModeRequest', 'PilotModeFeedback', 'ClutchFeedbackForward', 'ClutchFeedbackNeutral', 'ClutchFeedbackReverse', 'StartPropulsionAction', 'StopPropulsionAction', 'PilotManualModeRequest', 'PilotRotModeRequest', 'PilotManualModeFeedback', 'PilotRotModeFeedback', 'ThirdPartySafetyContactFeedback']\n", "allowed_system_function_action_ids = [\"ControlAction\", \"ControlFeedback\", \"ToggleAction\", \"ActionFeedback\", \"MoveUpAction\", \"MoveDownAction\", \"MoveLeftAction\", \"MoveRightAction\", \"MoveUpFeedback\", \"MoveDownFeedback\", \"MoveLeftFeedback\", \"MoveRightFeedback\", \"ToggleActionPulse\", \"KeepAlivePolling\", \"RegisterFillValue\", 'ThirdPartySafetyContactFeedback']\n", "allowed_alarm_ids = [\"MiBatteryLowVoltage\", \"MiBatteryHighVoltage\", \"MiTemperatureExceededThresholds\", \"MiPropulsionNotReady\", \"MiOilPressureOutOfBounds\", \"MiCoolantLiquidOutOfBounds\", \"MiPropulsionOverspeed\", \"MiFuelOutOfBounds\", \"MiGeneratorGeneral\", \"MiBilgeGeneral\", \"MiSpudpoleGeneral\", \"MiPowerGeneral\", \"MiFireGeneral\", \"MiCommunicationGeneral\", \"MiNavigationEquipmentDefect\", \"MiExternalEmergencyStop\", \"MiExternalPlcGeneral\", \"MiHydraulicFailure\", \"MiTankOutOfBounds\", \"MiHatchOpen\", \"MiFunctionStateMismatch\", \"MiPressureOutOfBounds\", \"MiPumpGeneral\", \"MiGasDetection\", \"MiWaterInFuel\", \"MiRedGroupActive\", \"MiNoFlowCoolingWater\", \"MiExternalSystemGeneral\", \"DaremiWindSpeedTooHigh\", \"DaremiWeightTooHigh\", \"DaremiTiltTooHigh\"]\n", "allowed_system_ids = [\"BridgeWarningIndicator\", \"BridgeAlarmIndicator\", \"AlarmGeneral\", \"AlarmFire\", \"AcknowledgeAlarms\", \"AcknowledgeWarnings\", \"Reset\", \"CargoLights\", \"DeckLight1\", \"DeckLight2\", \"DeckLight3\", \"DeckLight4\", \"StrobeLights\", \"Siren\", \"UpMastPole1\", \"UpMastPole2\", \"DropAnchorStern1\", \"DropAnchorBow1\", \"WiFiAccessPoint\", \"SearchLight1\", \"UpMastPole3\", \"UpMastPole4\", \"DownSpudPoleStern1\", \"DownSpudPoleBow1\", \"CallSystemRoom1\", \"CallSystemRoom2\", \"CallSystemRoom3\", \"CallSystemRoom4\", \"LightMasthead\", \"LightSides\", \"LightStern\", \"LightTowing\", \"LightAnchor\", \"LightSpecial\", \"LightBlueSign\", \"LightHorn\", \"LightSail\", \"LightAdnrSign1\", \"LightAdnrSign2\", \"Horn\"]\n", "allowed_joystick_parsing_ids = ['X', 'Y', 'Z']\n", "allowed_extra_ids = ['SpeedLevel0', 'SpeedLevelForward1', 'SpeedLevelReverse1', 'SpeedLevel2', 'SpeedLevel3', 'SpeedLevel4']"]}, {"cell_type": "code", "execution_count": 37, "id": "e9203261", "metadata": {}, "outputs": [], "source": ["config_df = pd.read_excel('input/20250124SystemFunctionsInterfaces.xlsx', 'Dignity - Werkina - System func')\n", "vehicle_id = 69"]}, {"cell_type": "markdown", "id": "c146f08b", "metadata": {}, "source": ["## Group the configs\n", "Assumption is made that the control & feedback registers will appear in the same order. Using this assumption we create a group id to know what system function configs should be grouped"]}, {"cell_type": "code", "execution_count": 38, "id": "bc0ba2b2", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_37218/2344871794.py:1: FutureWarning: Setting an item of incompatible dtype is deprecated and will raise an error in a future version of pandas. Value '' has dtype incompatible with float64, please explicitly cast to a compatible dtype first.\n", "  config_df.fillna('', inplace=True)\n"]}], "source": ["config_df.fillna('', inplace=True)\n", "config_df['GroupId'] = config_df.groupby(['Ip', 'DomainDriverPurpose', 'DomainDriverId']).cumcount()\n", "\n"]}, {"cell_type": "code", "execution_count": 39, "id": "3cce51a8", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Description</th>\n", "      <th>ValueType</th>\n", "      <th>RegisterType</th>\n", "      <th>Register</th>\n", "      <th>Bit</th>\n", "      <th>R/W</th>\n", "      <th>Comment</th>\n", "      <th>Type</th>\n", "      <th>DomainDriverId</th>\n", "      <th>DomainDriverPurpose</th>\n", "      <th>Ip</th>\n", "      <th>ServerName</th>\n", "      <th>AlarmMessage</th>\n", "      <th>NotificationGroupId</th>\n", "      <th>NotificationTypeId</th>\n", "      <th>EntityId</th>\n", "      <th>GroupId</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Ship Plc</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Heartbeat 3rd party -&gt; Sf system</td>\n", "      <td>BOOL</td>\n", "      <td>Holding Register</td>\n", "      <td>101.0</td>\n", "      <td>0.0</td>\n", "      <td>READ</td>\n", "      <td>Bit that toggles between 0 and 1 each second</td>\n", "      <td>HeartbeatRead</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>**************</td>\n", "      <td>Ship Plc</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Seafar Control Feedback</td>\n", "      <td>BOOL</td>\n", "      <td>Holding Register</td>\n", "      <td>101.0</td>\n", "      <td>1.0</td>\n", "      <td>READ</td>\n", "      <td>0 = Seafar does not request control, 1 = Seafa...</td>\n", "      <td>SystemFunctions</td>\n", "      <td>BridgeWarningIndicator</td>\n", "      <td>ControlFeedback</td>\n", "      <td>**************</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Seafar potential free contact feedback</td>\n", "      <td>BOOL</td>\n", "      <td>Holding Register</td>\n", "      <td>101.0</td>\n", "      <td>2.0</td>\n", "      <td>READ</td>\n", "      <td>0 = Seafar cannot take control, 1 = Seafar can...</td>\n", "      <td>SystemFunctions</td>\n", "      <td>BridgeWarningIndicator</td>\n", "      <td>ThirdPartySafetyContactFeedback</td>\n", "      <td>**************</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Local control request</td>\n", "      <td>BOOL</td>\n", "      <td>Holding Register</td>\n", "      <td>101.0</td>\n", "      <td>3.0</td>\n", "      <td>READ</td>\n", "      <td>Pulse when button is pressed on third party sy...</td>\n", "      <td>Reserved</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>**************</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>669</th>\n", "      <td>-</td>\n", "      <td>BOOL</td>\n", "      <td>Holding Register</td>\n", "      <td>221.0</td>\n", "      <td>11.0</td>\n", "      <td>WRITE</td>\n", "      <td></td>\n", "      <td>Reserved</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>**************</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>483</td>\n", "    </tr>\n", "    <tr>\n", "      <th>670</th>\n", "      <td>-</td>\n", "      <td>BOOL</td>\n", "      <td>Holding Register</td>\n", "      <td>221.0</td>\n", "      <td>12.0</td>\n", "      <td>WRITE</td>\n", "      <td></td>\n", "      <td>Reserved</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>**************</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>484</td>\n", "    </tr>\n", "    <tr>\n", "      <th>671</th>\n", "      <td>-</td>\n", "      <td>BOOL</td>\n", "      <td>Holding Register</td>\n", "      <td>221.0</td>\n", "      <td>13.0</td>\n", "      <td>WRITE</td>\n", "      <td></td>\n", "      <td>Reserved</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>**************</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>485</td>\n", "    </tr>\n", "    <tr>\n", "      <th>672</th>\n", "      <td>-</td>\n", "      <td>BOOL</td>\n", "      <td>Holding Register</td>\n", "      <td>221.0</td>\n", "      <td>14.0</td>\n", "      <td>WRITE</td>\n", "      <td></td>\n", "      <td>Reserved</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>**************</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>486</td>\n", "    </tr>\n", "    <tr>\n", "      <th>673</th>\n", "      <td>-</td>\n", "      <td>BOOL</td>\n", "      <td>Holding Register</td>\n", "      <td>221.0</td>\n", "      <td>15.0</td>\n", "      <td>WRITE</td>\n", "      <td></td>\n", "      <td>Reserved</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>**************</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>487</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>674 rows × 17 columns</p>\n", "</div>"], "text/plain": ["                                Description ValueType      RegisterType  \\\n", "0                                  Ship Plc                               \n", "1          Heartbeat 3rd party -> Sf system      BOOL  Holding Register   \n", "2                   Seafar Control Feedback      BOOL  Holding Register   \n", "3    Seafar potential free contact feedback      BOOL  Holding Register   \n", "4                     Local control request      BOOL  Holding Register   \n", "..                                      ...       ...               ...   \n", "669                                       -      BOOL  Holding Register   \n", "670                                       -      BOOL  Holding Register   \n", "671                                       -      BOOL  Holding Register   \n", "672                                       -      BOOL  Holding Register   \n", "673                                       -      BOOL  Holding Register   \n", "\n", "    Register   Bit    R/W                                            Comment  \\\n", "0                                                                              \n", "1      101.0   0.0   READ       Bit that toggles between 0 and 1 each second   \n", "2      101.0   1.0   READ  0 = Seafar does not request control, 1 = Seafa...   \n", "3      101.0   2.0   READ  0 = Seafar cannot take control, 1 = Seafar can...   \n", "4      101.0   3.0   READ  Pulse when button is pressed on third party sy...   \n", "..       ...   ...    ...                                                ...   \n", "669    221.0  11.0  WRITE                                                      \n", "670    221.0  12.0  WRITE                                                      \n", "671    221.0  13.0  WRITE                                                      \n", "672    221.0  14.0  WRITE                                                      \n", "673    221.0  15.0  WRITE                                                      \n", "\n", "                Type          DomainDriverId              DomainDriverPurpose  \\\n", "0                                                                               \n", "1      HeartbeatRead                                                            \n", "2    SystemFunctions  BridgeWarningIndicator                  ControlFeedback   \n", "3    SystemFunctions  BridgeWarningIndicator  ThirdPartySafetyContactFeedback   \n", "4           Reserved                                                            \n", "..               ...                     ...                              ...   \n", "669         Reserved                                                            \n", "670         Reserved                                                            \n", "671         Reserved                                                            \n", "672         Reserved                                                            \n", "673         Reserved                                                            \n", "\n", "                 Ip ServerName AlarmMessage NotificationGroupId  \\\n", "0                                                                 \n", "1    **************   Ship Plc                                    \n", "2    **************                                               \n", "3    **************                                               \n", "4    **************                                               \n", "..              ...        ...          ...                 ...   \n", "669  **************                                               \n", "670  **************                                               \n", "671  **************                                               \n", "672  **************                                               \n", "673  **************                                               \n", "\n", "    NotificationTypeId EntityId  GroupId  \n", "0                                      0  \n", "1                                      0  \n", "2                                      0  \n", "3                                      0  \n", "4                                      1  \n", "..                 ...      ...      ...  \n", "669                                  483  \n", "670                                  484  \n", "671                                  485  \n", "672                                  486  \n", "673                                  487  \n", "\n", "[674 rows x 17 columns]"]}, "execution_count": 39, "metadata": {}, "output_type": "execute_result"}], "source": ["config_df"]}, {"cell_type": "code", "execution_count": 40, "id": "0011e4bc", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Description</th>\n", "      <th>ValueType</th>\n", "      <th>RegisterType</th>\n", "      <th>Register</th>\n", "      <th>Bit</th>\n", "      <th>R/W</th>\n", "      <th>Comment</th>\n", "      <th>Type</th>\n", "      <th>DomainDriverId</th>\n", "      <th>DomainDriverPurpose</th>\n", "      <th>Ip</th>\n", "      <th>ServerName</th>\n", "      <th>AlarmMessage</th>\n", "      <th>NotificationGroupId</th>\n", "      <th>NotificationTypeId</th>\n", "      <th>EntityId</th>\n", "      <th>GroupId</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Ship Plc</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Heartbeat 3rd party -&gt; Sf system</td>\n", "      <td>BOOL</td>\n", "      <td>Holding Register</td>\n", "      <td>101.0</td>\n", "      <td>0.0</td>\n", "      <td>READ</td>\n", "      <td>Bit that toggles between 0 and 1 each second</td>\n", "      <td>HeartbeatRead</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>**************</td>\n", "      <td>Ship Plc</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Seafar Control Feedback</td>\n", "      <td>BOOL</td>\n", "      <td>Holding Register</td>\n", "      <td>101.0</td>\n", "      <td>1.0</td>\n", "      <td>READ</td>\n", "      <td>0 = Seafar does not request control, 1 = Seafa...</td>\n", "      <td>SystemFunctions</td>\n", "      <td>BridgeWarningIndicator</td>\n", "      <td>ControlFeedback</td>\n", "      <td>**************</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Seafar potential free contact feedback</td>\n", "      <td>BOOL</td>\n", "      <td>Holding Register</td>\n", "      <td>101.0</td>\n", "      <td>2.0</td>\n", "      <td>READ</td>\n", "      <td>0 = Seafar cannot take control, 1 = Seafar can...</td>\n", "      <td>SystemFunctions</td>\n", "      <td>BridgeWarningIndicator</td>\n", "      <td>ThirdPartySafetyContactFeedback</td>\n", "      <td>**************</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Local control request</td>\n", "      <td>BOOL</td>\n", "      <td>Holding Register</td>\n", "      <td>101.0</td>\n", "      <td>3.0</td>\n", "      <td>READ</td>\n", "      <td>Pulse when button is pressed on third party sy...</td>\n", "      <td>Reserved</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>**************</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>-</td>\n", "      <td>BOOL</td>\n", "      <td>Holding Register</td>\n", "      <td>101.0</td>\n", "      <td>4.0</td>\n", "      <td>READ</td>\n", "      <td>-</td>\n", "      <td>Reserved</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>**************</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>-</td>\n", "      <td>BOOL</td>\n", "      <td>Holding Register</td>\n", "      <td>101.0</td>\n", "      <td>5.0</td>\n", "      <td>READ</td>\n", "      <td>-</td>\n", "      <td>Reserved</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>**************</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>-</td>\n", "      <td>BOOL</td>\n", "      <td>Holding Register</td>\n", "      <td>101.0</td>\n", "      <td>6.0</td>\n", "      <td>READ</td>\n", "      <td>-</td>\n", "      <td>Reserved</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>**************</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>-</td>\n", "      <td>BOOL</td>\n", "      <td>Holding Register</td>\n", "      <td>101.0</td>\n", "      <td>7.0</td>\n", "      <td>READ</td>\n", "      <td>-</td>\n", "      <td>Reserved</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>**************</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>-</td>\n", "      <td>BOOL</td>\n", "      <td>Holding Register</td>\n", "      <td>101.0</td>\n", "      <td>8.0</td>\n", "      <td>READ</td>\n", "      <td>-</td>\n", "      <td>Reserved</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>**************</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>6</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                              Description ValueType      RegisterType  \\\n", "0                                Ship Plc                               \n", "1        Heartbeat 3rd party -> Sf system      BOOL  Holding Register   \n", "2                 Seafar Control Feedback      BOOL  Holding Register   \n", "3  Seafar potential free contact feedback      BOOL  Holding Register   \n", "4                   Local control request      BOOL  Holding Register   \n", "5                                       -      BOOL  Holding Register   \n", "6                                       -      BOOL  Holding Register   \n", "7                                       -      BOOL  Holding Register   \n", "8                                       -      BOOL  Holding Register   \n", "9                                       -      BOOL  Holding Register   \n", "\n", "  Register  Bit   R/W                                            Comment  \\\n", "0                                                                          \n", "1    101.0  0.0  READ       Bit that toggles between 0 and 1 each second   \n", "2    101.0  1.0  READ  0 = Seafar does not request control, 1 = Seafa...   \n", "3    101.0  2.0  READ  0 = Seafar cannot take control, 1 = Seafar can...   \n", "4    101.0  3.0  READ  Pulse when button is pressed on third party sy...   \n", "5    101.0  4.0  READ                                                  -   \n", "6    101.0  5.0  READ                                                  -   \n", "7    101.0  6.0  READ                                                  -   \n", "8    101.0  7.0  READ                                                  -   \n", "9    101.0  8.0  READ                                                  -   \n", "\n", "              Type          DomainDriverId              DomainDriverPurpose  \\\n", "0                                                                             \n", "1    HeartbeatRead                                                            \n", "2  SystemFunctions  BridgeWarningIndicator                  ControlFeedback   \n", "3  SystemFunctions  BridgeWarningIndicator  ThirdPartySafetyContactFeedback   \n", "4         Reserved                                                            \n", "5         Reserved                                                            \n", "6         Reserved                                                            \n", "7         Reserved                                                            \n", "8         Reserved                                                            \n", "9         Reserved                                                            \n", "\n", "               Ip ServerName AlarmMessage NotificationGroupId  \\\n", "0                                                               \n", "1  **************   Ship Plc                                    \n", "2  **************                                               \n", "3  **************                                               \n", "4  **************                                               \n", "5  **************                                               \n", "6  **************                                               \n", "7  **************                                               \n", "8  **************                                               \n", "9  **************                                               \n", "\n", "  NotificationTypeId EntityId  GroupId  \n", "0                                    0  \n", "1                                    0  \n", "2                                    0  \n", "3                                    0  \n", "4                                    1  \n", "5                                    2  \n", "6                                    3  \n", "7                                    4  \n", "8                                    5  \n", "9                                    6  "]}, "execution_count": 40, "metadata": {}, "output_type": "execute_result"}], "source": ["config_df.head(n=10)"]}, {"cell_type": "code", "execution_count": 41, "id": "702c40a0", "metadata": {}, "outputs": [{"data": {"text/plain": ["0        0\n", "1        0\n", "2        0\n", "3        0\n", "4        1\n", "      ... \n", "669    483\n", "670    484\n", "671    485\n", "672    486\n", "673    487\n", "Name: GroupId, Length: 674, dtype: int64"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["config_df['GroupId']"]}, {"cell_type": "code", "execution_count": 42, "id": "57f66bc7", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Description</th>\n", "      <th>ValueType</th>\n", "      <th>RegisterType</th>\n", "      <th>Register</th>\n", "      <th>Bit</th>\n", "      <th>R/W</th>\n", "      <th>Comment</th>\n", "      <th>Type</th>\n", "      <th>DomainDriverId</th>\n", "      <th>DomainDriverPurpose</th>\n", "      <th>Ip</th>\n", "      <th>ServerName</th>\n", "      <th>AlarmMessage</th>\n", "      <th>NotificationGroupId</th>\n", "      <th>NotificationTypeId</th>\n", "      <th>EntityId</th>\n", "      <th>GroupId</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [Description, ValueType, RegisterType, Register, Bit, R/W, Comment, Type, DomainDriverId, DomainDriverPurpose, Ip, ServerName, AlarmMessage, NotificationGroupId, NotificationTypeId, EntityId, GroupId]\n", "Index: []"]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "source": ["config_df[config_df['AlarmMessage'] == 'Fan in alarm']"]}, {"cell_type": "code", "execution_count": 43, "id": "4d108dc1", "metadata": {}, "outputs": [], "source": ["def generate_register_meta_data_config(register_type, register_id, register_value_type, bit_index):\n", "    if register_type == 'Holding Register' or register_type == 'Input Register':\n", "        if register_value_type.lower() == 'bool':\n", "            return {\n", "                \"RegisterMetaData\": {\n", "                    \"RegisterId\": int(register_id),\n", "                    \"RegisterType\": register_type.replace(' ', ''),\n", "                    \"RegisterValueType\": \"Boolean\"\n", "                },\n", "                \"RegisterParsingData\": {\n", "                    \"BitIndex\": int(bit_index)\n", "                }\n", "            }\n", "        elif register_value_type.lower() == 'int':\n", "            return {\n", "                'RegisterMetaData': {\n", "                    'RegisterId': int(register_id),\n", "                    'RegisterType': register_type.replace(' ', ''),\n", "                    'RegisterValueType': 'Integer16'\n", "                }\n", "            }\n", "        else:\n", "            raise Exception(f\"Unexpected value type received: {register_value_type} for register id: {register_id}\")\n", "            \n", "    elif register_type == 'Coil':\n", "        return {\n", "            \"RegisterMetaData\": {\n", "                \"RegisterId\": int(register_id),\n", "                \"RegisterType\": \"Coil\",\n", "                \"RegisterValueType\": \"Boolean\"\n", "            }\n", "        }\n", "    else:\n", "        raise Exception(\"Unexpected register type received: \" + register_type)"]}, {"cell_type": "code", "execution_count": null, "id": "a77893b9", "metadata": {}, "outputs": [], "source": ["excel_id_to_domain_driver_identification = {\n", "    \"Bow360Thruster1\": {\n", "        \"UnitId\": 1,\n", "        \"UnitLocation\": \"Bow\",\n", "        \"UnitType\": \"ThrusterRotational\"\n", "    },\n", "    \"Bow360Thruster2\": {\n", "        \"UnitId\": 2,\n", "        \"UnitLocation\": \"Bow\",\n", "        \"UnitType\": \"ThrusterRotational\"\n", "    },\n", "    \"SternThruster1\": {\n", "        \"UnitId\": 1,\n", "        \"UnitLocation\": \"Stern\",\n", "        \"UnitType\": \"ThrusterRotational\"\n", "    },\n", "    \"SternThruster2\": {\n", "        \"UnitId\": 2,\n", "        \"UnitLocation\": \"Stern\",\n", "        \"UnitType\": \"ThrusterRotational\"\n", "    },\n", "    \"SternEngine1\": {\n", "        \"UnitId\": 1,\n", "        \"UnitLocation\": \"Stern\",\n", "        \"UnitType\": \"Engine\"\n", "    },\n", "    \"SternEngine2\": {\n", "        \"UnitId\": 2,\n", "        \"UnitLocation\": \"Stern\",\n", "        \"UnitType\": \"Engine\"\n", "    },\n", "    \"BowPipeThruster1\": {\n", "        \"UnitId\": 1,\n", "        \"UnitLocation\": \"Bow\",\n", "        \"UnitType\": \"ThrusterTransversal\"\n", "    },\n", "    \"SternRudder1\": {\n", "        \"UnitId\": 1,\n", "        \"UnitLocation\": \"Stern\",\n", "        \"UnitType\": \"Rudder\"\n", "    },\n", "    \"UnspecifiedThrusterGeneric1\": {\n", "        \"UnitId\": 1,\n", "        \"UnitLocation\": \"Unspecified\",\n", "        \"UnitType\": \"ThrusterGeneric\"\n", "    },\n", "    \"UnspecifiedThrusterGeneric2\": {\n", "        \"UnitId\": 2,\n", "        \"UnitLocation\": \"Unspecified\",\n", "        \"UnitType\": \"ThrusterGeneric\"\n", "    },\n", "    \"UnspecifiedAutoPilot1\": {\n", "        \"UnitId\": 1,\n", "        \"UnitLocation\": \"Unspecified\",\n", "        \"UnitType\": \"AutoPilot\"\n", "    }\n", "}"]}, {"cell_type": "code", "execution_count": 45, "id": "8401d780", "metadata": {}, "outputs": [], "source": ["def domain_driver_id_to_string(domain_driver_id: dict):\n", "    return f'{domain_driver_id[\"UnitLocation\"]}{domain_driver_id[\"UnitType\"]}_{domain_driver_id[\"UnitId\"]}'"]}, {"cell_type": "code", "execution_count": null, "id": "2c6f1759", "metadata": {}, "outputs": [], "source": ["def prepare_config(row):\n", "    domain_driver_id_parts = row['DomainDriverId'].split('_')\n", "    domain_driver_id = domain_driver_id_parts[0]\n", "    joystick_parsing_id = ''\n", "    if len(domain_driver_id_parts) > 1:\n", "        joystick_parsing_id = domain_driver_id_parts[1] \n", "        \n", "    if row['Ip'] not in configs_per_ip:\n", "        configs_per_ip[row['Ip']] = {\n", "            \"DriverConfig\": {\n", "                \"DriverIdentification\": {\n", "                    \"name\": row['<PERSON><PERSON><PERSON>'],\n", "                    \"technicalComponentId\": \"\",\n", "                    \"driverId\": str(uuid.uuid4())\n", "                },\n", "                \"rebootIntervalOnFail_ms\": 1000,\n", "                \"heartbeatInterval_ms\": 1000,\n", "                \"SendingIntervalMs\": 250,\n", "                \"ReadingIntervalMs\": 250,\n", "                \"GeneralSystemDomainDrivers\": [],\n", "                \"PropulsionDomainDrivers\": [],\n", "                \"SystemFunctionsDomainDrivers\": [],\n", "                \"AlarmDomainDrivers\": []\n", "            },\n", "            \"ConnectionHandlerConfig\": {\n", "                \"protocol\": \"modbustcpclient\",\n", "                \"connectionAddress\": row['Ip'],\n", "                \"connectionAddressOption\": 502\n", "            },\n", "            \"Pipelines\": [\n", "                \"Automation\"\n", "            ]\n", "        }\n", "    if row['Ip'] not in system_configs_per_id_per_ip:\n", "        system_configs_per_id_per_ip[row['Ip']] = {}\n", "    system_configs_per_id = system_configs_per_id_per_ip[row['Ip']]\n", "    if row['Type'] == 'HeartbeatWrite':\n", "        heartbeat_config = generate_register_meta_data_config(row['RegisterType'], row['Register'], row['ValueType'], row['Bit'])\n", "        configs_per_ip[row['Ip']]['DriverConfig']['HeartbeatRegister'] = heartbeat_config\n", "        \n", "    elif row['Type'] == 'SystemFunction' or row['Type'] == 'SystemFunctions':\n", "        if domain_driver_id != '' and row['DomainDriverPurpose'] != '':\n", "            \n", "            if row['DomainDriverPurpose'] not in allowed_system_function_action_ids:\n", "                raise Exception(f'Received an unknown system function action id: {row[\"DomainDriverPurpose\"]}')\n", "\n", "            if domain_driver_id not in allowed_system_ids:\n", "                raise Exception(f'Received an unknown system id: {row[\"DomainDriverId\"]}')\n", "                \n", "                \n", "            bool_config = generate_register_meta_data_config(row['RegisterType'], row['Register'], row['ValueType'], row['Bit'])\n", "            bool_config['SystemFunctionsValuePurposes'] = row['DomainDriverPurpose']\n", "            \n", "            if row['DomainDriverPurpose'] == 'ToggleActionPulse':\n", "                bool_config['SystemFunctionsValuePurposes'] = 'ToggleAction'\n", "                system_functions_pulse_configs[domain_driver_id] = {\n", "                    'PulseTimeMs': 1000,\n", "                    'DisableStateMismatchCheck': False\n", "                }\n", "\n", "            if domain_driver_id in system_configs_per_id:\n", "                print(domain_driver_id)\n", "                print(row['GroupId'])\n", "                print(system_configs_per_id[domain_driver_id])\n", "                if row['GroupId'] < len(system_configs_per_id[domain_driver_id]):\n", "                    system_configs_per_id[domain_driver_id][row['GroupId']]['DomainSpecificConfig']['TrackedRegisters'].append(bool_config)\n", "                else:\n", "                    system_configs_per_id[domain_driver_id].append({\n", "                        \"DomainDriverIdentification\": {\n", "                            \"SystemFunctionId\": domain_driver_id,\n", "                            \"Description\": row['Description']\n", "                        },\n", "                        \"DomainSpecificConfig\": {\n", "                            \"TrackedRegisters\": [\n", "                                bool_config\n", "                            ]\n", "                        }\n", "                        })\n", "            else:\n", "                system_configs_per_id[domain_driver_id] = [{\n", "                    \"DomainDriverIdentification\": {\n", "                        \"SystemFunctionId\": domain_driver_id,\n", "                        \"Description\": row['Description']\n", "                    },\n", "                    \"DomainSpecificConfig\": {\n", "                        \"TrackedRegisters\": [\n", "                            bool_config\n", "                        ]\n", "                    }\n", "                }]\n", "            \n", "            \n", "    elif row['Type'] == 'RemoteControlSwitch':\n", "        remote_control_switch_config = generate_register_meta_data_config(row['RegisterType'], row['Register'], row['ValueType'], row['Bit'])\n", "        remote_control_switch_config['GeneralSystemValuePurpose'] = 'RunSwitchFeedback'\n", "        configs_per_ip[row['Ip']]['DriverConfig']['GeneralSystemDomainDrivers'].append(remote_control_switch_config)\n", "    elif row['Type'] == 'EmergencyStopFeedback':\n", "        remote_control_switch_config = generate_register_meta_data_config(row['RegisterType'], row['Register'], row['ValueType'], row['Bit'])\n", "        remote_control_switch_config['RegisterParsingData']['InvertBitValue'] = True\n", "        remote_control_switch_config['GeneralSystemValuePurpose'] = 'EmergencyStopFeedback'\n", "        configs_per_ip[row['Ip']]['DriverConfig']['GeneralSystemDomainDrivers'].append(remote_control_switch_config)\n", "    elif row['Type'] == 'Reserved':\n", "        pass\n", "    elif row['Type'] == 'HeartbeatRead':\n", "        heartbeat_read_config = generate_register_meta_data_config(row['RegisterType'], row['Register'], row['ValueType'], row['Bit'])\n", "        heartbeat_read_config['GeneralSystemValuePurpose'] = 'ThirdPartyHeartbeat'\n", "        configs_per_ip[row['Ip']]['DriverConfig']['GeneralSystemDomainDrivers'].append(heartbeat_read_config)\n", "    elif row['Type'] == 'Propulsion':\n", "        if row['Ip'] not in propulsion_configs_per_id_per_ip:\n", "            propulsion_configs_per_id_per_ip[row['Ip']] = {}\n", "            \n", "        propulsion_configs_per_id = propulsion_configs_per_id_per_ip[row['Ip']]\n", "        propulsion_config = {}\n", "        if domain_driver_id in propulsion_configs_per_id:\n", "            propulsion_config = propulsion_configs_per_id[domain_driver_id]\n", "        else:\n", "            if domain_driver_id not in excel_id_to_domain_driver_identification:\n", "                raise Exception(f'Received a propulsion misconfig for {row}')\n", "            propulsion_config = {\n", "                'DomainDriverIdentification': excel_id_to_domain_driver_identification[domain_driver_id],\n", "                'DomainSpecificConfig': {\n", "                    'ClutchValues': {\n", "                        \"DetentForwardValue\": 20,\n", "                        \"ClutchForwardValue\": 20,\n", "                        \"DetentBackwardsValue\": -20,\n", "                        \"ClutchBackwardsValue\": -20\n", "                    },\n", "                    \"TrackedRegisters\": []\n", "                }\n", "            }\n", "        if row['DomainDriverPurpose'] not in allowed_propulsion_action_ids:\n", "            raise Exception(f'Received an unknown propulsion action: {row[\"DomainDriverPurpose\"]}')\n", "        tracked_register = generate_register_meta_data_config(row['RegisterType'], row['Register'], row['ValueType'], row['Bit'])\n", "        \n", "        if joystick_parsing_id != '':\n", "            if joystick_parsing_id in allowed_joystick_parsing_ids:\n", "                joystick_parsing_data = {\n", "                    'JoystickValueProperty': joystick_parsing_id\n", "                }\n", "            else:\n", "                raise Exception(f'Received an unexpected joystick parsing id: {joystick_parsing_id}')\n", "            tracked_register['JoystickParsingData'] = joystick_parsing_data\n", "        if 'ExtraId' in row:\n", "            if row['ExtraId'] in allowed_extra_ids:\n", "                tracked_register['ArconPropulsionValuePurposeMetaData'] = {\n", "                                        \"ArconPropulsionValuePurposeType\": row['ExtraId']\n", "                                    }\n", "        propulsion_value_purpose = row['DomainDriverPurpose']\n", "        if propulsion_value_purpose == 'ToggleActionPulse':\n", "            propulsion_value_purpose = 'ActionTogglePropulsion'\n", "            propulsion_id_str = domain_driver_id_to_string(propulsion_config['DomainDriverIdentification'])\n", "            propulsion_pulse_configs[propulsion_id_str] = {\n", "                'PulseTimeMs': 3000,\n", "                'DisableStateMismatchCheck': False\n", "            }\n", "        elif propulsion_value_purpose == 'ActionTogglePropulsion':\n", "            #ensure that default we use a pulse\n", "            propulsion_id_str = domain_driver_id_to_string(propulsion_config['DomainDriverIdentification'])\n", "            propulsion_pulse_configs[propulsion_id_str] = {\n", "                'PulseTimeMs': 3000,\n", "                'DisableStateMismatchCheck': False\n", "            }\n", "        tracked_register['PropulsionValuePurpose'] = propulsion_value_purpose\n", "        if 'Thruster' in domain_driver_id and 'Direction' in propulsion_value_purpose:\n", "            tracked_register['RegisterScaleData'] = {\n", "                'ScalingType': 'ThrusterPercentageToMovementDegrees'\n", "            }\n", "        propulsion_config['DomainSpecificConfig']['TrackedRegisters'].append(tracked_register)\n", "        propulsion_configs_per_id[domain_driver_id] = propulsion_config\n", "        propulsion_configs_per_id_per_ip[row['Ip']] = propulsion_configs_per_id\n", "        configs_per_ip[row['Ip']]['DriverConfig']['PropulsionDomainDrivers'] = list(propulsion_configs_per_id.values())\n", "    elif row['Type'] == '':\n", "        pass\n", "    elif row['Type'] == 'Alarms':\n", "        if domain_driver_id not in allowed_alarm_ids:\n", "            raise Exception(f'Received an unknown alarm id: {row[\"DomainDriverId\"]}')\n", "\n", "        bool_config = generate_register_meta_data_config(row['RegisterType'], row['Register'], row['ValueType'], row['Bit'])\n", "        \n", "        alarm_domain_driver = {\n", "            'DomainDriverIdentification': {\n", "                'AlarmMessage': row['AlarmMessage'],\n", "                'NotificationGroupId': row['NotificationGroupId'],\n", "                'EntityId': row['EntityId'],\n", "                'NotificationTypeId': row['NotificationTypeId']\n", "            },\n", "            'DomainSpecificConfig': bool_config\n", "        }\n", "        \n", "        if alarm_domain_driver['DomainDriverIdentification']['NotificationTypeId'] == 'Warning':\n", "            alarm_domain_driver['DomainDriverIdentification']['WarningId'] = domain_driver_id\n", "        else:\n", "            alarm_domain_driver['DomainDriverIdentification']['AlarmId'] = domain_driver_id\n", "        for k,v in alarm_domain_driver['DomainDriverIdentification'].items():\n", "            if k != 'EntityId' and v.strip() == '':\n", "                raise Exception(f'Received a misconfig for {k}. {alarm_domain_driver}')\n", "        configs_per_ip[row['Ip']]['DriverConfig']['AlarmDomainDrivers'].append(alarm_domain_driver)\n", "\n", "    else:\n", "        raise Exception(f'Received invalid type: {row[\"Type\"]}')\n", "        \n", "        "]}, {"cell_type": "code", "execution_count": 47, "id": "acfc4a65", "metadata": {}, "outputs": [], "source": ["safety_system_io_config = {\n", "            \"DriverConfig\": {\n", "                \"DriverIdentification\": {\n", "                    \"name\": \"Operator panel - Moxa\",\n", "                    \"technicalComponentId\": \"\",\n", "                    \"driverId\": \"626b9a03-f1f1-4cdc-a6f8-32785617bf3b\"\n", "                },\n", "                \"rebootIntervalOnFail_ms\": 1000,\n", "                \"heartbeatInterval_ms\": 1000,\n", "                \"SendingIntervalMs\": 250,\n", "                \"ReadingIntervalMs\": 250,\n", "                \"GeneralSystemDomainDrivers\": [\n", "                    {\n", "                        \"RegisterMetaData\": {\n", "                            \"RegisterId\": 32,\n", "                            \"RegisterType\": \"HoldingRegister\",\n", "                            \"RegisterValueType\": \"Boolean\"\n", "                        },\n", "                        \"RegisterParsingData\": {\n", "                            \"BitIndex\": 0\n", "                        },\n", "                        \"GeneralSystemValuePurpose\": \"SafetyBuzzerControl\"\n", "                    },\n", "                    {\n", "                        \"RegisterMetaData\": {\n", "                            \"RegisterId\": 48,\n", "                            \"RegisterType\": \"InputRegister\",\n", "                            \"RegisterValueType\": \"Boolean\"\n", "                        },\n", "                        \"RegisterParsingData\": {\n", "                            \"BitIndex\": 0\n", "                        },\n", "                        \"GeneralSystemValuePurpose\": \"LocalControlRequest\"\n", "                    },\n", "                    {\n", "                        \"RegisterMetaData\": {\n", "                            \"RegisterId\": 32,\n", "                            \"RegisterType\": \"HoldingRegister\",\n", "                            \"RegisterValueType\": \"Boolean\"\n", "                        },\n", "                        \"RegisterParsingData\": {\n", "                            \"BitIndex\": 2\n", "                        },\n", "                        \"GeneralSystemValuePurpose\": \"RunStateLedControl\"\n", "                    },\n", "                    {\n", "                        \"RegisterMetaData\": {\n", "                            \"RegisterId\": 32,\n", "                            \"RegisterType\": \"HoldingRegister\",\n", "                            \"RegisterValueType\": \"Boolean\"\n", "                        },\n", "                        \"RegisterParsingData\": {\n", "                            \"BitIndex\": 1\n", "                        },\n", "                        \"GeneralSystemValuePurpose\": \"SafetyLed\"\n", "                    },\n", "                    {\n", "                        \"RegisterMetaData\": {\n", "                            \"RegisterId\": 32,\n", "                            \"RegisterType\": \"HoldingRegister\",\n", "                            \"RegisterValueType\": \"Boolean\"\n", "                        },\n", "                        \"RegisterParsingData\": {\n", "                            \"BitIndex\": 3\n", "                        },\n", "                        \"GeneralSystemValuePurpose\": \"ResetSafetySystem\"\n", "                    }\n", "                ],\n", "                \"PropulsionDomainDrivers\": [],\n", "                \"SystemFunctionsDomainDrivers\": [],\n", "                \"AlarmDomainDrivers\": []\n", "            },\n", "            \"ConnectionHandlerConfig\": {\n", "                \"protocol\": \"modbustcpclient\",\n", "                \"connectionAddress\": \"*************\",\n", "                \"connectionAddressOption\": 502\n", "            },\n", "            \"Pipelines\": [\n", "                \"Automation\"\n", "            ]\n", "        }"]}, {"cell_type": "code", "execution_count": 48, "id": "87b61e94", "metadata": {}, "outputs": [], "source": ["cabinet_readout_io_moxa = {\n", "    \"DriverConfig\": {\n", "        \"DriverIdentification\": {\n", "            \"name\": \"Cabinet readout IO - Moxa\",\n", "            \"technicalComponentId\": \"\",\n", "            \"driverId\": \"dd5bd1e3-e4fc-4098-9b25-6aeab8fa6e89\"\n", "        },\n", "        \"rebootIntervalOnFail_ms\": 1000,\n", "        \"heartbeatInterval_ms\": 1000,\n", "        \"SendingIntervalMs\": 250,\n", "        \"ReadingIntervalMs\": 250,\n", "        \"GeneralSystemDomainDrivers\": [],\n", "        \"PropulsionDomainDrivers\": [],\n", "        \"SystemFunctionsDomainDrivers\": [],\n", "        \"AlarmDomainDrivers\": [\n", "            {\n", "                \"DomainDriverIdentification\": {\n", "                    \"AlarmMessage\": \"Stabilizer channel 1 not ok\",\n", "                    \"NotificationGroupId\": \"PowerGeneral\",\n", "                    \"EntityId\": \"Stabilizer 1\",\n", "                    \"NotificationTypeId\": \"Warning\",\n", "                    \"WarningId\": \"MiPowerGeneral\"\n", "                },\n", "                \"DomainSpecificConfig\": {\n", "                    \"RegisterMetaData\": {\n", "                        \"RegisterId\": 48,\n", "                        \"RegisterType\": \"InputRegister\",\n", "                        \"RegisterValueType\": \"Boolean\"\n", "                    },\n", "                    \"RegisterParsingData\": {\n", "                        \"BitIndex\": 0,\n", "                        \"InvertBitValue\": True\n", "                    }\n", "                }\n", "            },\n", "            {\n", "                \"DomainDriverIdentification\": {\n", "                    \"AlarmMessage\": \"Stabilizer channel 2 not ok\",\n", "                    \"NotificationGroupId\": \"PowerGeneral\",\n", "                    \"EntityId\": \"Stabilizer 2\",\n", "                    \"NotificationTypeId\": \"Warning\",\n", "                    \"WarningId\": \"MiPowerGeneral\"\n", "                },\n", "                \"DomainSpecificConfig\": {\n", "                    \"RegisterMetaData\": {\n", "                        \"RegisterId\": 48,\n", "                        \"RegisterType\": \"InputRegister\",\n", "                        \"RegisterValueType\": \"Boolean\"\n", "                    },\n", "                    \"RegisterParsingData\": {\n", "                        \"BitIndex\": 1,\n", "                        \"InvertBitValue\": True\n", "                    }\n", "                }\n", "            },\n", "            {\n", "                \"DomainDriverIdentification\": {\n", "                    \"AlarmMessage\": \"12V Distribution not ok\",\n", "                    \"NotificationGroupId\": \"PowerGeneral\",\n", "                    \"EntityId\": \"12V Core cabinet\",\n", "                    \"NotificationTypeId\": \"Warning\",\n", "                    \"WarningId\": \"MiPowerGeneral\"\n", "                },\n", "                \"DomainSpecificConfig\": {\n", "                    \"RegisterMetaData\": {\n", "                        \"RegisterId\": 48,\n", "                        \"RegisterType\": \"InputRegister\",\n", "                        \"RegisterValueType\": \"Boolean\"\n", "                    },\n", "                    \"RegisterParsingData\": {\n", "                        \"BitIndex\": 3,\n", "                        \"InvertBitValue\": True\n", "                    }\n", "                }\n", "            },\n", "            {\n", "                \"DomainDriverIdentification\": {\n", "                    \"AlarmMessage\": \"Cooling 5 degrees above set temperature of 35 degrees\",\n", "                    \"NotificationGroupId\": \"ControlSystemGeneral\",\n", "                    \"EntityId\": \"Cooling Core cabinet\",\n", "                    \"NotificationTypeId\": \"Warning\",\n", "                    \"WarningId\": \"MiTemperatureExceededThresholds\"\n", "                },\n", "                \"DomainSpecificConfig\": {\n", "                    \"RegisterMetaData\": {\n", "                        \"RegisterId\": 48,\n", "                        \"RegisterType\": \"InputRegister\",\n", "                        \"RegisterValueType\": \"Boolean\"\n", "                    },\n", "                    \"RegisterParsingData\": {\n", "                        \"BitIndex\": 4,\n", "                        \"InvertBitValue\": True\n", "                    }\n", "                }\n", "            },\n", "            {\n", "                \"DomainDriverIdentification\": {\n", "                    \"AlarmMessage\": \"Core cabinets door opened\",\n", "                    \"NotificationGroupId\": \"ControlSystemGeneral\",\n", "                    \"EntityId\": \"Door Core cabinet\",\n", "                    \"NotificationTypeId\": \"Warning\",\n", "                    \"WarningId\": \"MiHatchOpen\"\n", "                },\n", "                \"DomainSpecificConfig\": {\n", "                    \"RegisterMetaData\": {\n", "                        \"RegisterId\": 48,\n", "                        \"RegisterType\": \"InputRegister\",\n", "                        \"RegisterValueType\": \"Boolean\"\n", "                    },\n", "                    \"RegisterParsingData\": {\n", "                        \"BitIndex\": 5,\n", "                        \"InvertBitValue\": True\n", "                    }\n", "                }\n", "            }\n", "        ]\n", "    },\n", "    \"ConnectionHandlerConfig\": {\n", "        \"protocol\": \"modbustcpclient\",\n", "        \"connectionAddress\": \"*************\",\n", "        \"connectionAddressOption\": 502\n", "    },\n", "    \"Pipelines\": [\n", "        \"Automation\"\n", "    ]\n", "}"]}, {"cell_type": "code", "execution_count": 49, "id": "4c03fc43", "metadata": {}, "outputs": [], "source": ["safety_system_pilz_config = {\n", "            \"DriverConfig\": {\n", "                \"DriverIdentification\": {\n", "                    \"name\": \"Safety system - Pilz\",\n", "                    \"technicalComponentId\": \"\",\n", "                    \"driverId\": \"e46936ab-d2e4-42eb-9333-85b0e4f133e4\"\n", "                },\n", "                \"rebootIntervalOnFail_ms\": 1000,\n", "                \"heartbeatInterval_ms\": 1000,\n", "                \"SendingIntervalMs\": 500,\n", "                \"ReadingIntervalMs\": 250,\n", "                \"GeneralSystemDomainDrivers\": [\n", "                    {\n", "                        \"RegisterMetaData\": {\n", "                            \"RegisterId\": 1060,\n", "                            \"RegisterType\": \"HoldingRegister\",\n", "                            \"RegisterValueType\": \"Boolean\"\n", "                        },\n", "                        \"RegisterParsingData\": {\n", "                            \"BitIndex\": 2\n", "                        },\n", "                        \"GeneralSystemValuePurpose\": \"RunSwitchFeedback\"\n", "                    },\n", "                    {\n", "                        \"RegisterMetaData\": {\n", "                            \"RegisterId\": 1060,\n", "                            \"RegisterType\": \"HoldingRegister\",\n", "                            \"RegisterValueType\": \"Boolean\"\n", "                        },\n", "                        \"RegisterParsingData\": {\n", "                            \"BitIndex\": 0,\n", "                            \"InvertBitValue\": True\n", "                        },\n", "                        \"GeneralSystemValuePurpose\": \"EmergencyStopFeedback\"\n", "                    },\n", "                    {\n", "                        \"RegisterMetaData\": {\n", "                            \"RegisterId\": 1062,\n", "                            \"RegisterType\": \"HoldingRegister\",\n", "                            \"RegisterValueType\": \"Boolean\"\n", "                        },\n", "                        \"RegisterParsingData\": {\n", "                            \"BitIndex\": 2\n", "                        },\n", "                        \"GeneralSystemValuePurpose\": \"SafetyOutputFeedback\"\n", "                     }\n", "                ],\n", "                \"PropulsionDomainDrivers\": [],\n", "                \"SystemFunctionsDomainDrivers\": [],\n", "                \"AlarmDomainDrivers\": []\n", "            },\n", "            \"ConnectionHandlerConfig\": {\n", "                \"protocol\": \"modbustcpclient\",\n", "                \"connectionAddress\": \"*************\",\n", "                \"connectionAddressOption\": 502\n", "            },\n", "            \"Pipelines\": [\n", "                \"Automation\"\n", "            ]\n", "        }"]}, {"cell_type": "code", "execution_count": 50, "id": "f4efacb8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["BridgeWarningIndicator\n", "0\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'BridgeWarningIndicator', 'Description': 'Seafar Control Feedback'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 101, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'ControlFeedback'}]}}]\n", "CallSystemRoom1\n", "1\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom1', 'Description': 'Call / Alarm Bow Engineroom'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 102, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 10}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}]\n", "CallSystemRoom1\n", "2\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom1', 'Description': 'Call / Alarm Bow Engineroom'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 102, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 10}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom1', 'Description': 'Call / Alarm Stern Engineroom'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 102, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 11}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}]\n", "CallSystemRoom2\n", "1\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': 'Spare alarm 81'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 108, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'KeepAlivePolling'}]}}]\n", "CallSystemRoom2\n", "2\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': 'Spare alarm 81'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 108, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'KeepAlivePolling'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': 'Spare alarm 113'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 110, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'KeepAlivePolling'}]}}]\n", "LightAdnrSign1\n", "1\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'LightAdnrSign1', 'Description': 'Blue ADN Stern 1'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 113, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 7}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}]\n", "LightAdnrSign2\n", "1\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'LightAdnrSign2', 'Description': 'Blue ADN Stern 2'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 113, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 8}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}]\n", "LightTowing\n", "1\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'LightTowing', 'Description': 'Top light Bow 1'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 114, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}]\n", "LightSides\n", "1\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'LightSides', 'Description': 'Side light PS'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 114, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 3}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}]\n", "LightAnchor\n", "1\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'LightAnchor', 'Description': 'Anchor light Stern PS'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 114, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 8}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}]\n", "LightAnchor\n", "2\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'LightAnchor', 'Description': 'Anchor light Stern PS'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 114, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 8}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'LightAnchor', 'Description': 'Anchor light Stern SB'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 114, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 9}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}]\n", "LightAnchor\n", "3\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'LightAnchor', 'Description': 'Anchor light Stern PS'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 114, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 8}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'LightAnchor', 'Description': 'Anchor light Stern SB'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 114, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 9}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'LightAnchor', 'Description': 'Anchor light Bow PS'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 114, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 10}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}]\n", "CallSystemRoom2\n", "3\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': 'Spare alarm 81'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 108, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'KeepAlivePolling'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': 'Spare alarm 113'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 110, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'KeepAlivePolling'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': 'Spare alarm 146'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 112, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'KeepAlivePolling'}]}}]\n", "UpMastPole1\n", "0\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'UpMastPole1', 'Description': 'Mast Bow Up'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 116, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'MoveUpFeedback'}]}}]\n", "UpMastPole2\n", "0\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'UpMastPole2', 'Description': 'Mast <PERSON>'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 116, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 4}, 'SystemFunctionsValuePurposes': 'MoveUpFeedback'}]}}]\n", "UpMastPole3\n", "0\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'UpMastPole3', 'Description': 'Spoiler Mast Stern Up'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 116, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 6}, 'SystemFunctionsValuePurposes': 'MoveUpFeedback'}]}}]\n", "CallSystemRoom2\n", "4\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': 'Spare alarm 81'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 108, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'KeepAlivePolling'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': 'Spare alarm 113'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 110, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'KeepAlivePolling'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': 'Spare alarm 146'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 112, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'KeepAlivePolling'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': '-'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 115, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'KeepAlivePolling'}]}}]\n", "DeckLight1\n", "1\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Poller Fore Portside'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 118, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}]\n", "DeckLight1\n", "2\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Poller Fore Portside'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 118, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Poller Fore Starboard'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 118, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}]\n", "DeckLight1\n", "3\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Poller Fore Portside'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 118, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Poller Fore Starboard'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 118, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Mast Fore Portside'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 118, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 2}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}]\n", "DeckLight1\n", "4\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Poller Fore Portside'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 118, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Poller Fore Starboard'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 118, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Mast Fore Portside'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 118, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 2}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Mast Fore Starboard'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 118, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 3}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}]\n", "DeckLight1\n", "5\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Poller Fore Portside'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 118, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Poller Fore Starboard'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 118, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Mast Fore Portside'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 118, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 2}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Mast Fore Starboard'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 118, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 3}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Spoiler Aft Deck Portside'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}]\n", "DeckLight1\n", "6\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Poller Fore Portside'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 118, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Poller Fore Starboard'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 118, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Mast Fore Portside'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 118, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 2}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Mast Fore Starboard'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 118, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 3}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Spoiler Aft Deck Portside'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Spoiler Aft Deck Starboard'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}]\n", "DeckLight1\n", "7\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Poller Fore Portside'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 118, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Poller Fore Starboard'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 118, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Mast Fore Portside'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 118, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 2}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Mast Fore Starboard'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 118, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 3}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Spoiler Aft Deck Portside'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Spoiler Aft Deck Starboard'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Boardunit Portside'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 2}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}]\n", "DeckLight1\n", "8\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Poller Fore Portside'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 118, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Poller Fore Starboard'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 118, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Mast Fore Portside'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 118, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 2}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Mast Fore Starboard'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 118, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 3}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Spoiler Aft Deck Portside'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Spoiler Aft Deck Starboard'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Boardunit Portside'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 2}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Boardunit Startboard'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 3}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}]\n", "DeckLight1\n", "9\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Poller Fore Portside'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 118, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Poller Fore Starboard'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 118, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Mast Fore Portside'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 118, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 2}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Mast Fore Starboard'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 118, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 3}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Spoiler Aft Deck Portside'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Spoiler Aft Deck Starboard'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Boardunit Portside'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 2}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Boardunit Startboard'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 3}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlighting Wheelhouse Roof Front'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 4}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}]\n", "SearchLight1\n", "0\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'SearchLight1', 'Description': 'Search light Bow ON/OFF'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 120, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}]\n", "SearchLight1\n", "0\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'SearchLight1', 'Description': 'Search light Bow ON/OFF'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 120, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 120, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'MoveLeftFeedback'}]}}]\n", "SearchLight1\n", "0\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'SearchLight1', 'Description': 'Search light Bow ON/OFF'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 120, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 120, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'MoveLeftFeedback'}, {'RegisterMetaData': {'RegisterId': 120, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 2}, 'SystemFunctionsValuePurposes': 'MoveRightFeedback'}]}}]\n", "SearchLight1\n", "0\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'SearchLight1', 'Description': 'Search light Bow ON/OFF'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 120, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 120, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'MoveLeftFeedback'}, {'RegisterMetaData': {'RegisterId': 120, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 2}, 'SystemFunctionsValuePurposes': 'MoveRightFeedback'}, {'RegisterMetaData': {'RegisterId': 120, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 3}, 'SystemFunctionsValuePurposes': 'MoveUpFeedback'}]}}]\n", "DownSpudPoleStern1\n", "0\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'DownSpudPoleStern1', 'Description': 'Search light Stern PS ON/OFF'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 120, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 8}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}]\n", "DownSpudPoleStern1\n", "0\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'DownSpudPoleStern1', 'Description': 'Search light Stern PS ON/OFF'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 120, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 8}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 120, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 9}, 'SystemFunctionsValuePurposes': 'MoveLeftFeedback'}]}}]\n", "DownSpudPoleStern1\n", "0\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'DownSpudPoleStern1', 'Description': 'Search light Stern PS ON/OFF'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 120, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 8}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 120, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 9}, 'SystemFunctionsValuePurposes': 'MoveLeftFeedback'}, {'RegisterMetaData': {'RegisterId': 120, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 10}, 'SystemFunctionsValuePurposes': 'MoveRightFeedback'}]}}]\n", "DownSpudPoleStern1\n", "0\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'DownSpudPoleStern1', 'Description': 'Search light Stern PS ON/OFF'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 120, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 8}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 120, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 9}, 'SystemFunctionsValuePurposes': 'MoveLeftFeedback'}, {'RegisterMetaData': {'RegisterId': 120, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 10}, 'SystemFunctionsValuePurposes': 'MoveRightFeedback'}, {'RegisterMetaData': {'RegisterId': 120, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 11}, 'SystemFunctionsValuePurposes': 'MoveUpFeedback'}]}}]\n", "DownSpudPoleBow1\n", "0\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'DownSpudPoleBow1', 'Description': 'Search light Stern SB ON/OFF'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 121, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}]\n", "DownSpudPoleBow1\n", "0\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'DownSpudPoleBow1', 'Description': 'Search light Stern SB ON/OFF'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 121, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 121, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'MoveLeftFeedback'}]}}]\n", "DownSpudPoleBow1\n", "0\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'DownSpudPoleBow1', 'Description': 'Search light Stern SB ON/OFF'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 121, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 121, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'MoveLeftFeedback'}, {'RegisterMetaData': {'RegisterId': 121, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 2}, 'SystemFunctionsValuePurposes': 'MoveRightFeedback'}]}}]\n", "DownSpudPoleBow1\n", "0\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'DownSpudPoleBow1', 'Description': 'Search light Stern SB ON/OFF'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 121, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 121, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'MoveLeftFeedback'}, {'RegisterMetaData': {'RegisterId': 121, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 2}, 'SystemFunctionsValuePurposes': 'MoveRightFeedback'}, {'RegisterMetaData': {'RegisterId': 121, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 3}, 'SystemFunctionsValuePurposes': 'MoveUpFeedback'}]}}]\n", "BridgeWarningIndicator\n", "0\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'BridgeWarningIndicator', 'Description': 'Seafar Control Feedback'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 101, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'ControlFeedback'}, {'RegisterMetaData': {'RegisterId': 101, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 2}, 'SystemFunctionsValuePurposes': 'ThirdPartySafetyContactFeedback'}]}}]\n", "AlarmGeneral\n", "0\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'AlarmGeneral', 'Description': 'General Alarm'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 102, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}]\n", "CallSystemRoom1\n", "0\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom1', 'Description': 'Call / Alarm Bow Engineroom'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 102, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 10}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom1', 'Description': 'Call / Alarm Stern Engineroom'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 102, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 11}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom1', 'Description': 'Call / Alarm Stern Accommodation'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 102, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 14}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}]\n", "CallSystemRoom1\n", "1\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom1', 'Description': 'Call / Alarm Bow Engineroom'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 102, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 10}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 202, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 10}, 'SystemFunctionsValuePurposes': 'ToggleAction'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom1', 'Description': 'Call / Alarm Stern Engineroom'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 102, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 11}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom1', 'Description': 'Call / Alarm Stern Accommodation'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 102, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 14}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}]\n", "CallSystemRoom1\n", "2\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom1', 'Description': 'Call / Alarm Bow Engineroom'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 102, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 10}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 202, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 10}, 'SystemFunctionsValuePurposes': 'ToggleAction'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom1', 'Description': 'Call / Alarm Stern Engineroom'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 102, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 11}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 202, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 11}, 'SystemFunctionsValuePurposes': 'ToggleAction'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom1', 'Description': 'Call / Alarm Stern Accommodation'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 102, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 14}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}]\n", "CallSystemRoom2\n", "0\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': 'Spare alarm 81'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 108, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'KeepAlivePolling'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': 'Spare alarm 113'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 110, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'KeepAlivePolling'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': 'Spare alarm 146'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 112, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'KeepAlivePolling'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': '-'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 115, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'KeepAlivePolling'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': 'Generator 1 Running'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 117, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'KeepAlivePolling'}]}}]\n", "CallSystemRoom2\n", "1\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': 'Spare alarm 81'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 108, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'KeepAlivePolling'}, {'RegisterMetaData': {'RegisterId': 203, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': 'Spare alarm 113'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 110, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'KeepAlivePolling'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': 'Spare alarm 146'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 112, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'KeepAlivePolling'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': '-'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 115, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'KeepAlivePolling'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': 'Generator 1 Running'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 117, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'KeepAlivePolling'}]}}]\n", "CallSystemRoom2\n", "2\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': 'Spare alarm 81'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 108, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'KeepAlivePolling'}, {'RegisterMetaData': {'RegisterId': 203, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': 'Spare alarm 113'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 110, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'KeepAlivePolling'}, {'RegisterMetaData': {'RegisterId': 204, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': 'Spare alarm 146'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 112, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'KeepAlivePolling'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': '-'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 115, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'KeepAlivePolling'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': 'Generator 1 Running'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 117, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'KeepAlivePolling'}]}}]\n", "CallSystemRoom2\n", "3\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': 'Spare alarm 81'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 108, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'KeepAlivePolling'}, {'RegisterMetaData': {'RegisterId': 203, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': 'Spare alarm 113'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 110, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'KeepAlivePolling'}, {'RegisterMetaData': {'RegisterId': 204, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': 'Spare alarm 146'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 112, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'KeepAlivePolling'}, {'RegisterMetaData': {'RegisterId': 205, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': '-'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 115, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'KeepAlivePolling'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': 'Generator 1 Running'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 117, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'KeepAlivePolling'}]}}]\n", "CallSystemRoom2\n", "4\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': 'Spare alarm 81'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 108, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'KeepAlivePolling'}, {'RegisterMetaData': {'RegisterId': 203, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': 'Spare alarm 113'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 110, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'KeepAlivePolling'}, {'RegisterMetaData': {'RegisterId': 204, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': 'Spare alarm 146'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 112, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'KeepAlivePolling'}, {'RegisterMetaData': {'RegisterId': 205, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': '-'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 115, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'KeepAlivePolling'}, {'RegisterMetaData': {'RegisterId': 206, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': 'Generator 1 Running'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 117, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'KeepAlivePolling'}]}}]\n", "CallSystemRoom2\n", "5\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': 'Spare alarm 81'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 108, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'KeepAlivePolling'}, {'RegisterMetaData': {'RegisterId': 203, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': 'Spare alarm 113'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 110, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'KeepAlivePolling'}, {'RegisterMetaData': {'RegisterId': 204, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': 'Spare alarm 146'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 112, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'KeepAlivePolling'}, {'RegisterMetaData': {'RegisterId': 205, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': '-'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 115, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'KeepAlivePolling'}, {'RegisterMetaData': {'RegisterId': 206, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': 'Generator 1 Running'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 117, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'KeepAlivePolling'}, {'RegisterMetaData': {'RegisterId': 207, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}}]\n", "CallSystemRoom2\n", "6\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': 'Spare alarm 81'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 108, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'KeepAlivePolling'}, {'RegisterMetaData': {'RegisterId': 203, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': 'Spare alarm 113'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 110, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'KeepAlivePolling'}, {'RegisterMetaData': {'RegisterId': 204, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': 'Spare alarm 146'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 112, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'KeepAlivePolling'}, {'RegisterMetaData': {'RegisterId': 205, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': '-'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 115, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'KeepAlivePolling'}, {'RegisterMetaData': {'RegisterId': 206, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': 'Generator 1 Running'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 117, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'KeepAlivePolling'}, {'RegisterMetaData': {'RegisterId': 207, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': '-'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 208, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}}]\n", "CallSystemRoom2\n", "7\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': 'Spare alarm 81'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 108, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'KeepAlivePolling'}, {'RegisterMetaData': {'RegisterId': 203, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': 'Spare alarm 113'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 110, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'KeepAlivePolling'}, {'RegisterMetaData': {'RegisterId': 204, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': 'Spare alarm 146'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 112, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'KeepAlivePolling'}, {'RegisterMetaData': {'RegisterId': 205, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': '-'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 115, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'KeepAlivePolling'}, {'RegisterMetaData': {'RegisterId': 206, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': 'Generator 1 Running'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 117, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'KeepAlivePolling'}, {'RegisterMetaData': {'RegisterId': 207, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': '-'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 208, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': '-'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 209, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}}]\n", "CallSystemRoom2\n", "8\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': 'Spare alarm 81'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 108, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'KeepAlivePolling'}, {'RegisterMetaData': {'RegisterId': 203, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': 'Spare alarm 113'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 110, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'KeepAlivePolling'}, {'RegisterMetaData': {'RegisterId': 204, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': 'Spare alarm 146'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 112, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'KeepAlivePolling'}, {'RegisterMetaData': {'RegisterId': 205, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': '-'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 115, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'KeepAlivePolling'}, {'RegisterMetaData': {'RegisterId': 206, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': 'Generator 1 Running'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 117, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'KeepAlivePolling'}, {'RegisterMetaData': {'RegisterId': 207, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': '-'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 208, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': '-'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 209, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': '-'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 210, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}}]\n", "CallSystemRoom2\n", "9\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': 'Spare alarm 81'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 108, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'KeepAlivePolling'}, {'RegisterMetaData': {'RegisterId': 203, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': 'Spare alarm 113'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 110, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'KeepAlivePolling'}, {'RegisterMetaData': {'RegisterId': 204, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': 'Spare alarm 146'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 112, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'KeepAlivePolling'}, {'RegisterMetaData': {'RegisterId': 205, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': '-'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 115, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'KeepAlivePolling'}, {'RegisterMetaData': {'RegisterId': 206, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': 'Generator 1 Running'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 117, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'KeepAlivePolling'}, {'RegisterMetaData': {'RegisterId': 207, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': '-'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 208, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': '-'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 209, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': '-'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 210, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': '-'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 211, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}}]\n", "Horn\n", "0\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'Horn', 'Description': 'Vessel Horn'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 113, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}]\n", "LightBlueSign\n", "0\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'LightBlueSign', 'Description': 'Blue Sign (board)'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 113, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 3}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}]\n", "StrobeLights\n", "0\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'StrobeLights', 'Description': 'Orange Strobe Light'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 113, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 6}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}]\n", "LightAdnrSign1\n", "0\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'LightAdnrSign1', 'Description': 'Blue ADN Stern 1'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 113, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 7}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'LightAdnrSign1', 'Description': 'Blue ADN Bow 1'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 113, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 10}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}]\n", "LightAdnrSign2\n", "0\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'LightAdnrSign2', 'Description': 'Blue ADN Stern 2'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 113, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 8}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'LightAdnrSign2', 'Description': 'Blue ADN Bow 2'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 113, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 11}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}]\n", "LightAdnrSign1\n", "1\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'LightAdnrSign1', 'Description': 'Blue ADN Stern 1'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 113, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 7}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 213, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 7}, 'SystemFunctionsValuePurposes': 'ToggleAction'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'LightAdnrSign1', 'Description': 'Blue ADN Bow 1'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 113, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 10}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}]\n", "LightAdnrSign2\n", "1\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'LightAdnrSign2', 'Description': 'Blue ADN Stern 2'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 113, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 8}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 213, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 8}, 'SystemFunctionsValuePurposes': 'ToggleAction'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'LightAdnrSign2', 'Description': 'Blue ADN Bow 2'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 113, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 11}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}]\n", "LightTowing\n", "0\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'LightTowing', 'Description': 'Top light Bow 1'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 114, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'LightTowing', 'Description': 'Top light Bow 2'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 114, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}]\n", "LightTowing\n", "1\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'LightTowing', 'Description': 'Top light Bow 1'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 114, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 214, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ToggleAction'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'LightTowing', 'Description': 'Top light Bow 2'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 114, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}]\n", "LightSides\n", "0\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'LightSides', 'Description': 'Side light PS'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 114, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 3}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'LightSides', 'Description': 'Side light SB'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 114, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 4}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}]\n", "LightSides\n", "1\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'LightSides', 'Description': 'Side light PS'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 114, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 3}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 214, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 3}, 'SystemFunctionsValuePurposes': 'ToggleAction'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'LightSides', 'Description': 'Side light SB'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 114, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 4}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}]\n", "LightStern\n", "0\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'LightStern', 'Description': 'Stern light 1'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 114, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 5}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}]\n", "LightAnchor\n", "0\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'LightAnchor', 'Description': 'Anchor light Stern PS'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 114, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 8}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'LightAnchor', 'Description': 'Anchor light Stern SB'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 114, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 9}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'LightAnchor', 'Description': 'Anchor light Bow PS'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 114, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 10}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'LightAnchor', 'Description': 'Anchor light Bow SB'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 114, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 11}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}]\n", "LightAnchor\n", "1\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'LightAnchor', 'Description': 'Anchor light Stern PS'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 114, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 8}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 214, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 8}, 'SystemFunctionsValuePurposes': 'ToggleAction'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'LightAnchor', 'Description': 'Anchor light Stern SB'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 114, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 9}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'LightAnchor', 'Description': 'Anchor light Bow PS'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 114, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 10}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'LightAnchor', 'Description': 'Anchor light Bow SB'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 114, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 11}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}]\n", "LightAnchor\n", "2\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'LightAnchor', 'Description': 'Anchor light Stern PS'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 114, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 8}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 214, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 8}, 'SystemFunctionsValuePurposes': 'ToggleAction'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'LightAnchor', 'Description': 'Anchor light Stern SB'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 114, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 9}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 214, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 9}, 'SystemFunctionsValuePurposes': 'ToggleAction'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'LightAnchor', 'Description': 'Anchor light Bow PS'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 114, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 10}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'LightAnchor', 'Description': 'Anchor light Bow SB'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 114, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 11}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}]\n", "LightAnchor\n", "3\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'LightAnchor', 'Description': 'Anchor light Stern PS'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 114, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 8}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 214, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 8}, 'SystemFunctionsValuePurposes': 'ToggleAction'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'LightAnchor', 'Description': 'Anchor light Stern SB'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 114, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 9}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 214, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 9}, 'SystemFunctionsValuePurposes': 'ToggleAction'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'LightAnchor', 'Description': 'Anchor light Bow PS'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 114, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 10}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 214, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 10}, 'SystemFunctionsValuePurposes': 'ToggleAction'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'LightAnchor', 'Description': 'Anchor light Bow SB'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 114, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 11}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}]\n", "CallSystemRoom2\n", "10\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': 'Spare alarm 81'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 108, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'KeepAlivePolling'}, {'RegisterMetaData': {'RegisterId': 203, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': 'Spare alarm 113'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 110, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'KeepAlivePolling'}, {'RegisterMetaData': {'RegisterId': 204, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': 'Spare alarm 146'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 112, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'KeepAlivePolling'}, {'RegisterMetaData': {'RegisterId': 205, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': '-'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 115, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'KeepAlivePolling'}, {'RegisterMetaData': {'RegisterId': 206, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': 'Generator 1 Running'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 117, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'KeepAlivePolling'}, {'RegisterMetaData': {'RegisterId': 207, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': '-'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 208, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': '-'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 209, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': '-'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 210, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': '-'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 211, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': '-'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 212, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}}]\n", "UpMastPole1\n", "0\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'UpMastPole1', 'Description': 'Mast Bow Up'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 116, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'MoveUpFeedback'}, {'RegisterMetaData': {'RegisterId': 116, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'MoveDownFeedback'}]}}]\n", "UpMastPole1\n", "0\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'UpMastPole1', 'Description': 'Mast Bow Up'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 116, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'MoveUpFeedback'}, {'RegisterMetaData': {'RegisterId': 116, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'MoveDownFeedback'}, {'RegisterMetaData': {'RegisterId': 216, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'MoveUpAction'}]}}]\n", "UpMastPole2\n", "0\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'UpMastPole2', 'Description': 'Mast Stern Up'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 116, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 4}, 'SystemFunctionsValuePurposes': 'MoveUpFeedback'}, {'RegisterMetaData': {'RegisterId': 116, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 5}, 'SystemFunctionsValuePurposes': 'MoveDownFeedback'}]}}]\n", "UpMastPole2\n", "0\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'UpMastPole2', 'Description': 'Mast Stern Up'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 116, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 4}, 'SystemFunctionsValuePurposes': 'MoveUpFeedback'}, {'RegisterMetaData': {'RegisterId': 116, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 5}, 'SystemFunctionsValuePurposes': 'MoveDownFeedback'}, {'RegisterMetaData': {'RegisterId': 216, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 4}, 'SystemFunctionsValuePurposes': 'MoveUpAction'}]}}]\n", "UpMastPole3\n", "0\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'UpMastPole3', 'Description': 'Spoiler Mast Stern Up'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 116, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 6}, 'SystemFunctionsValuePurposes': 'MoveUpFeedback'}, {'RegisterMetaData': {'RegisterId': 116, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 7}, 'SystemFunctionsValuePurposes': 'MoveDownFeedback'}]}}]\n", "UpMastPole3\n", "0\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'UpMastPole3', 'Description': 'Spoiler Mast Stern Up'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 116, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 6}, 'SystemFunctionsValuePurposes': 'MoveUpFeedback'}, {'RegisterMetaData': {'RegisterId': 116, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 7}, 'SystemFunctionsValuePurposes': 'MoveDownFeedback'}, {'RegisterMetaData': {'RegisterId': 216, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 6}, 'SystemFunctionsValuePurposes': 'MoveUpAction'}]}}]\n", "CallSystemRoom2\n", "11\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': 'Spare alarm 81'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 108, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'KeepAlivePolling'}, {'RegisterMetaData': {'RegisterId': 203, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': 'Spare alarm 113'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 110, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'KeepAlivePolling'}, {'RegisterMetaData': {'RegisterId': 204, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': 'Spare alarm 146'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 112, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'KeepAlivePolling'}, {'RegisterMetaData': {'RegisterId': 205, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': '-'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 115, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'KeepAlivePolling'}, {'RegisterMetaData': {'RegisterId': 206, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': 'Generator 1 Running'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 117, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'KeepAlivePolling'}, {'RegisterMetaData': {'RegisterId': 207, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': '-'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 208, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': '-'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 209, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': '-'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 210, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': '-'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 211, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': '-'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 212, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2', 'Description': '-'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 215, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}}]\n", "DeckLight1\n", "0\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Poller Fore Portside'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 118, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Poller Fore Starboard'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 118, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Mast Fore Portside'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 118, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 2}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Mast Fore Starboard'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 118, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 3}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Spoiler Aft Deck Portside'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Spoiler Aft Deck Starboard'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Boardunit Portside'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 2}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Boardunit Startboard'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 3}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlighting Wheelhouse Roof Front'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 4}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlighting Aft Mast'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 5}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}]\n", "DeckLight1\n", "1\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Poller Fore Portside'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 118, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 218, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ToggleAction'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Poller Fore Starboard'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 118, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Mast Fore Portside'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 118, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 2}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Mast Fore Starboard'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 118, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 3}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Spoiler Aft Deck Portside'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Spoiler Aft Deck Starboard'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Boardunit Portside'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 2}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Boardunit Startboard'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 3}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlighting Wheelhouse Roof Front'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 4}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlighting Aft Mast'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 5}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}]\n", "DeckLight1\n", "2\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Poller Fore Portside'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 118, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 218, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ToggleAction'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Poller Fore Starboard'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 118, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 218, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'ToggleAction'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Mast Fore Portside'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 118, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 2}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Mast Fore Starboard'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 118, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 3}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Spoiler Aft Deck Portside'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Spoiler Aft Deck Starboard'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Boardunit Portside'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 2}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Boardunit Startboard'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 3}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlighting Wheelhouse Roof Front'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 4}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlighting Aft Mast'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 5}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}]\n", "DeckLight1\n", "3\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Poller Fore Portside'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 118, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 218, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ToggleAction'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Poller Fore Starboard'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 118, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 218, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'ToggleAction'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Mast Fore Portside'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 118, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 2}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 218, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 2}, 'SystemFunctionsValuePurposes': 'ToggleAction'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Mast Fore Starboard'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 118, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 3}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Spoiler Aft Deck Portside'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Spoiler Aft Deck Starboard'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Boardunit Portside'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 2}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Boardunit Startboard'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 3}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlighting Wheelhouse Roof Front'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 4}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlighting Aft Mast'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 5}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}]\n", "DeckLight1\n", "4\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Poller Fore Portside'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 118, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 218, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ToggleAction'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Poller Fore Starboard'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 118, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 218, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'ToggleAction'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Mast Fore Portside'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 118, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 2}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 218, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 2}, 'SystemFunctionsValuePurposes': 'ToggleAction'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Mast Fore Starboard'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 118, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 3}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 218, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 3}, 'SystemFunctionsValuePurposes': 'ToggleAction'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Spoiler Aft Deck Portside'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Spoiler Aft Deck Starboard'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Boardunit Portside'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 2}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Boardunit Startboard'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 3}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlighting Wheelhouse Roof Front'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 4}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlighting Aft Mast'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 5}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}]\n", "DeckLight1\n", "5\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Poller Fore Portside'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 118, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 218, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ToggleAction'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Poller Fore Starboard'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 118, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 218, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'ToggleAction'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Mast Fore Portside'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 118, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 2}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 218, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 2}, 'SystemFunctionsValuePurposes': 'ToggleAction'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Mast Fore Starboard'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 118, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 3}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 218, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 3}, 'SystemFunctionsValuePurposes': 'ToggleAction'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Spoiler Aft Deck Portside'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 219, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ToggleAction'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Spoiler Aft Deck Starboard'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Boardunit Portside'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 2}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Boardunit Startboard'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 3}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlighting Wheelhouse Roof Front'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 4}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlighting Aft Mast'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 5}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}]\n", "DeckLight1\n", "6\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Poller Fore Portside'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 118, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 218, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ToggleAction'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Poller Fore Starboard'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 118, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 218, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'ToggleAction'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Mast Fore Portside'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 118, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 2}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 218, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 2}, 'SystemFunctionsValuePurposes': 'ToggleAction'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Mast Fore Starboard'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 118, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 3}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 218, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 3}, 'SystemFunctionsValuePurposes': 'ToggleAction'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Spoiler Aft Deck Portside'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 219, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ToggleAction'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Spoiler Aft Deck Starboard'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 219, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'ToggleAction'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Boardunit Portside'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 2}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Boardunit Startboard'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 3}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlighting Wheelhouse Roof Front'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 4}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlighting Aft Mast'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 5}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}]\n", "DeckLight1\n", "7\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Poller Fore Portside'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 118, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 218, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ToggleAction'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Poller Fore Starboard'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 118, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 218, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'ToggleAction'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Mast Fore Portside'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 118, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 2}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 218, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 2}, 'SystemFunctionsValuePurposes': 'ToggleAction'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Mast Fore Starboard'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 118, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 3}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 218, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 3}, 'SystemFunctionsValuePurposes': 'ToggleAction'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Spoiler Aft Deck Portside'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 219, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ToggleAction'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Spoiler Aft Deck Starboard'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 219, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'ToggleAction'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Boardunit Portside'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 2}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 219, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 2}, 'SystemFunctionsValuePurposes': 'ToggleAction'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Boardunit Startboard'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 3}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlighting Wheelhouse Roof Front'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 4}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlighting Aft Mast'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 5}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}]\n", "DeckLight1\n", "8\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Poller Fore Portside'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 118, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 218, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ToggleAction'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Poller Fore Starboard'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 118, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 218, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'ToggleAction'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Mast Fore Portside'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 118, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 2}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 218, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 2}, 'SystemFunctionsValuePurposes': 'ToggleAction'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Mast Fore Starboard'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 118, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 3}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 218, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 3}, 'SystemFunctionsValuePurposes': 'ToggleAction'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Spoiler Aft Deck Portside'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 219, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ToggleAction'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Spoiler Aft Deck Starboard'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 219, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'ToggleAction'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Boardunit Portside'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 2}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 219, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 2}, 'SystemFunctionsValuePurposes': 'ToggleAction'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Boardunit Startboard'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 3}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 219, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 3}, 'SystemFunctionsValuePurposes': 'ToggleAction'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlighting Wheelhouse Roof Front'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 4}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlighting Aft Mast'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 5}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}]\n", "DeckLight1\n", "9\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Poller Fore Portside'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 118, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 218, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ToggleAction'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Poller Fore Starboard'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 118, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 218, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'ToggleAction'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Mast Fore Portside'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 118, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 2}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 218, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 2}, 'SystemFunctionsValuePurposes': 'ToggleAction'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Mast Fore Starboard'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 118, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 3}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 218, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 3}, 'SystemFunctionsValuePurposes': 'ToggleAction'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Spoiler Aft Deck Portside'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 219, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ToggleAction'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Spoiler Aft Deck Starboard'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 219, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'ToggleAction'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Boardunit Portside'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 2}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 219, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 2}, 'SystemFunctionsValuePurposes': 'ToggleAction'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlight Boardunit Startboard'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 3}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 219, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 3}, 'SystemFunctionsValuePurposes': 'ToggleAction'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlighting Wheelhouse Roof Front'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 4}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 219, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 4}, 'SystemFunctionsValuePurposes': 'ToggleAction'}]}}, {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1', 'Description': 'Floodlighting Aft Mast'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 5}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}]}}]\n", "SearchLight1\n", "0\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'SearchLight1', 'Description': 'Search light Bow ON/OFF'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 120, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 120, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'MoveLeftFeedback'}, {'RegisterMetaData': {'RegisterId': 120, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 2}, 'SystemFunctionsValuePurposes': 'MoveRightFeedback'}, {'RegisterMetaData': {'RegisterId': 120, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 3}, 'SystemFunctionsValuePurposes': 'MoveUpFeedback'}, {'RegisterMetaData': {'RegisterId': 120, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 4}, 'SystemFunctionsValuePurposes': 'MoveDownFeedback'}]}}]\n", "SearchLight1\n", "0\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'SearchLight1', 'Description': 'Search light Bow ON/OFF'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 120, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 120, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'MoveLeftFeedback'}, {'RegisterMetaData': {'RegisterId': 120, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 2}, 'SystemFunctionsValuePurposes': 'MoveRightFeedback'}, {'RegisterMetaData': {'RegisterId': 120, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 3}, 'SystemFunctionsValuePurposes': 'MoveUpFeedback'}, {'RegisterMetaData': {'RegisterId': 120, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 4}, 'SystemFunctionsValuePurposes': 'MoveDownFeedback'}, {'RegisterMetaData': {'RegisterId': 220, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ToggleAction'}]}}]\n", "SearchLight1\n", "0\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'SearchLight1', 'Description': 'Search light Bow ON/OFF'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 120, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 120, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'MoveLeftFeedback'}, {'RegisterMetaData': {'RegisterId': 120, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 2}, 'SystemFunctionsValuePurposes': 'MoveRightFeedback'}, {'RegisterMetaData': {'RegisterId': 120, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 3}, 'SystemFunctionsValuePurposes': 'MoveUpFeedback'}, {'RegisterMetaData': {'RegisterId': 120, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 4}, 'SystemFunctionsValuePurposes': 'MoveDownFeedback'}, {'RegisterMetaData': {'RegisterId': 220, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ToggleAction'}, {'RegisterMetaData': {'RegisterId': 220, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'MoveLeftAction'}]}}]\n", "SearchLight1\n", "0\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'SearchLight1', 'Description': 'Search light Bow ON/OFF'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 120, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 120, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'MoveLeftFeedback'}, {'RegisterMetaData': {'RegisterId': 120, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 2}, 'SystemFunctionsValuePurposes': 'MoveRightFeedback'}, {'RegisterMetaData': {'RegisterId': 120, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 3}, 'SystemFunctionsValuePurposes': 'MoveUpFeedback'}, {'RegisterMetaData': {'RegisterId': 120, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 4}, 'SystemFunctionsValuePurposes': 'MoveDownFeedback'}, {'RegisterMetaData': {'RegisterId': 220, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ToggleAction'}, {'RegisterMetaData': {'RegisterId': 220, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'MoveLeftAction'}, {'RegisterMetaData': {'RegisterId': 220, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 2}, 'SystemFunctionsValuePurposes': 'MoveRightAction'}]}}]\n", "SearchLight1\n", "0\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'SearchLight1', 'Description': 'Search light Bow ON/OFF'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 120, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 120, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'MoveLeftFeedback'}, {'RegisterMetaData': {'RegisterId': 120, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 2}, 'SystemFunctionsValuePurposes': 'MoveRightFeedback'}, {'RegisterMetaData': {'RegisterId': 120, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 3}, 'SystemFunctionsValuePurposes': 'MoveUpFeedback'}, {'RegisterMetaData': {'RegisterId': 120, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 4}, 'SystemFunctionsValuePurposes': 'MoveDownFeedback'}, {'RegisterMetaData': {'RegisterId': 220, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ToggleAction'}, {'RegisterMetaData': {'RegisterId': 220, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'MoveLeftAction'}, {'RegisterMetaData': {'RegisterId': 220, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 2}, 'SystemFunctionsValuePurposes': 'MoveRightAction'}, {'RegisterMetaData': {'RegisterId': 220, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 3}, 'SystemFunctionsValuePurposes': 'MoveUpAction'}]}}]\n", "DownSpudPoleStern1\n", "0\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'DownSpudPoleStern1', 'Description': 'Search light Stern PS ON/OFF'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 120, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 8}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 120, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 9}, 'SystemFunctionsValuePurposes': 'MoveLeftFeedback'}, {'RegisterMetaData': {'RegisterId': 120, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 10}, 'SystemFunctionsValuePurposes': 'MoveRightFeedback'}, {'RegisterMetaData': {'RegisterId': 120, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 11}, 'SystemFunctionsValuePurposes': 'MoveUpFeedback'}, {'RegisterMetaData': {'RegisterId': 120, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 12}, 'SystemFunctionsValuePurposes': 'MoveDownFeedback'}]}}]\n", "DownSpudPoleStern1\n", "0\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'DownSpudPoleStern1', 'Description': 'Search light Stern PS ON/OFF'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 120, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 8}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 120, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 9}, 'SystemFunctionsValuePurposes': 'MoveLeftFeedback'}, {'RegisterMetaData': {'RegisterId': 120, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 10}, 'SystemFunctionsValuePurposes': 'MoveRightFeedback'}, {'RegisterMetaData': {'RegisterId': 120, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 11}, 'SystemFunctionsValuePurposes': 'MoveUpFeedback'}, {'RegisterMetaData': {'RegisterId': 120, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 12}, 'SystemFunctionsValuePurposes': 'MoveDownFeedback'}, {'RegisterMetaData': {'RegisterId': 220, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 8}, 'SystemFunctionsValuePurposes': 'ToggleAction'}]}}]\n", "DownSpudPoleStern1\n", "0\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'DownSpudPoleStern1', 'Description': 'Search light Stern PS ON/OFF'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 120, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 8}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 120, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 9}, 'SystemFunctionsValuePurposes': 'MoveLeftFeedback'}, {'RegisterMetaData': {'RegisterId': 120, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 10}, 'SystemFunctionsValuePurposes': 'MoveRightFeedback'}, {'RegisterMetaData': {'RegisterId': 120, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 11}, 'SystemFunctionsValuePurposes': 'MoveUpFeedback'}, {'RegisterMetaData': {'RegisterId': 120, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 12}, 'SystemFunctionsValuePurposes': 'MoveDownFeedback'}, {'RegisterMetaData': {'RegisterId': 220, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 8}, 'SystemFunctionsValuePurposes': 'ToggleAction'}, {'RegisterMetaData': {'RegisterId': 220, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 9}, 'SystemFunctionsValuePurposes': 'MoveLeftAction'}]}}]\n", "DownSpudPoleStern1\n", "0\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'DownSpudPoleStern1', 'Description': 'Search light Stern PS ON/OFF'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 120, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 8}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 120, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 9}, 'SystemFunctionsValuePurposes': 'MoveLeftFeedback'}, {'RegisterMetaData': {'RegisterId': 120, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 10}, 'SystemFunctionsValuePurposes': 'MoveRightFeedback'}, {'RegisterMetaData': {'RegisterId': 120, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 11}, 'SystemFunctionsValuePurposes': 'MoveUpFeedback'}, {'RegisterMetaData': {'RegisterId': 120, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 12}, 'SystemFunctionsValuePurposes': 'MoveDownFeedback'}, {'RegisterMetaData': {'RegisterId': 220, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 8}, 'SystemFunctionsValuePurposes': 'ToggleAction'}, {'RegisterMetaData': {'RegisterId': 220, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 9}, 'SystemFunctionsValuePurposes': 'MoveLeftAction'}, {'RegisterMetaData': {'RegisterId': 220, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 10}, 'SystemFunctionsValuePurposes': 'MoveRightAction'}]}}]\n", "DownSpudPoleStern1\n", "0\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'DownSpudPoleStern1', 'Description': 'Search light Stern PS ON/OFF'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 120, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 8}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 120, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 9}, 'SystemFunctionsValuePurposes': 'MoveLeftFeedback'}, {'RegisterMetaData': {'RegisterId': 120, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 10}, 'SystemFunctionsValuePurposes': 'MoveRightFeedback'}, {'RegisterMetaData': {'RegisterId': 120, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 11}, 'SystemFunctionsValuePurposes': 'MoveUpFeedback'}, {'RegisterMetaData': {'RegisterId': 120, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 12}, 'SystemFunctionsValuePurposes': 'MoveDownFeedback'}, {'RegisterMetaData': {'RegisterId': 220, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 8}, 'SystemFunctionsValuePurposes': 'ToggleAction'}, {'RegisterMetaData': {'RegisterId': 220, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 9}, 'SystemFunctionsValuePurposes': 'MoveLeftAction'}, {'RegisterMetaData': {'RegisterId': 220, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 10}, 'SystemFunctionsValuePurposes': 'MoveRightAction'}, {'RegisterMetaData': {'RegisterId': 220, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 11}, 'SystemFunctionsValuePurposes': 'MoveUpAction'}]}}]\n", "DownSpudPoleBow1\n", "0\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'DownSpudPoleBow1', 'Description': 'Search light Stern SB ON/OFF'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 121, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 121, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'MoveLeftFeedback'}, {'RegisterMetaData': {'RegisterId': 121, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 2}, 'SystemFunctionsValuePurposes': 'MoveRightFeedback'}, {'RegisterMetaData': {'RegisterId': 121, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 3}, 'SystemFunctionsValuePurposes': 'MoveUpFeedback'}, {'RegisterMetaData': {'RegisterId': 121, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 4}, 'SystemFunctionsValuePurposes': 'MoveDownFeedback'}]}}]\n", "DownSpudPoleBow1\n", "0\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'DownSpudPoleBow1', 'Description': 'Search light Stern SB ON/OFF'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 121, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 121, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'MoveLeftFeedback'}, {'RegisterMetaData': {'RegisterId': 121, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 2}, 'SystemFunctionsValuePurposes': 'MoveRightFeedback'}, {'RegisterMetaData': {'RegisterId': 121, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 3}, 'SystemFunctionsValuePurposes': 'MoveUpFeedback'}, {'RegisterMetaData': {'RegisterId': 121, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 4}, 'SystemFunctionsValuePurposes': 'MoveDownFeedback'}, {'RegisterMetaData': {'RegisterId': 221, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ToggleAction'}]}}]\n", "DownSpudPoleBow1\n", "0\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'DownSpudPoleBow1', 'Description': 'Search light Stern SB ON/OFF'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 121, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 121, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'MoveLeftFeedback'}, {'RegisterMetaData': {'RegisterId': 121, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 2}, 'SystemFunctionsValuePurposes': 'MoveRightFeedback'}, {'RegisterMetaData': {'RegisterId': 121, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 3}, 'SystemFunctionsValuePurposes': 'MoveUpFeedback'}, {'RegisterMetaData': {'RegisterId': 121, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 4}, 'SystemFunctionsValuePurposes': 'MoveDownFeedback'}, {'RegisterMetaData': {'RegisterId': 221, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ToggleAction'}, {'RegisterMetaData': {'RegisterId': 221, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'MoveLeftAction'}]}}]\n", "DownSpudPoleBow1\n", "0\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'DownSpudPoleBow1', 'Description': 'Search light Stern SB ON/OFF'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 121, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 121, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'MoveLeftFeedback'}, {'RegisterMetaData': {'RegisterId': 121, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 2}, 'SystemFunctionsValuePurposes': 'MoveRightFeedback'}, {'RegisterMetaData': {'RegisterId': 121, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 3}, 'SystemFunctionsValuePurposes': 'MoveUpFeedback'}, {'RegisterMetaData': {'RegisterId': 121, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 4}, 'SystemFunctionsValuePurposes': 'MoveDownFeedback'}, {'RegisterMetaData': {'RegisterId': 221, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ToggleAction'}, {'RegisterMetaData': {'RegisterId': 221, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'MoveLeftAction'}, {'RegisterMetaData': {'RegisterId': 221, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 2}, 'SystemFunctionsValuePurposes': 'MoveRightAction'}]}}]\n", "DownSpudPoleBow1\n", "0\n", "[{'DomainDriverIdentification': {'SystemFunctionId': 'DownSpudPoleBow1', 'Description': 'Search light Stern SB ON/OFF'}, 'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 121, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ActionFeedback'}, {'RegisterMetaData': {'RegisterId': 121, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'MoveLeftFeedback'}, {'RegisterMetaData': {'RegisterId': 121, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 2}, 'SystemFunctionsValuePurposes': 'MoveRightFeedback'}, {'RegisterMetaData': {'RegisterId': 121, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 3}, 'SystemFunctionsValuePurposes': 'MoveUpFeedback'}, {'RegisterMetaData': {'RegisterId': 121, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 4}, 'SystemFunctionsValuePurposes': 'MoveDownFeedback'}, {'RegisterMetaData': {'RegisterId': 221, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 0}, 'SystemFunctionsValuePurposes': 'ToggleAction'}, {'RegisterMetaData': {'RegisterId': 221, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 1}, 'SystemFunctionsValuePurposes': 'MoveLeftAction'}, {'RegisterMetaData': {'RegisterId': 221, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 2}, 'SystemFunctionsValuePurposes': 'MoveRightAction'}, {'RegisterMetaData': {'RegisterId': 221, 'RegisterType': 'HoldingRegister', 'RegisterValueType': 'Boolean'}, 'RegisterParsingData': {'BitIndex': 3}, 'SystemFunctionsValuePurposes': 'MoveUpAction'}]}}]\n"]}, {"ename": "AttributeError", "evalue": "'int' object has no attribute 'strip'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mAttributeError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[50], line 24\u001b[0m\n\u001b[1;32m     22\u001b[0m amp_config \u001b[38;5;241m=\u001b[39m {\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mModbusAutomationDriver\u001b[39m\u001b[38;5;124m'\u001b[39m: []}\n\u001b[1;32m     23\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m ip, config \u001b[38;5;129;01min\u001b[39;00m configs_per_ip\u001b[38;5;241m.\u001b[39mitems():\n\u001b[0;32m---> 24\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[43mip\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mstrip\u001b[49m() \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m'\u001b[39m\u001b[38;5;124m'\u001b[39m:\n\u001b[1;32m     25\u001b[0m         \u001b[38;5;28;01mcontinue\u001b[39;00m\n\u001b[1;32m     26\u001b[0m     config[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mConnectionHandlerConfig\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;241m=\u001b[39m  {\n\u001b[1;32m     27\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mprotocol\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmodbustcpclient\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[1;32m     28\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mconnectionAddress\u001b[39m\u001b[38;5;124m\"\u001b[39m: ip,\n\u001b[1;32m     29\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mconnectionAddressOption\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;241m502\u001b[39m\n\u001b[1;32m     30\u001b[0m     }\n", "\u001b[0;31mAttributeError\u001b[0m: 'int' object has no attribute 'strip'"]}], "source": ["modbus_driver_configs = []\n", "configs_per_ip = {\n", "    f'10.0.{vehicle_id}.13': safety_system_io_config,\n", "    f'10.1.{vehicle_id}.164': safety_system_pilz_config,\n", "    f'10.0.{vehicle_id}.15': cabinet_readout_io_moxa\n", "}\n", "\n", "propulsion_pulse_configs = {}\n", "system_functions_pulse_configs = {}\n", "\n", "\n", "system_configs_per_id_per_ip = {}\n", "propulsion_configs_per_id_per_ip = {}\n", "_ = config_df.apply(prepare_config, axis=1)\n", "\n", "for ip, system_configs_per_id in system_configs_per_id_per_ip.items():\n", "    if ip != '':\n", "        for _id, system_funcion_config  in system_configs_per_id.items():\n", "            for config in system_funcion_config:\n", "                configs_per_ip[ip]['DriverConfig']['SystemFunctionsDomainDrivers'].append(config)\n", "\n", "amp_config = {'ModbusAutomationDriver': []}\n", "for ip, config in configs_per_ip.items():\n", "    if ip.strip() == '':\n", "        continue\n", "    config['ConnectionHandlerConfig'] =  {\n", "        \"protocol\": \"modbustcpclient\",\n", "        \"connectionAddress\": ip,\n", "        \"connectionAddressOption\": 502\n", "    }\n", "    config['Pipelines'] = [\n", "        'Automation'\n", "    ]\n", "    amp_config['ModbusAutomationDriver'].append(config)"]}, {"cell_type": "code", "execution_count": 51, "id": "eaaa3688", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'ModbusAutomationDriver': [{'DriverConfig': {'DriverIdentification': {'name': 'Operator panel - Moxa',\n", "     'technicalComponentId': '',\n", "     'driverId': '626b9a03-f1f1-4cdc-a6f8-32785617bf3b'},\n", "    'rebootIntervalOnFail_ms': 1000,\n", "    'heartbeatInterval_ms': 1000,\n", "    'SendingIntervalMs': 250,\n", "    'ReadingIntervalMs': 250,\n", "    'GeneralSystemDomainDrivers': [{'RegisterMetaData': {'RegisterId': 32,\n", "       'RegisterType': 'HoldingRegister',\n", "       'RegisterValueType': 'Boolean'},\n", "      'RegisterParsingData': {'BitIndex': 0},\n", "      'GeneralSystemValuePurpose': 'SafetyBuzzerControl'},\n", "     {'RegisterMetaData': {'RegisterId': 48,\n", "       'RegisterType': 'InputRegister',\n", "       'RegisterValueType': 'Boolean'},\n", "      'RegisterParsingData': {'BitIndex': 0},\n", "      'GeneralSystemValuePurpose': 'LocalControlRequest'},\n", "     {'RegisterMetaData': {'RegisterId': 32,\n", "       'RegisterType': 'HoldingRegister',\n", "       'RegisterValueType': 'Boolean'},\n", "      'RegisterParsingData': {'BitIndex': 2},\n", "      'GeneralSystemValuePurpose': 'RunStateLedControl'},\n", "     {'RegisterMetaData': {'RegisterId': 32,\n", "       'RegisterType': 'HoldingRegister',\n", "       'RegisterValueType': 'Boolean'},\n", "      'RegisterParsingData': {'BitIndex': 1},\n", "      'GeneralSystemValuePurpose': 'SafetyLed'},\n", "     {'RegisterMetaData': {'RegisterId': 32,\n", "       'RegisterType': 'HoldingRegister',\n", "       'RegisterValueType': 'Boolean'},\n", "      'RegisterParsingData': {'BitIndex': 3},\n", "      'GeneralSystemValuePurpose': 'ResetSafetySystem'}],\n", "    'PropulsionDomainDrivers': [],\n", "    'SystemFunctionsDomainDrivers': [],\n", "    'AlarmDomainDrivers': []},\n", "   'ConnectionHandlerConfig': {'protocol': 'modbustcpclient',\n", "    'connectionAddress': '**********',\n", "    'connectionAddressOption': 502},\n", "   'Pipelines': ['Automation']},\n", "  {'DriverConfig': {'DriverIdentification': {'name': 'Safety system - Pilz',\n", "     'technicalComponentId': '',\n", "     'driverId': 'e46936ab-d2e4-42eb-9333-85b0e4f133e4'},\n", "    'rebootIntervalOnFail_ms': 1000,\n", "    'heartbeatInterval_ms': 1000,\n", "    'SendingIntervalMs': 500,\n", "    'ReadingIntervalMs': 250,\n", "    'GeneralSystemDomainDrivers': [{'RegisterMetaData': {'RegisterId': 1060,\n", "       'RegisterType': 'HoldingRegister',\n", "       'RegisterValueType': 'Boolean'},\n", "      'RegisterParsingData': {'BitIndex': 2},\n", "      'GeneralSystemValuePurpose': 'RunSwitchFeedback'},\n", "     {'RegisterMetaData': {'RegisterId': 1060,\n", "       'RegisterType': 'HoldingRegister',\n", "       'RegisterValueType': 'Boolean'},\n", "      'RegisterParsingData': {'BitIndex': 0, 'InvertBitValue': True},\n", "      'GeneralSystemValuePurpose': 'EmergencyStopFeedback'},\n", "     {'RegisterMetaData': {'RegisterId': 1062,\n", "       'RegisterType': 'HoldingRegister',\n", "       'RegisterValueType': 'Boolean'},\n", "      'RegisterParsingData': {'BitIndex': 2},\n", "      'GeneralSystemValuePurpose': 'SafetyOutputFeedback'}],\n", "    'PropulsionDomainDrivers': [],\n", "    'SystemFunctionsDomainDrivers': [],\n", "    'AlarmDomainDrivers': []},\n", "   'ConnectionHandlerConfig': {'protocol': 'modbustcpclient',\n", "    'connectionAddress': '***********',\n", "    'connectionAddressOption': 502},\n", "   'Pipelines': ['Automation']},\n", "  {'DriverConfig': {'DriverIdentification': {'name': 'Cabinet readout IO - Moxa',\n", "     'technicalComponentId': '',\n", "     'driverId': 'dd5bd1e3-e4fc-4098-9b25-6aeab8fa6e89'},\n", "    'rebootIntervalOnFail_ms': 1000,\n", "    'heartbeatInterval_ms': 1000,\n", "    'SendingIntervalMs': 250,\n", "    'ReadingIntervalMs': 250,\n", "    'GeneralSystemDomainDrivers': [],\n", "    'PropulsionDomainDrivers': [],\n", "    'SystemFunctionsDomainDrivers': [],\n", "    'AlarmDomainDrivers': [{'DomainDriverIdentification': {'AlarmMessage': 'Stabilizer channel 1 not ok',\n", "       'NotificationGroupId': 'PowerGeneral',\n", "       'EntityId': 'Stabilizer 1',\n", "       'NotificationTypeId': 'Warning',\n", "       'WarningId': 'MiPowerGeneral'},\n", "      'DomainSpecificConfig': {'RegisterMetaData': {'RegisterId': 48,\n", "        'RegisterType': 'InputRegister',\n", "        'RegisterValueType': 'Boolean'},\n", "       'RegisterParsingData': {'BitIndex': 0, 'InvertBitValue': True}}},\n", "     {'DomainDriverIdentification': {'AlarmMessage': 'Stabilizer channel 2 not ok',\n", "       'NotificationGroupId': 'PowerGeneral',\n", "       'EntityId': 'Stabilizer 2',\n", "       'NotificationTypeId': 'Warning',\n", "       'WarningId': 'MiPowerGeneral'},\n", "      'DomainSpecificConfig': {'RegisterMetaData': {'RegisterId': 48,\n", "        'RegisterType': 'InputRegister',\n", "        'RegisterValueType': 'Boolean'},\n", "       'RegisterParsingData': {'BitIndex': 1, 'InvertBitValue': True}}},\n", "     {'DomainDriverIdentification': {'AlarmMessage': '12V Distribution not ok',\n", "       'NotificationGroupId': 'PowerGeneral',\n", "       'EntityId': '12V Core cabinet',\n", "       'NotificationTypeId': 'Warning',\n", "       'WarningId': 'MiPowerGeneral'},\n", "      'DomainSpecificConfig': {'RegisterMetaData': {'RegisterId': 48,\n", "        'RegisterType': 'InputRegister',\n", "        'RegisterValueType': 'Boolean'},\n", "       'RegisterParsingData': {'BitIndex': 3, 'InvertBitValue': True}}},\n", "     {'DomainDriverIdentification': {'AlarmMessage': 'Cooling 5 degrees above set temperature of 35 degrees',\n", "       'NotificationGroupId': 'ControlSystemGeneral',\n", "       'EntityId': 'Cooling Core cabinet',\n", "       'NotificationTypeId': 'Warning',\n", "       'WarningId': 'MiTemperatureExceededThresholds'},\n", "      'DomainSpecificConfig': {'RegisterMetaData': {'RegisterId': 48,\n", "        'RegisterType': 'InputRegister',\n", "        'RegisterValueType': 'Boolean'},\n", "       'RegisterParsingData': {'BitIndex': 4, 'InvertBitValue': True}}},\n", "     {'DomainDriverIdentification': {'AlarmMessage': 'Core cabinets door opened',\n", "       'NotificationGroupId': 'ControlSystemGeneral',\n", "       'EntityId': 'Door Core cabinet',\n", "       'NotificationTypeId': 'Warning',\n", "       'WarningId': 'Mi<PERSON><PERSON>O<PERSON>'},\n", "      'DomainSpecificConfig': {'RegisterMetaData': {'RegisterId': 48,\n", "        'RegisterType': 'InputRegister',\n", "        'RegisterValueType': 'Boolean'},\n", "       'RegisterParsingData': {'BitIndex': 5, 'InvertBitValue': True}}}]},\n", "   'ConnectionHandlerConfig': {'protocol': 'modbustcpclient',\n", "    'connectionAddress': '**********',\n", "    'connectionAddressOption': 502},\n", "   'Pipelines': ['Automation']},\n", "  {'DriverConfig': {'DriverIdentification': {'name': 'Ship Plc',\n", "     'technicalComponentId': '',\n", "     'driverId': 'd8f0293b-1da1-48e5-867e-ef45ffe439db'},\n", "    'rebootIntervalOnFail_ms': 1000,\n", "    'heartbeatInterval_ms': 1000,\n", "    'SendingIntervalMs': 250,\n", "    'ReadingIntervalMs': 250,\n", "    'GeneralSystemDomainDrivers': [{'RegisterMetaData': {'RegisterId': 101,\n", "       'RegisterType': 'HoldingRegister',\n", "       'RegisterValueType': 'Boolean'},\n", "      'RegisterParsingData': {'BitIndex': 0},\n", "      'GeneralSystemValuePurpose': 'ThirdPartyHeartbeat'}],\n", "    'PropulsionDomainDrivers': [],\n", "    'SystemFunctionsDomainDrivers': [{'DomainDriverIdentification': {'SystemFunctionId': 'BridgeWarningIndicator',\n", "       'Description': 'Seafar Control Feedback'},\n", "      'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 101,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 1},\n", "         'SystemFunctionsValuePurposes': 'ControlFeedback'},\n", "        {'RegisterMetaData': {'RegisterId': 101,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 2},\n", "         'SystemFunctionsValuePurposes': 'ThirdPartySafetyContactFeedback'},\n", "        {'RegisterMetaData': {'RegisterId': 201,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 1},\n", "         'SystemFunctionsValuePurposes': 'ControlAction'}]}},\n", "     {'DomainDriverIdentification': {'SystemFunctionId': 'AlarmGeneral',\n", "       'Description': 'General <PERSON><PERSON>'},\n", "      'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 102,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 0},\n", "         'SystemFunctionsValuePurposes': 'ActionFeedback'},\n", "        {'RegisterMetaData': {'RegisterId': 202,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 0},\n", "         'SystemFunctionsValuePurposes': 'ToggleAction'}]}},\n", "     {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom1',\n", "       'Description': 'Call / Alarm Bow Engineroom'},\n", "      'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 102,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 10},\n", "         'SystemFunctionsValuePurposes': 'ActionFeedback'},\n", "        {'RegisterMetaData': {'RegisterId': 202,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 10},\n", "         'SystemFunctionsValuePurposes': 'ToggleAction'}]}},\n", "     {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom1',\n", "       'Description': 'Call / Alarm Stern Engineroom'},\n", "      'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 102,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 11},\n", "         'SystemFunctionsValuePurposes': 'ActionFeedback'},\n", "        {'RegisterMetaData': {'RegisterId': 202,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 11},\n", "         'SystemFunctionsValuePurposes': 'ToggleAction'}]}},\n", "     {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom1',\n", "       'Description': 'Call / Alarm Stern Accommodation'},\n", "      'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 102,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 14},\n", "         'SystemFunctionsValuePurposes': 'ActionFeedback'},\n", "        {'RegisterMetaData': {'RegisterId': 202,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 14},\n", "         'SystemFunctionsValuePurposes': 'ToggleAction'}]}},\n", "     {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2',\n", "       'Description': 'Spare alarm 81'},\n", "      'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 108,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 0},\n", "         'SystemFunctionsValuePurposes': 'KeepAlivePolling'},\n", "        {'RegisterMetaData': {'RegisterId': 203,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 0},\n", "         'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}},\n", "     {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2',\n", "       'Description': 'Spare alarm 113'},\n", "      'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 110,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 0},\n", "         'SystemFunctionsValuePurposes': 'KeepAlivePolling'},\n", "        {'RegisterMetaData': {'RegisterId': 204,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 0},\n", "         'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}},\n", "     {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2',\n", "       'Description': 'Spare alarm 146'},\n", "      'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 112,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 1},\n", "         'SystemFunctionsValuePurposes': 'KeepAlivePolling'},\n", "        {'RegisterMetaData': {'RegisterId': 205,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 0},\n", "         'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}},\n", "     {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2',\n", "       'Description': '-'},\n", "      'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 115,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 0},\n", "         'SystemFunctionsValuePurposes': 'KeepAlivePolling'},\n", "        {'RegisterMetaData': {'RegisterId': 206,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 0},\n", "         'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}},\n", "     {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2',\n", "       'Description': 'Generator 1 Running'},\n", "      'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 117,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 0},\n", "         'SystemFunctionsValuePurposes': 'KeepAlivePolling'},\n", "        {'RegisterMetaData': {'RegisterId': 207,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 0},\n", "         'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}},\n", "     {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2',\n", "       'Description': '-'},\n", "      'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 208,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 0},\n", "         'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}},\n", "     {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2',\n", "       'Description': '-'},\n", "      'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 209,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 0},\n", "         'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}},\n", "     {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2',\n", "       'Description': '-'},\n", "      'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 210,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 0},\n", "         'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}},\n", "     {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2',\n", "       'Description': '-'},\n", "      'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 211,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 0},\n", "         'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}},\n", "     {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2',\n", "       'Description': '-'},\n", "      'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 212,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 0},\n", "         'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}},\n", "     {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2',\n", "       'Description': '-'},\n", "      'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 215,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 0},\n", "         'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}},\n", "     {'DomainDriverIdentification': {'SystemFunctionId': 'CallSystemRoom2',\n", "       'Description': 'Generator 1 Start'},\n", "      'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 217,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 0},\n", "         'SystemFunctionsValuePurposes': 'RegisterFillValue'}]}},\n", "     {'DomainDriverIdentification': {'SystemFunctionId': 'Horn',\n", "       'Description': '<PERSON><PERSON><PERSON>'},\n", "      'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 113,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 0},\n", "         'SystemFunctionsValuePurposes': 'ActionFeedback'},\n", "        {'RegisterMetaData': {'RegisterId': 213,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 0},\n", "         'SystemFunctionsValuePurposes': 'ToggleAction'}]}},\n", "     {'DomainDriverIdentification': {'SystemFunctionId': 'LightBlueSign',\n", "       'Description': 'Blue Sign (board)'},\n", "      'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 113,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 3},\n", "         'SystemFunctionsValuePurposes': 'ActionFeedback'},\n", "        {'RegisterMetaData': {'RegisterId': 213,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 3},\n", "         'SystemFunctionsValuePurposes': 'ToggleAction'}]}},\n", "     {'DomainDriverIdentification': {'SystemFunctionId': 'StrobeLights',\n", "       'Description': 'Orange Strobe Light'},\n", "      'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 113,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 6},\n", "         'SystemFunctionsValuePurposes': 'ActionFeedback'},\n", "        {'RegisterMetaData': {'RegisterId': 213,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 6},\n", "         'SystemFunctionsValuePurposes': 'ToggleAction'}]}},\n", "     {'DomainDriverIdentification': {'SystemFunctionId': 'LightAdnrSign1',\n", "       'Description': 'Blue ADN Stern 1'},\n", "      'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 113,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 7},\n", "         'SystemFunctionsValuePurposes': 'ActionFeedback'},\n", "        {'RegisterMetaData': {'RegisterId': 213,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 7},\n", "         'SystemFunctionsValuePurposes': 'ToggleAction'}]}},\n", "     {'DomainDriverIdentification': {'SystemFunctionId': 'LightAdnrSign1',\n", "       'Description': 'Blue ADN Bow 1'},\n", "      'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 113,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 10},\n", "         'SystemFunctionsValuePurposes': 'ActionFeedback'},\n", "        {'RegisterMetaData': {'RegisterId': 213,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 10},\n", "         'SystemFunctionsValuePurposes': 'ToggleAction'}]}},\n", "     {'DomainDriverIdentification': {'SystemFunctionId': 'LightAdnrSign2',\n", "       'Description': 'Blue ADN Stern 2'},\n", "      'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 113,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 8},\n", "         'SystemFunctionsValuePurposes': 'ActionFeedback'},\n", "        {'RegisterMetaData': {'RegisterId': 213,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 8},\n", "         'SystemFunctionsValuePurposes': 'ToggleAction'}]}},\n", "     {'DomainDriverIdentification': {'SystemFunctionId': 'LightAdnrSign2',\n", "       'Description': 'Blue ADN Bow 2'},\n", "      'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 113,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 11},\n", "         'SystemFunctionsValuePurposes': 'ActionFeedback'},\n", "        {'RegisterMetaData': {'RegisterId': 213,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 11},\n", "         'SystemFunctionsValuePurposes': 'ToggleAction'}]}},\n", "     {'DomainDriverIdentification': {'SystemFunctionId': 'LightTowing',\n", "       'Description': 'Top light Bow 1'},\n", "      'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 114,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 0},\n", "         'SystemFunctionsValuePurposes': 'ActionFeedback'},\n", "        {'RegisterMetaData': {'RegisterId': 214,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 0},\n", "         'SystemFunctionsValuePurposes': 'ToggleAction'}]}},\n", "     {'DomainDriverIdentification': {'SystemFunctionId': 'LightTowing',\n", "       'Description': 'Top light Bow 2'},\n", "      'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 114,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 1},\n", "         'SystemFunctionsValuePurposes': 'ActionFeedback'},\n", "        {'RegisterMetaData': {'RegisterId': 214,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 1},\n", "         'SystemFunctionsValuePurposes': 'ToggleAction'}]}},\n", "     {'DomainDriverIdentification': {'SystemFunctionId': 'LightSides',\n", "       'Description': 'Side light PS'},\n", "      'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 114,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 3},\n", "         'SystemFunctionsValuePurposes': 'ActionFeedback'},\n", "        {'RegisterMetaData': {'RegisterId': 214,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 3},\n", "         'SystemFunctionsValuePurposes': 'ToggleAction'}]}},\n", "     {'DomainDriverIdentification': {'SystemFunctionId': 'LightSides',\n", "       'Description': 'Side light SB'},\n", "      'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 114,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 4},\n", "         'SystemFunctionsValuePurposes': 'ActionFeedback'},\n", "        {'RegisterMetaData': {'RegisterId': 214,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 4},\n", "         'SystemFunctionsValuePurposes': 'ToggleAction'}]}},\n", "     {'DomainDriverIdentification': {'SystemFunctionId': 'LightStern',\n", "       'Description': 'Stern light 1'},\n", "      'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 114,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 5},\n", "         'SystemFunctionsValuePurposes': 'ActionFeedback'},\n", "        {'RegisterMetaData': {'RegisterId': 214,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 5},\n", "         'SystemFunctionsValuePurposes': 'ToggleAction'}]}},\n", "     {'DomainDriverIdentification': {'SystemFunctionId': 'LightAnchor',\n", "       'Description': '<PERSON><PERSON> light Stern PS'},\n", "      'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 114,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 8},\n", "         'SystemFunctionsValuePurposes': 'ActionFeedback'},\n", "        {'RegisterMetaData': {'RegisterId': 214,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 8},\n", "         'SystemFunctionsValuePurposes': 'ToggleAction'}]}},\n", "     {'DomainDriverIdentification': {'SystemFunctionId': 'LightAnchor',\n", "       'Description': '<PERSON><PERSON> light Stern SB'},\n", "      'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 114,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 9},\n", "         'SystemFunctionsValuePurposes': 'ActionFeedback'},\n", "        {'RegisterMetaData': {'RegisterId': 214,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 9},\n", "         'SystemFunctionsValuePurposes': 'ToggleAction'}]}},\n", "     {'DomainDriverIdentification': {'SystemFunctionId': 'LightAnchor',\n", "       'Description': '<PERSON><PERSON> light Bow PS'},\n", "      'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 114,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 10},\n", "         'SystemFunctionsValuePurposes': 'ActionFeedback'},\n", "        {'RegisterMetaData': {'RegisterId': 214,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 10},\n", "         'SystemFunctionsValuePurposes': 'ToggleAction'}]}},\n", "     {'DomainDriverIdentification': {'SystemFunctionId': 'LightAnchor',\n", "       'Description': '<PERSON><PERSON> light Bow SB'},\n", "      'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 114,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 11},\n", "         'SystemFunctionsValuePurposes': 'ActionFeedback'},\n", "        {'RegisterMetaData': {'RegisterId': 214,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 11},\n", "         'SystemFunctionsValuePurposes': 'ToggleAction'}]}},\n", "     {'DomainDriverIdentification': {'SystemFunctionId': 'UpMastPole1',\n", "       'Description': '<PERSON><PERSON>'},\n", "      'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 116,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 0},\n", "         'SystemFunctionsValuePurposes': 'MoveUpFeedback'},\n", "        {'RegisterMetaData': {'RegisterId': 116,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 1},\n", "         'SystemFunctionsValuePurposes': 'MoveDownFeedback'},\n", "        {'RegisterMetaData': {'RegisterId': 216,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 0},\n", "         'SystemFunctionsValuePurposes': 'MoveUpAction'},\n", "        {'RegisterMetaData': {'RegisterId': 216,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 1},\n", "         'SystemFunctionsValuePurposes': 'MoveDownAction'}]}},\n", "     {'DomainDriverIdentification': {'SystemFunctionId': 'UpMastPole2',\n", "       'Description': '<PERSON><PERSON>'},\n", "      'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 116,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 4},\n", "         'SystemFunctionsValuePurposes': 'MoveUpFeedback'},\n", "        {'RegisterMetaData': {'RegisterId': 116,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 5},\n", "         'SystemFunctionsValuePurposes': 'MoveDownFeedback'},\n", "        {'RegisterMetaData': {'RegisterId': 216,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 4},\n", "         'SystemFunctionsValuePurposes': 'MoveUpAction'},\n", "        {'RegisterMetaData': {'RegisterId': 216,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 5},\n", "         'SystemFunctionsValuePurposes': 'MoveDownAction'}]}},\n", "     {'DomainDriverIdentification': {'SystemFunctionId': 'UpMastPole3',\n", "       'Description': 'Spoiler Mast <PERSON>'},\n", "      'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 116,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 6},\n", "         'SystemFunctionsValuePurposes': 'MoveUpFeedback'},\n", "        {'RegisterMetaData': {'RegisterId': 116,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 7},\n", "         'SystemFunctionsValuePurposes': 'MoveDownFeedback'},\n", "        {'RegisterMetaData': {'RegisterId': 216,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 6},\n", "         'SystemFunctionsValuePurposes': 'MoveUpAction'},\n", "        {'RegisterMetaData': {'RegisterId': 216,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 7},\n", "         'SystemFunctionsValuePurposes': 'MoveDownAction'}]}},\n", "     {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1',\n", "       'Description': 'Floodlight Poller Fore Portside'},\n", "      'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 118,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 0},\n", "         'SystemFunctionsValuePurposes': 'ActionFeedback'},\n", "        {'RegisterMetaData': {'RegisterId': 218,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 0},\n", "         'SystemFunctionsValuePurposes': 'ToggleAction'}]}},\n", "     {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1',\n", "       'Description': 'Floodlight Poller Fore Starboard'},\n", "      'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 118,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 1},\n", "         'SystemFunctionsValuePurposes': 'ActionFeedback'},\n", "        {'RegisterMetaData': {'RegisterId': 218,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 1},\n", "         'SystemFunctionsValuePurposes': 'ToggleAction'}]}},\n", "     {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1',\n", "       'Description': 'Floodlight Mast Fore Portside'},\n", "      'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 118,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 2},\n", "         'SystemFunctionsValuePurposes': 'ActionFeedback'},\n", "        {'RegisterMetaData': {'RegisterId': 218,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 2},\n", "         'SystemFunctionsValuePurposes': 'ToggleAction'}]}},\n", "     {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1',\n", "       'Description': 'Floodlight Mast Fore Starboard'},\n", "      'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 118,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 3},\n", "         'SystemFunctionsValuePurposes': 'ActionFeedback'},\n", "        {'RegisterMetaData': {'RegisterId': 218,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 3},\n", "         'SystemFunctionsValuePurposes': 'ToggleAction'}]}},\n", "     {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1',\n", "       'Description': 'Floodlight Spoiler Aft Deck Portside'},\n", "      'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 0},\n", "         'SystemFunctionsValuePurposes': 'ActionFeedback'},\n", "        {'RegisterMetaData': {'RegisterId': 219,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 0},\n", "         'SystemFunctionsValuePurposes': 'ToggleAction'}]}},\n", "     {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1',\n", "       'Description': 'Floodlight Spoiler Aft Deck Starboard'},\n", "      'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 1},\n", "         'SystemFunctionsValuePurposes': 'ActionFeedback'},\n", "        {'RegisterMetaData': {'RegisterId': 219,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 1},\n", "         'SystemFunctionsValuePurposes': 'ToggleAction'}]}},\n", "     {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1',\n", "       'Description': 'Floodlight Boardunit Portside'},\n", "      'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 2},\n", "         'SystemFunctionsValuePurposes': 'ActionFeedback'},\n", "        {'RegisterMetaData': {'RegisterId': 219,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 2},\n", "         'SystemFunctionsValuePurposes': 'ToggleAction'}]}},\n", "     {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1',\n", "       'Description': 'Floodlight Boardunit Startboard'},\n", "      'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 3},\n", "         'SystemFunctionsValuePurposes': 'ActionFeedback'},\n", "        {'RegisterMetaData': {'RegisterId': 219,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 3},\n", "         'SystemFunctionsValuePurposes': 'ToggleAction'}]}},\n", "     {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1',\n", "       'Description': 'Floodlighting Wheelhouse Roof Front'},\n", "      'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 4},\n", "         'SystemFunctionsValuePurposes': 'ActionFeedback'},\n", "        {'RegisterMetaData': {'RegisterId': 219,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 4},\n", "         'SystemFunctionsValuePurposes': 'ToggleAction'}]}},\n", "     {'DomainDriverIdentification': {'SystemFunctionId': 'DeckLight1',\n", "       'Description': 'Floodlighting Aft Mast'},\n", "      'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 119,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 5},\n", "         'SystemFunctionsValuePurposes': 'ActionFeedback'},\n", "        {'RegisterMetaData': {'RegisterId': 219,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 5},\n", "         'SystemFunctionsValuePurposes': 'ToggleAction'}]}},\n", "     {'DomainDriverIdentification': {'SystemFunctionId': 'SearchLight1',\n", "       'Description': 'Search light Bow ON/OFF'},\n", "      'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 120,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 0},\n", "         'SystemFunctionsValuePurposes': 'ActionFeedback'},\n", "        {'RegisterMetaData': {'RegisterId': 120,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 1},\n", "         'SystemFunctionsValuePurposes': 'MoveLeftFeedback'},\n", "        {'RegisterMetaData': {'RegisterId': 120,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 2},\n", "         'SystemFunctionsValuePurposes': 'MoveRightFeedback'},\n", "        {'RegisterMetaData': {'RegisterId': 120,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 3},\n", "         'SystemFunctionsValuePurposes': 'MoveUpFeedback'},\n", "        {'RegisterMetaData': {'RegisterId': 120,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 4},\n", "         'SystemFunctionsValuePurposes': 'MoveDownFeedback'},\n", "        {'RegisterMetaData': {'RegisterId': 220,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 0},\n", "         'SystemFunctionsValuePurposes': 'ToggleAction'},\n", "        {'RegisterMetaData': {'RegisterId': 220,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 1},\n", "         'SystemFunctionsValuePurposes': 'MoveLeftAction'},\n", "        {'RegisterMetaData': {'RegisterId': 220,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 2},\n", "         'SystemFunctionsValuePurposes': 'MoveRightAction'},\n", "        {'RegisterMetaData': {'RegisterId': 220,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 3},\n", "         'SystemFunctionsValuePurposes': 'MoveUpAction'},\n", "        {'RegisterMetaData': {'RegisterId': 220,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 4},\n", "         'SystemFunctionsValuePurposes': 'MoveDownAction'}]}},\n", "     {'DomainDriverIdentification': {'SystemFunctionId': 'DownSpudPoleStern1',\n", "       'Description': 'Search light Stern PS ON/OFF'},\n", "      'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 120,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 8},\n", "         'SystemFunctionsValuePurposes': 'ActionFeedback'},\n", "        {'RegisterMetaData': {'RegisterId': 120,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 9},\n", "         'SystemFunctionsValuePurposes': 'MoveLeftFeedback'},\n", "        {'RegisterMetaData': {'RegisterId': 120,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 10},\n", "         'SystemFunctionsValuePurposes': 'MoveRightFeedback'},\n", "        {'RegisterMetaData': {'RegisterId': 120,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 11},\n", "         'SystemFunctionsValuePurposes': 'MoveUpFeedback'},\n", "        {'RegisterMetaData': {'RegisterId': 120,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 12},\n", "         'SystemFunctionsValuePurposes': 'MoveDownFeedback'},\n", "        {'RegisterMetaData': {'RegisterId': 220,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 8},\n", "         'SystemFunctionsValuePurposes': 'ToggleAction'},\n", "        {'RegisterMetaData': {'RegisterId': 220,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 9},\n", "         'SystemFunctionsValuePurposes': 'MoveLeftAction'},\n", "        {'RegisterMetaData': {'RegisterId': 220,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 10},\n", "         'SystemFunctionsValuePurposes': 'MoveRightAction'},\n", "        {'RegisterMetaData': {'RegisterId': 220,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 11},\n", "         'SystemFunctionsValuePurposes': 'MoveUpAction'},\n", "        {'RegisterMetaData': {'RegisterId': 220,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 12},\n", "         'SystemFunctionsValuePurposes': 'MoveDownAction'}]}},\n", "     {'DomainDriverIdentification': {'SystemFunctionId': 'DownSpudPoleBow1',\n", "       'Description': 'Search light Stern SB ON/OFF'},\n", "      'DomainSpecificConfig': {'TrackedRegisters': [{'RegisterMetaData': {'RegisterId': 121,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 0},\n", "         'SystemFunctionsValuePurposes': 'ActionFeedback'},\n", "        {'RegisterMetaData': {'RegisterId': 121,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 1},\n", "         'SystemFunctionsValuePurposes': 'MoveLeftFeedback'},\n", "        {'RegisterMetaData': {'RegisterId': 121,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 2},\n", "         'SystemFunctionsValuePurposes': 'MoveRightFeedback'},\n", "        {'RegisterMetaData': {'RegisterId': 121,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 3},\n", "         'SystemFunctionsValuePurposes': 'MoveUpFeedback'},\n", "        {'RegisterMetaData': {'RegisterId': 121,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 4},\n", "         'SystemFunctionsValuePurposes': 'MoveDownFeedback'},\n", "        {'RegisterMetaData': {'RegisterId': 221,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 0},\n", "         'SystemFunctionsValuePurposes': 'ToggleAction'},\n", "        {'RegisterMetaData': {'RegisterId': 221,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 1},\n", "         'SystemFunctionsValuePurposes': 'MoveLeftAction'},\n", "        {'RegisterMetaData': {'RegisterId': 221,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 2},\n", "         'SystemFunctionsValuePurposes': 'MoveRightAction'},\n", "        {'RegisterMetaData': {'RegisterId': 221,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 3},\n", "         'SystemFunctionsValuePurposes': 'MoveUpAction'},\n", "        {'RegisterMetaData': {'RegisterId': 221,\n", "          'RegisterType': 'HoldingRegister',\n", "          'RegisterValueType': 'Boolean'},\n", "         'RegisterParsingData': {'BitIndex': 4},\n", "         'SystemFunctionsValuePurposes': 'MoveDownAction'}]}}],\n", "    'AlarmDomainDrivers': [{'DomainDriverIdentification': {'AlarmMessage': 'Fire alarm system failure',\n", "       'NotificationGroupId': 'SafetySystems',\n", "       'EntityId': 'Fire Alarm',\n", "       'NotificationTypeId': 'Warning',\n", "       'WarningId': 'MiNavigationEquipmentDefect'},\n", "      'DomainSpecificConfig': {'RegisterMetaData': {'RegisterId': 102,\n", "        'RegisterType': 'HoldingRegister',\n", "        'RegisterValueType': 'Boolean'},\n", "       'RegisterParsingData': {'BitIndex': 2}}},\n", "     {'DomainDriverIdentification': {'AlarmMessage': 'Cannot sail with active red group',\n", "       'NotificationGroupId': 'PowerGeneral',\n", "       'EntityId': 'Red group',\n", "       'NotificationTypeId': 'Alarm',\n", "       'AlarmId': 'MiRedGroupActive'},\n", "      'DomainSpecificConfig': {'RegisterMetaData': {'RegisterId': 102,\n", "        'RegisterType': 'HoldingRegister',\n", "        'RegisterValueType': 'Boolean'},\n", "       'RegisterParsingData': {'BitIndex': 3}}},\n", "     {'DomainDriverIdentification': {'AlarmMessage': 'Generator 1 Pump Breaker Is Tripped',\n", "       'NotificationGroupId': 'PowerGeneral',\n", "       'EntityId': 'Generator 1 Breaker',\n", "       'NotificationTypeId': 'Alarm',\n", "       'AlarmId': 'MiGeneratorGeneral'},\n", "      'DomainSpecificConfig': {'RegisterMetaData': {'RegisterId': 103,\n", "        'RegisterType': 'HoldingRegister',\n", "        'RegisterValueType': 'Boolean'},\n", "       'RegisterParsingData': {'BitIndex': 4}}},\n", "     {'DomainDriverIdentification': {'AlarmMessage': 'Generator 1 Bow Thruster Breaker Is Tripped',\n", "       'NotificationGroupId': 'PowerGeneral',\n", "       'EntityId': 'Bow Thruster Breaker',\n", "       'NotificationTypeId': 'Alarm',\n", "       'AlarmId': 'MiGeneratorGeneral'},\n", "      'DomainSpecificConfig': {'RegisterMetaData': {'RegisterId': 103,\n", "        'RegisterType': 'HoldingRegister',\n", "        'RegisterValueType': 'Boolean'},\n", "       'RegisterParsingData': {'BitIndex': 5}}},\n", "     {'DomainDriverIdentification': {'AlarmMessage': 'Bow Thruster Overload Advice To Reduce Power',\n", "       'NotificationGroupId': 'Propulsion',\n", "       'EntityId': 'Bow Thruster',\n", "       'NotificationTypeId': 'Warning',\n", "       'WarningId': 'MiPropulsionOverspeed'},\n", "      'DomainSpecificConfig': {'RegisterMetaData': {'RegisterId': 103,\n", "        'RegisterType': 'HoldingRegister',\n", "        'RegisterValueType': 'Boolean'},\n", "       'RegisterParsingData': {'BitIndex': 6}}},\n", "     {'DomainDriverIdentification': {'AlarmMessage': 'Generator 2 Pump Breaker Is Tripped',\n", "       'NotificationGroupId': 'PowerSystems',\n", "       'EntityId': 'Generator 2 Pump Breaker',\n", "       'NotificationTypeId': 'Alarm',\n", "       'AlarmId': 'MiGeneratorGeneral'},\n", "      'DomainSpecificConfig': {'RegisterMetaData': {'RegisterId': 104,\n", "        'RegisterType': 'HoldingRegister',\n", "        'RegisterValueType': 'Boolean'},\n", "       'RegisterParsingData': {'BitIndex': 0}}},\n", "     {'DomainDriverIdentification': {'AlarmMessage': 'Generator 2 Boardnet Breaker Is Tripped',\n", "       'NotificationGroupId': 'PowerSystems',\n", "       'EntityId': 'Boardnet breaker',\n", "       'NotificationTypeId': 'Warning',\n", "       'WarningId': 'MiPowerGeneral'},\n", "      'DomainSpecificConfig': {'RegisterMetaData': {'RegisterId': 104,\n", "        'RegisterType': 'HoldingRegister',\n", "        'RegisterValueType': 'Boolean'},\n", "       'RegisterParsingData': {'BitIndex': 1}}},\n", "     {'DomainDriverIdentification': {'AlarmMessage': 'Emergency Stop Engine Room Fan Is Active',\n", "       'NotificationGroupId': 'SafetySystems',\n", "       'EntityId': 'Estop Engine Room Fan',\n", "       'NotificationTypeId': 'Warning',\n", "       'WarningId': 'MiExternalEmergencyStop'},\n", "      'DomainSpecificConfig': {'RegisterMetaData': {'RegisterId': 104,\n", "        'RegisterType': 'HoldingRegister',\n", "        'RegisterValueType': 'Boolean'},\n", "       'RegisterParsingData': {'BitIndex': 2}}},\n", "     {'DomainDriverIdentification': {'AlarmMessage': 'Pressure Too Low Overpressure System Engineroom',\n", "       'NotificationGroupId': 'SafetySystems',\n", "       'EntityId': 'Overpressure Engine Room',\n", "       'NotificationTypeId': 'Alarm',\n", "       'AlarmId': 'MiPressureOutOfBounds'},\n", "      'DomainSpecificConfig': {'RegisterMetaData': {'RegisterId': 104,\n", "        'RegisterType': 'HoldingRegister',\n", "        'RegisterValueType': 'Boolean'},\n", "       'RegisterParsingData': {'BitIndex': 3}}},\n", "     {'DomainDriverIdentification': {'AlarmMessage': 'Door Extinghuiser Installation Opened',\n", "       'NotificationGroupId': 'SafetySystems',\n", "       'EntityId': 'Door Extinghuiser Installation',\n", "       'NotificationTypeId': 'Warning',\n", "       'WarningId': 'Mi<PERSON><PERSON>O<PERSON>'},\n", "      'DomainSpecificConfig': {'RegisterMetaData': {'RegisterId': 104,\n", "        'RegisterType': 'HoldingRegister',\n", "        'RegisterValueType': 'Boolean'},\n", "       'RegisterParsingData': {'BitIndex': 4}}},\n", "     {'DomainDriverIdentification': {'AlarmMessage': 'Failure Synchronisation Module Generator 2',\n", "       'NotificationGroupId': 'PowerSystems',\n", "       'EntityId': 'Generator 2 Sync',\n", "       'NotificationTypeId': 'Alarm',\n", "       'AlarmId': 'MiGeneratorGeneral'},\n", "      'DomainSpecificConfig': {'RegisterMetaData': {'RegisterId': 105,\n", "        'RegisterType': 'HoldingRegister',\n", "        'RegisterValueType': 'Boolean'},\n", "       'RegisterParsingData': {'BitIndex': 1}}},\n", "     {'DomainDriverIdentification': {'AlarmMessage': 'Pressure Of Extinghuiser Installation To Low',\n", "       'NotificationGroupId': 'SafetySystems',\n", "       'EntityId': 'Extinguisher installation',\n", "       'NotificationTypeId': 'Warning',\n", "       'WarningId': 'MiPressureOutOfBounds'},\n", "      'DomainSpecificConfig': {'RegisterMetaData': {'RegisterId': 105,\n", "        'RegisterType': 'HoldingRegister',\n", "        'RegisterValueType': 'Boolean'},\n", "       'RegisterParsingData': {'BitIndex': 2}}},\n", "     {'DomainDriverIdentification': {'AlarmMessage': 'Alarm Present On Generator 1',\n", "       'NotificationGroupId': 'PowerSystems',\n", "       'EntityId': 'Generator 1',\n", "       'NotificationTypeId': 'Alarm',\n", "       'AlarmId': 'MiGeneratorGeneral'},\n", "      'DomainSpecificConfig': {'RegisterMetaData': {'RegisterId': 105,\n", "        'RegisterType': 'HoldingRegister',\n", "        'RegisterValueType': 'Boolean'},\n", "       'RegisterParsingData': {'BitIndex': 3}}},\n", "     {'DomainDriverIdentification': {'AlarmMessage': 'Alarm Present On Generator 2',\n", "       'NotificationGroupId': 'PowerSystems',\n", "       'EntityId': 'Generator 2',\n", "       'NotificationTypeId': 'Alarm',\n", "       'AlarmId': 'MiGeneratorGeneral'},\n", "      'DomainSpecificConfig': {'RegisterMetaData': {'RegisterId': 105,\n", "        'RegisterType': 'HoldingRegister',\n", "        'RegisterValueType': 'Boolean'},\n", "       'RegisterParsingData': {'BitIndex': 4}}},\n", "     {'DomainDriverIdentification': {'AlarmMessage': 'Bilge Level High Inside Engine Room',\n", "       'NotificationGroupId': 'SafetySystems',\n", "       'EntityId': 'Engine Room Bow',\n", "       'NotificationTypeId': 'Alarm',\n", "       'AlarmId': 'MiBilgeGeneral'},\n", "      'DomainSpecificConfig': {'RegisterMetaData': {'RegisterId': 105,\n", "        'RegisterType': 'HoldingRegister',\n", "        'RegisterValueType': 'Boolean'},\n", "       'RegisterParsingData': {'BitIndex': 5}}},\n", "     {'DomainDriverIdentification': {'AlarmMessage': 'Level Inside Fuel Tank Is Too Low',\n", "       'NotificationGroupId': 'PowerGeneral',\n", "       'EntityId': 'Fuel Tank Bow',\n", "       'NotificationTypeId': 'Warning',\n", "       'WarningId': 'MiFuelOutOfBounds'},\n", "      'DomainSpecificConfig': {'RegisterMetaData': {'RegisterId': 105,\n", "        'RegisterType': 'HoldingRegister',\n", "        'RegisterValueType': 'Boolean'},\n", "       'RegisterParsingData': {'BitIndex': 6}}},\n", "     {'DomainDriverIdentification': {'AlarmMessage': 'Level Inside Fuel Tank Is Too High',\n", "       'NotificationGroupId': 'PowerSystems',\n", "       'EntityId': 'Fuel Tank Bow',\n", "       'NotificationTypeId': 'Warning',\n", "       'WarningId': 'MiFuelOutOfBounds'},\n", "      'DomainSpecificConfig': {'RegisterMetaData': {'RegisterId': 105,\n", "        'RegisterType': 'HoldingRegister',\n", "        'RegisterValueType': 'Boolean'},\n", "       'RegisterParsingData': {'BitIndex': 8}}},\n", "     {'DomainDriverIdentification': {'AlarmMessage': 'Alarm Compressor',\n", "       'NotificationGroupId': 'ControlSystemGeneral',\n", "       'EntityId': 'Alarm Compressor',\n", "       'NotificationTypeId': 'Alarm',\n", "       'AlarmId': 'MiPressureOutOfBounds'},\n", "      'DomainSpecificConfig': {'RegisterMetaData': {'RegisterId': 105,\n", "        'RegisterType': 'HoldingRegister',\n", "        'RegisterValueType': 'Boolean'},\n", "       'RegisterParsingData': {'BitIndex': 10}}},\n", "     {'DomainDriverIdentification': {'AlarmMessage': 'Alarm Generator 1 Aftertreatment System',\n", "       'NotificationGroupId': 'PowerGeneral',\n", "       'EntityId': 'Aftertreatment Generator 1',\n", "       'NotificationTypeId': 'Alarm',\n", "       'AlarmId': 'MiGeneratorGeneral'},\n", "      'DomainSpecificConfig': {'RegisterMetaData': {'RegisterId': 105,\n", "        'RegisterType': 'HoldingRegister',\n", "        'RegisterValueType': 'Boolean'},\n", "       'RegisterParsingData': {'BitIndex': 11}}},\n", "     {'DomainDriverIdentification': {'AlarmMessage': 'Generator 3 Breaker Is Tripped',\n", "       'NotificationGroupId': 'PowerSystems',\n", "       'EntityId': 'Generator 3 Breaker',\n", "       'NotificationTypeId': 'Alarm',\n", "       'AlarmId': 'MiGeneratorGeneral'},\n", "      'DomainSpecificConfig': {'RegisterMetaData': {'RegisterId': 106,\n", "        'RegisterType': 'HoldingRegister',\n", "        'RegisterValueType': 'Boolean'},\n", "       'RegisterParsingData': {'BitIndex': 1}}},\n", "     {'DomainDriverIdentification': {'AlarmMessage': 'Alarm Present On Generator 3',\n", "       'NotificationGroupId': 'PowerSystems',\n", "       'EntityId': 'Generator 3',\n", "       'NotificationTypeId': 'Alarm',\n", "       'AlarmId': 'MiGeneratorGeneral'},\n", "      'DomainSpecificConfig': {'RegisterMetaData': {'RegisterId': 106,\n", "        'RegisterType': 'HoldingRegister',\n", "        'RegisterValueType': 'Boolean'},\n", "       'RegisterParsingData': {'BitIndex': 2}}},\n", "     {'DomainDriverIdentification': {'AlarmMessage': 'Failure Synchronisation Module Generator 3',\n", "       'NotificationGroupId': 'PowerGeneral',\n", "       'EntityId': 'Generator 3 Sync',\n", "       'NotificationTypeId': 'Alarm',\n", "       'AlarmId': 'MiGeneratorGeneral'},\n", "      'DomainSpecificConfig': {'RegisterMetaData': {'RegisterId': 106,\n", "        'RegisterType': 'HoldingRegister',\n", "        'RegisterValueType': 'Boolean'},\n", "       'RegisterParsingData': {'BitIndex': 3}}},\n", "     {'DomainDriverIdentification': {'AlarmMessage': 'Emergency Stop Engine Room Fan Is Active',\n", "       'NotificationGroupId': 'PowerGeneral',\n", "       'EntityId': 'Estop Engine Room Fan',\n", "       'NotificationTypeId': 'Warning',\n", "       'WarningId': 'MiExternalEmergencyStop'},\n", "      'DomainSpecificConfig': {'RegisterMetaData': {'RegisterId': 106,\n", "        'RegisterType': 'HoldingRegister',\n", "        'RegisterValueType': 'Boolean'},\n", "       'RegisterParsingData': {'BitIndex': 4}}},\n", "     {'DomainDriverIdentification': {'AlarmMessage': 'Pressure Too Low Overpressure System Wheelhouse',\n", "       'NotificationGroupId': 'ControlSystemGeneral',\n", "       'EntityId': 'Wheelhouse',\n", "       'NotificationTypeId': 'Warning',\n", "       'WarningId': 'MiPressureOutOfBounds'},\n", "      'DomainSpecificConfig': {'RegisterMetaData': {'RegisterId': 106,\n", "        'RegisterType': 'HoldingRegister',\n", "        'RegisterValueType': 'Boolean'},\n", "       'RegisterParsingData': {'BitIndex': 5}}},\n", "     {'DomainDriverIdentification': {'AlarmMessage': 'Pressure Too Low Overpressure System Accommodation',\n", "       'NotificationGroupId': 'SafetySystems',\n", "       'EntityId': 'Accommodation',\n", "       'NotificationTypeId': 'Warning',\n", "       'WarningId': 'MiPressureOutOfBounds'},\n", "      'DomainSpecificConfig': {'RegisterMetaData': {'RegisterId': 106,\n", "        'RegisterType': 'HoldingRegister',\n", "        'RegisterValueType': 'Boolean'},\n", "       'RegisterParsingData': {'BitIndex': 6}}},\n", "     {'DomainDriverIdentification': {'AlarmMessage': 'Pressure Too Low Overpressure System Engineroom',\n", "       'NotificationGroupId': 'SafetySystems',\n", "       'EntityId': 'Engine Room',\n", "       'NotificationTypeId': 'Warning',\n", "       'WarningId': 'MiPressureOutOfBounds'},\n", "      'DomainSpecificConfig': {'RegisterMetaData': {'RegisterId': 106,\n", "        'RegisterType': 'HoldingRegister',\n", "        'RegisterValueType': 'Boolean'},\n", "       'RegisterParsingData': {'BitIndex': 7}}},\n", "     {'DomainDriverIdentification': {'AlarmMessage': 'Supply Failure Wheelhouse Lift Control',\n", "       'NotificationGroupId': 'SafetySystems',\n", "       'EntityId': 'Supply Wheelhouse',\n", "       'NotificationTypeId': 'Alarm',\n", "       'AlarmId': 'MiPowerGeneral'},\n", "      'DomainSpecificConfig': {'RegisterMetaData': {'RegisterId': 106,\n", "        'RegisterType': 'HoldingRegister',\n", "        'RegisterValueType': 'Boolean'},\n", "       'RegisterParsingData': {'BitIndex': 8}}},\n", "     {'DomainDriverIdentification': {'AlarmMessage': 'Main Battery Voltage Is Too Low',\n", "       'NotificationGroupId': 'SafetySystems',\n", "       'EntityId': 'Main Battery',\n", "       'NotificationTypeId': 'Alarm',\n", "       'AlarmId': 'MiBatteryLowVoltage'},\n", "      'DomainSpecificConfig': {'RegisterMetaData': {'RegisterId': 106,\n", "        'RegisterType': 'HoldingRegister',\n", "        'RegisterValueType': 'Boolean'},\n", "       'RegisterParsingData': {'BitIndex': 9}}},\n", "     {'DomainDriverIdentification': {'AlarmMessage': 'Door Extinghuiser Installation Opened',\n", "       'NotificationGroupId': 'SafetySystems',\n", "       'EntityId': 'Door Extinghuiser Installation',\n", "       'NotificationTypeId': 'Warning',\n", "       'WarningId': 'Mi<PERSON><PERSON>O<PERSON>'},\n", "      'DomainSpecificConfig': {'RegisterMetaData': {'RegisterId': 106,\n", "        'RegisterType': 'HoldingRegister',\n", "        'RegisterValueType': 'Boolean'},\n", "       'RegisterParsingData': {'BitIndex': 10}}},\n", "     {'DomainDriverIdentification': {'AlarmMessage': 'Supply Failure Gasdetection System',\n", "       'NotificationGroupId': 'SafetySystems',\n", "       'EntityId': 'Gas detection supply',\n", "       'NotificationTypeId': 'Warning',\n", "       'WarningId': 'MiGasDetection'},\n", "      'DomainSpecificConfig': {'RegisterMetaData': {'RegisterId': 106,\n", "        'RegisterType': 'HoldingRegister',\n", "        'RegisterValueType': 'Boolean'},\n", "       'RegisterParsingData': {'BitIndex': 12}}},\n", "     {'DomainDriverIdentification': {'AlarmMessage': 'Pressure Too Low Extinghuiser System SB',\n", "       'NotificationGroupId': 'SafetySystems',\n", "       'EntityId': 'Extinghuiser system SB',\n", "       'NotificationTypeId': 'Warning',\n", "       'WarningId': 'MiPressureOutOfBounds'},\n", "      'DomainSpecificConfig': {'RegisterMetaData': {'RegisterId': 106,\n", "        'RegisterType': 'HoldingRegister',\n", "        'RegisterValueType': 'Boolean'},\n", "       'RegisterParsingData': {'BitIndex': 13}}},\n", "     {'DomainDriverIdentification': {'AlarmMessage': 'Bilge Level High Inside Engine Room',\n", "       'NotificationGroupId': 'SafetySystems',\n", "       'EntityId': 'Bilge Engine Room',\n", "       'NotificationTypeId': 'Alarm',\n", "       'AlarmId': 'MiBilgeGeneral'},\n", "      'DomainSpecificConfig': {'RegisterMetaData': {'RegisterId': 106,\n", "        'RegisterType': 'HoldingRegister',\n", "        'RegisterValueType': 'Boolean'},\n", "       'RegisterParsingData': {'BitIndex': 14}}},\n", "     {'DomainDriverIdentification': {'AlarmMessage': 'Bilge Level High Inside Wheelhouse Column',\n", "       'NotificationGroupId': 'Propulsion',\n", "       'EntityId': 'Bigle Wheelhouse Column',\n", "       'NotificationTypeId': 'Alarm',\n", "       'AlarmId': 'MiBilgeGeneral'},\n", "      'DomainSpecificConfig': {'RegisterMetaData': {'RegisterId': 106,\n", "        'RegisterType': 'HoldingRegister',\n", "        'RegisterValueType': 'Boolean'},\n", "       'RegisterParsingData': {'BitIndex': 15}}},\n", "     {'DomainDriverIdentification': {'AlarmMessage': 'Level Inside Fuel Tank PS Is Too Low',\n", "       'NotificationGroupId': 'PowerSystems',\n", "       'EntityId': 'Fuel Tank PS',\n", "       'NotificationTypeId': 'Warning',\n", "       'WarningId': 'MiFuelOutOfBounds'},\n", "      'DomainSpecificConfig': {'RegisterMetaData': {'RegisterId': 107,\n", "        'RegisterType': 'HoldingRegister',\n", "        'RegisterValueType': 'Boolean'},\n", "       'RegisterParsingData': {'BitIndex': 1}}},\n", "     {'DomainDriverIdentification': {'AlarmMessage': 'Level Inside Fuel Tank SB Is Too Low',\n", "       'NotificationGroupId': 'PowerSystems',\n", "       'EntityId': 'Fuel Tank SB',\n", "       'NotificationTypeId': 'Warning',\n", "       'WarningId': 'MiFuelOutOfBounds'},\n", "      'DomainSpecificConfig': {'RegisterMetaData': {'RegisterId': 107,\n", "        'RegisterType': 'HoldingRegister',\n", "        'RegisterValueType': 'Boolean'},\n", "       'RegisterParsingData': {'BitIndex': 2}}},\n", "     {'DomainDriverIdentification': {'AlarmMessage': 'Level Inside Fuel Tank PS Is Too High',\n", "       'NotificationGroupId': 'PowerSystems',\n", "       'EntityId': 'Fuel Tank PS',\n", "       'NotificationTypeId': 'Warning',\n", "       'WarningId': 'MiFuelOutOfBounds'},\n", "      'DomainSpecificConfig': {'RegisterMetaData': {'RegisterId': 107,\n", "        'RegisterType': 'HoldingRegister',\n", "        'RegisterValueType': 'Boolean'},\n", "       'RegisterParsingData': {'BitIndex': 3}}},\n", "     {'DomainDriverIdentification': {'AlarmMessage': 'Level Inside Fuel Tank SB Is Too High',\n", "       'NotificationGroupId': 'PowerSystems',\n", "       'EntityId': 'Fuel Tank SB',\n", "       'NotificationTypeId': 'Warning',\n", "       'WarningId': 'MiFuelOutOfBounds'},\n", "      'DomainSpecificConfig': {'RegisterMetaData': {'RegisterId': 107,\n", "        'RegisterType': 'HoldingRegister',\n", "        'RegisterValueType': 'Boolean'},\n", "       'RegisterParsingData': {'BitIndex': 4}}},\n", "     {'DomainDriverIdentification': {'AlarmMessage': 'Shaft Lubrication Header Tank PS Level Too Low',\n", "       'NotificationGroupId': 'Propulsion',\n", "       'EntityId': 'Shaft Lubrication PS',\n", "       'NotificationTypeId': 'Alarm',\n", "       'AlarmId': 'MiTankOutOfBounds'},\n", "      'DomainSpecificConfig': {'RegisterMetaData': {'RegisterId': 107,\n", "        'RegisterType': 'HoldingRegister',\n", "        'RegisterValueType': 'Boolean'},\n", "       'RegisterParsingData': {'BitIndex': 5}}},\n", "     {'DomainDriverIdentification': {'AlarmMessage': 'Shaft Brake Installation PS Is Active (Brake Is On)',\n", "       'NotificationGroupId': 'Propulsion',\n", "       'EntityId': 'Shaft Brake PS',\n", "       'NotificationTypeId': 'Alarm',\n", "       'AlarmId': 'MiPropulsionNotReady'},\n", "      'DomainSpecificConfig': {'RegisterMetaData': {'RegisterId': 107,\n", "        'RegisterType': 'HoldingRegister',\n", "        'RegisterValueType': 'Boolean'},\n", "       'RegisterParsingData': {'BitIndex': 6}}},\n", "     {'DomainDriverIdentification': {'AlarmMessage': 'Shaft Lubrication Header Tank SB Level Too Low',\n", "       'NotificationGroupId': 'Propulsion',\n", "       'EntityId': 'Shaft Lubrication SB',\n", "       'NotificationTypeId': 'Alarm',\n", "       'AlarmId': 'MiTankOutOfBounds'},\n", "      'DomainSpecificConfig': {'RegisterMetaData': {'RegisterId': 107,\n", "        'RegisterType': 'HoldingRegister',\n", "        'RegisterValueType': 'Boolean'},\n", "       'RegisterParsingData': {'BitIndex': 7}}},\n", "     {'DomainDriverIdentification': {'AlarmMessage': 'Shaft Brake Installation SB Is Active (Brake Is On)',\n", "       'NotificationGroupId': 'Propulsion',\n", "       'EntityId': 'Shaft Brake SB',\n", "       'NotificationTypeId': 'Alarm',\n", "       'AlarmId': 'MiPropulsionNotReady'},\n", "      'DomainSpecificConfig': {'RegisterMetaData': {'RegisterId': 107,\n", "        'RegisterType': 'HoldingRegister',\n", "        'RegisterValueType': 'Boolean'},\n", "       'RegisterParsingData': {'BitIndex': 8}}},\n", "     {'DomainDriverIdentification': {'AlarmMessage': 'Door Wheelhouse Elevator Opened',\n", "       'NotificationGroupId': 'SafetySystems',\n", "       'EntityId': 'Door Wheelhouse Elevator',\n", "       'NotificationTypeId': 'Warning',\n", "       'WarningId': 'Mi<PERSON><PERSON>O<PERSON>'},\n", "      'DomainSpecificConfig': {'RegisterMetaData': {'RegisterId': 107,\n", "        'RegisterType': 'HoldingRegister',\n", "        'RegisterValueType': 'Boolean'},\n", "       'RegisterParsingData': {'BitIndex': 9}}},\n", "     {'DomainDriverIdentification': {'AlarmMessage': 'Pressure Too Low Extinghuiser System PS',\n", "       'NotificationGroupId': 'PowerGeneral',\n", "       'EntityId': 'Extinghuiser system PS',\n", "       'NotificationTypeId': 'Warning',\n", "       'WarningId': 'MiPressureOutOfBounds'},\n", "      'DomainSpecificConfig': {'RegisterMetaData': {'RegisterId': 107,\n", "        'RegisterType': 'HoldingRegister',\n", "        'RegisterValueType': 'Boolean'},\n", "       'RegisterParsingData': {'BitIndex': 10}}},\n", "     {'DomainDriverIdentification': {'AlarmMessage': 'Failure Aftertreatment System Mainengine PS',\n", "       'NotificationGroupId': 'PowerGeneral',\n", "       'EntityId': 'Aftertreatment Main Engine PS',\n", "       'NotificationTypeId': 'Warning',\n", "       'WarningId': 'MiPropulsionNotReady'},\n", "      'DomainSpecificConfig': {'RegisterMetaData': {'RegisterId': 107,\n", "        'RegisterType': 'HoldingRegister',\n", "        'RegisterValueType': 'Boolean'},\n", "       'RegisterParsingData': {'BitIndex': 11}}},\n", "     {'DomainDriverIdentification': {'AlarmMessage': 'Failure Aftertreatment System Mainengine SB',\n", "       'NotificationGroupId': 'PowerSystems',\n", "       'EntityId': 'Aftertreatment Main Engine SB',\n", "       'NotificationTypeId': 'Warning',\n", "       'WarningId': 'MiPropulsionNotReady'},\n", "      'DomainSpecificConfig': {'RegisterMetaData': {'RegisterId': 107,\n", "        'RegisterType': 'HoldingRegister',\n", "        'RegisterValueType': 'Boolean'},\n", "       'RegisterParsingData': {'BitIndex': 12}}},\n", "     {'DomainDriverIdentification': {'AlarmMessage': 'Isolation Fault 24Vdc Main batteries',\n", "       'NotificationGroupId': 'PowerSystems',\n", "       'EntityId': '24Vdc Main Batteries',\n", "       'NotificationTypeId': 'Warning',\n", "       'WarningId': 'MiPowerGeneral'},\n", "      'DomainSpecificConfig': {'RegisterMetaData': {'RegisterId': 109,\n", "        'RegisterType': 'HoldingRegister',\n", "        'RegisterValueType': 'Boolean'},\n", "       'RegisterParsingData': {'BitIndex': 3}}},\n", "     {'DomainDriverIdentification': {'AlarmMessage': 'General Failure Charger/Inverter 24Vdc Installation',\n", "       'NotificationGroupId': 'PowerSystems',\n", "       'EntityId': 'Inverter 24V DC',\n", "       'NotificationTypeId': 'Warning',\n", "       'WarningId': 'MiPowerGeneral'},\n", "      'DomainSpecificConfig': {'RegisterMetaData': {'RegisterId': 109,\n", "        'RegisterType': 'HoldingRegister',\n", "        'RegisterValueType': 'Boolean'},\n", "       'RegisterParsingData': {'BitIndex': 4}}},\n", "     {'DomainDriverIdentification': {'AlarmMessage': 'Voltage Failure Emergency Controls Wheelhouse',\n", "       'NotificationGroupId': 'ControlSystemGeneral',\n", "       'EntityId': 'Emergency Controls Wheelhouse',\n", "       'NotificationTypeId': 'Alarm',\n", "       'AlarmId': 'MiPowerGeneral'},\n", "      'DomainSpecificConfig': {'RegisterMetaData': {'RegisterId': 109,\n", "        'RegisterType': 'HoldingRegister',\n", "        'RegisterValueType': 'Boolean'},\n", "       'RegisterParsingData': {'BitIndex': 5}}},\n", "     {'DomainDriverIdentification': {'AlarmMessage': 'Failure Navigation Lights',\n", "       'NotificationGroupId': 'NavigationEquipment',\n", "       'EntityId': 'Navigation Lights',\n", "       'NotificationTypeId': 'Warning',\n", "       'WarningId': 'MiNavigationEquipmentDefect'},\n", "      'DomainSpecificConfig': {'RegisterMetaData': {'RegisterId': 109,\n", "        'RegisterType': 'HoldingRegister',\n", "        'RegisterValueType': 'Boolean'},\n", "       'RegisterParsingData': {'BitIndex': 6}}},\n", "     {'DomainDriverIdentification': {'AlarmMessage': 'Failure 24Vdc Navigation Lights',\n", "       'NotificationGroupId': 'NavigationEquipment',\n", "       'EntityId': '24Vdc Navigation Lights',\n", "       'NotificationTypeId': 'Warning',\n", "       'WarningId': 'MiPowerGeneral'},\n", "      'DomainSpecificConfig': {'RegisterMetaData': {'RegisterId': 109,\n", "        'RegisterType': 'HoldingRegister',\n", "        'RegisterValueType': 'Boolean'},\n", "       'RegisterParsingData': {'BitIndex': 7}}},\n", "     {'DomainDriverIdentification': {'AlarmMessage': 'Fire / Failure From Firedetection Unit (See Unit)',\n", "       'NotificationGroupId': 'SafetySystems',\n", "       'EntityId': 'Firedetection Unit',\n", "       'NotificationTypeId': 'Alarm',\n", "       'AlarmId': 'MiFireGeneral'},\n", "      'DomainSpecificConfig': {'RegisterMetaData': {'RegisterId': 109,\n", "        'RegisterType': 'HoldingRegister',\n", "        'RegisterValueType': 'Boolean'},\n", "       'RegisterParsingData': {'BitIndex': 8}}},\n", "     {'DomainDriverIdentification': {'AlarmMessage': 'Failure 400Vac Boardnet Supply',\n", "       'NotificationGroupId': 'PowerSystems',\n", "       'EntityId': '400Vac Boardnet Supply',\n", "       'NotificationTypeId': 'Alarm',\n", "       'AlarmId': 'MiPowerGeneral'},\n", "      'DomainSpecificConfig': {'RegisterMetaData': {'RegisterId': 109,\n", "        'RegisterType': 'HoldingRegister',\n", "        'RegisterValueType': 'Boolean'},\n", "       'RegisterParsingData': {'BitIndex': 9}}},\n", "     {'DomainDriverIdentification': {'AlarmMessage': 'Failure 24Vdc Backup Supply Wheelhouse',\n", "       'NotificationGroupId': 'PowerSystems',\n", "       'EntityId': '24Vdc Wheelhouse backup',\n", "       'NotificationTypeId': 'Warning',\n", "       'WarningId': 'MiPowerGeneral'},\n", "      'DomainSpecificConfig': {'RegisterMetaData': {'RegisterId': 109,\n", "        'RegisterType': 'HoldingRegister',\n", "        'RegisterValueType': 'Boolean'},\n", "       'RegisterParsingData': {'BitIndex': 10}}},\n", "     {'DomainDriverIdentification': {'AlarmMessage': 'Steering Watch Alarm',\n", "       'NotificationGroupId': 'PowerSystems',\n", "       'EntityId': 'Steering Watch Alarm',\n", "       'NotificationTypeId': 'Warning',\n", "       'WarningId': 'MiPowerGeneral'},\n", "      'DomainSpecificConfig': {'RegisterMetaData': {'RegisterId': 109,\n", "        'RegisterType': 'HoldingRegister',\n", "        'RegisterValueType': 'Boolean'},\n", "       'RegisterParsingData': {'BitIndex': 11}}},\n", "     {'DomainDriverIdentification': {'AlarmMessage': 'Voltage Failure 24Vdc PLC System',\n", "       'NotificationGroupId': 'ControlSystemPLC',\n", "       'EntityId': '24Vdc Plc system',\n", "       'NotificationTypeId': 'Warning',\n", "       'WarningId': 'MiPowerGeneral'},\n", "      'DomainSpecificConfig': {'RegisterMetaData': {'RegisterId': 111,\n", "        'RegisterType': 'HoldingRegister',\n", "        'RegisterValueType': 'Boolean'},\n", "       'RegisterParsingData': {'BitIndex': 0}}},\n", "     {'DomainDriverIdentification': {'AlarmMessage': 'General Failure EtherNET System',\n", "       'NotificationGroupId': 'Communication',\n", "       'EntityId': 'Werkina Ethernet System',\n", "       'NotificationTypeId': 'Alarm',\n", "       'AlarmId': 'MiCommunicationGeneral'},\n", "      'DomainSpecificConfig': {'RegisterMetaData': {'RegisterId': 111,\n", "        'RegisterType': 'HoldingRegister',\n", "        'RegisterValueType': 'Boolean'},\n", "       'RegisterParsingData': {'BitIndex': 2}}},\n", "     {'DomainDriverIdentification': {'AlarmMessage': 'Communication Lost With IO Station Wheelhouse',\n", "       'NotificationGroupId': 'Communication',\n", "       'EntityId': 'IO Wheelhouse',\n", "       'NotificationTypeId': 'Warning',\n", "       'WarningId': 'MiCommunicationGeneral'},\n", "      'DomainSpecificConfig': {'RegisterMetaData': {'RegisterId': 111,\n", "        'RegisterType': 'HoldingRegister',\n", "        'RegisterValueType': 'Boolean'},\n", "       'RegisterParsingData': {'BitIndex': 3}}},\n", "     {'DomainDriverIdentification': {'AlarmMessage': 'Communication Lost With IO Station Engine Room Fore',\n", "       'NotificationGroupId': 'Communication',\n", "       'EntityId': 'IO Engine Room',\n", "       'NotificationTypeId': 'Warning',\n", "       'WarningId': 'MiCommunicationGeneral'},\n", "      'DomainSpecificConfig': {'RegisterMetaData': {'RegisterId': 111,\n", "        'RegisterType': 'HoldingRegister',\n", "        'RegisterValueType': 'Boolean'},\n", "       'RegisterParsingData': {'BitIndex': 4}}},\n", "     {'DomainDriverIdentification': {'AlarmMessage': 'Communication Lost With Frequency Converter Slobpump',\n", "       'NotificationGroupId': 'Communication',\n", "       'EntityId': 'Frequency Converter Slobpump',\n", "       'NotificationTypeId': 'Warning',\n", "       'WarningId': 'MiCommunicationGeneral'},\n", "      'DomainSpecificConfig': {'RegisterMetaData': {'RegisterId': 111,\n", "        'RegisterType': 'HoldingRegister',\n", "        'RegisterValueType': 'Boolean'},\n", "       'RegisterParsingData': {'BitIndex': 5}}},\n", "     {'DomainDriverIdentification': {'AlarmMessage': 'Communication Lost With Seafar',\n", "       'NotificationGroupId': 'Communication',\n", "       'EntityId': 'Werkina Plc',\n", "       'NotificationTypeId': 'Alarm',\n", "       'AlarmId': 'MiCommunicationGeneral'},\n", "      'DomainSpecificConfig': {'RegisterMetaData': {'RegisterId': 111,\n", "        'RegisterType': 'HoldingRegister',\n", "        'RegisterValueType': 'Boolean'},\n", "       'RegisterParsingData': {'BitIndex': 6}}},\n", "     {'DomainDriverIdentification': {'AlarmMessage': 'Communication Lost With Werkina HMI Wheelhouse',\n", "       'NotificationGroupId': 'Communication',\n", "       'EntityId': '<PERSON><PERSON><PERSON> Hmi',\n", "       'NotificationTypeId': 'Warning',\n", "       'WarningId': 'MiCommunicationGeneral'},\n", "      'DomainSpecificConfig': {'RegisterMetaData': {'RegisterId': 111,\n", "        'RegisterType': 'HoldingRegister',\n", "        'RegisterValueType': 'Boolean'},\n", "       'RegisterParsingData': {'BitIndex': 7}}}],\n", "    'HeartbeatRegister': {'RegisterMetaData': {'RegisterId': 201,\n", "      'RegisterType': 'HoldingRegister',\n", "      'RegisterValueType': 'Boolean'},\n", "     'RegisterParsingData': {'BitIndex': 0}}},\n", "   'ConnectionHandlerConfig': {'protocol': 'modbustcpclient',\n", "    'connectionAddress': '**************',\n", "    'connectionAddressOption': 502},\n", "   'Pipelines': ['Automation']}]}"]}, "execution_count": 51, "metadata": {}, "output_type": "execute_result"}], "source": ["amp_config"]}, {"cell_type": "code", "execution_count": 52, "id": "87293dd2", "metadata": {}, "outputs": [], "source": ["with open('amp_config.json', 'w') as f:\n", "    json.dump(amp_config, f, indent=4)\n", "with open('PropulsionSystemStateWatcherConfigs.json', 'w') as f:\n", "    json.dump(propulsion_pulse_configs, f, indent=4)\n", "\n", "with open('SystemFunctionsSystemStateWatcherConfigs.json', 'w') as f:\n", "    json.dump(system_functions_pulse_configs, f, indent=4)"]}, {"cell_type": "code", "execution_count": null, "id": "17b3dcdc-8ae1-49aa-a09d-c8a18c4d129e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d8174f24-9a09-4138-a36a-57dad1acfca2", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "44e18b2f-a3b5-4dbc-b973-940811c5ffd0", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "2768a991-f677-4294-8e9b-fd44a7d7c384", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "64d0e42a-9d09-452a-9c6d-ce258d5563e8", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}