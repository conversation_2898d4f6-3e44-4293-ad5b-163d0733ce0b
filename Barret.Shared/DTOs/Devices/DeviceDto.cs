using Barret.Core.Areas.Devices.Enums;
using Barret.Shared.DTOs.Devices.Alarms;

namespace Barret.Shared.DTOs.Devices
{
    /// <summary>
    /// Data transfer object for a device.
    /// </summary>
    public class DeviceDto
    {
        /// <summary>
        /// Gets or sets the unique identifier of the device.
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// Gets or sets the name of the device.
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the role of the device.
        /// </summary>
        public DeviceRole DeviceRole { get; set; } = DeviceRole.Undefined;

        /// <summary>
        /// Gets or sets the ID of the device model, if any.
        /// </summary>
        public Guid? DeviceModelId { get; set; }

        /// <summary>
        /// Gets or sets the name of the device model, if any.
        /// </summary>
        public string? ModelName { get; set; }

        /// <summary>
        /// Gets or sets the ID of the manufacturer, if any.
        /// </summary>
        public Guid? ManufacturerId { get; set; }

        /// <summary>
        /// Gets or sets the name of the manufacturer, if any.
        /// </summary>
        public string? ManufacturerName { get; set; }

        /// <summary>
        /// Gets or sets the 3D position of the device (for visualization).
        /// </summary>
        public RelativePositionDto Position { get; set; } = new();

        /// <summary>
        /// Gets or sets the maritime position of the device (for configuration).
        /// Only applicable to certain device types like engines, radars, etc.
        /// </summary>
        public MaritimePositionDto? MaritimePosition { get; set; }

        /// <summary>
        /// Gets or sets the connections from this device to other devices.
        /// </summary>
        public List<DeviceConnectionDto> Connections { get; set; } = [];

        /// <summary>
        /// Gets or sets the connection details of the device.
        /// </summary>
        public ConnectionHandlerDto Connection { get; set; } = new();

        /// <summary>
        /// Gets or sets the alarms of the device.
        /// </summary>
        public List<AlarmDto> Alarms { get; set; } = [];

        /// <summary>
        /// Gets or sets the ID of the vehicle that owns this device, if any.
        /// </summary>
        public Guid? VehicleId { get; set; }

        /// <summary>
        /// Gets or sets the name of the device group this device belongs to.
        /// This maintains the relationship between the device and its group.
        /// </summary>
        public string DeviceGroupName { get; set; } = string.Empty;

        /// <summary>
        /// Gets a value indicating whether this device has a model.
        /// </summary>
        public bool HasModel => DeviceModelId.HasValue;
    }
}