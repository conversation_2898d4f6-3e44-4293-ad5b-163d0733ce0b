using Barret.Core.Areas.DeviceGroups;
using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.DeviceGroups.AntennaGroup;
using Barret.Core.Areas.DeviceGroups.AudioGroup;
using Barret.Core.Areas.DeviceGroups.CameraGroup;
using Barret.Core.Areas.DeviceGroups.HornGroup;
using Barret.Core.Areas.DeviceGroups.LightGroup;
using Barret.Core.Areas.DeviceGroups.Propulsion.EngineGroup;
using Barret.Core.Areas.DeviceGroups.Propulsion.ThrusterGroup;
using Barret.Core.Areas.DeviceGroups.RadarGroup;
using Barret.Core.Areas.DeviceGroups.RadioGroup;
using Barret.Core.Areas.DeviceGroups.RudderGroup;
using Barret.Core.Areas.DeviceGroups.Seafar.NetworkGroup;
using Barret.Core.Areas.DeviceGroups.Seafar.VCSGroup;
using Barret.Core.Areas.DeviceGroups.SensorGroup;
using Barret.Core.Areas.DeviceGroups.TrackpilotGroup;
using Barret.Shared.DTOs.Devices;
using Barret.Shared.DTOs.Vehicles;
using Barret.Shared.DTOs.Vehicles.Vessels;

namespace Barret.Shared.Factories
{
    /// <summary>
    /// Factory for creating vehicle DTOs
    /// </summary>
    public static class VehicleDTOFactory
    {
        /// <summary>
        /// Creates a new VesselDto with all device groups properly initialized
        /// </summary>
        /// <returns>A new VesselDto with default values and initialized device groups</returns>
        public static VesselDto CreateVesselDto()
        {
            var vessel = new VesselDto
            {
                Id = Guid.NewGuid(),
                VehicleId = $"VESSEL-{Guid.NewGuid().ToString()[..8]}",
                Name = "New Vessel",
                MMSI = "000000000",
                Dimensions = new DimensionsDto
                {
                    DistanceGpsToFront = 10,
                    DistanceGpsToBack = 10,
                    DistanceGpsToLeft = 5,
                    DistanceGpsToRight = 5
                },
                DeviceGroups = []
            };

            // Initialize standard device groups
            InitializeStandardDeviceGroups(vessel);

            // Initialize all device groups with their allowed roles
            InitializeDeviceGroups(vessel);

            return vessel;
        }

        /// <summary>
        /// Initializes standard device groups in a vessel using DeviceGroups enum
        /// </summary>
        /// <param name="vessel">The vessel to initialize device groups for</param>
        public static void InitializeStandardDeviceGroups(VesselDto vessel)
        {
            // Initialize all device groups using the DeviceGroups enum
            foreach (var groupType in Enum.GetValues<DeviceGroups>())
            {
                // Get allowed roles directly from the enum
                var allowedRoles = groupType.GetAllowedRoles();

                // Create the device group DTO with proper enum-based structure
                vessel.DeviceGroups[groupType] = new DeviceGroupDto
                {
                    Type = groupType,
                    AllowedRoles = allowedRoles.ToList(),
                    Devices = []
                };
            }
        }

        /// <summary>
        /// Initializes all device groups in a vessel with their allowed roles using DeviceGroups enum
        /// </summary>
        /// <param name="vessel">The vessel to initialize device groups for</param>
        public static void InitializeDeviceGroups(VesselDto vessel)
        {
            // Initialize all device groups using the DeviceGroups enum
            foreach (var groupType in Enum.GetValues<DeviceGroups>())
            {
                // Get or create the device group using the enum-based method
                var deviceGroup = vessel.GetDeviceGroup(groupType);

                // Ensure allowed roles are set (they should already be set by GetDeviceGroup)
                if (deviceGroup.AllowedRoles == null || !deviceGroup.AllowedRoles.Any())
                {
                    deviceGroup.AllowedRoles = groupType.GetAllowedRoles().ToList();
                }
            }
        }
    }
}
