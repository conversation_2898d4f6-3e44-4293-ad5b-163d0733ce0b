using DevExpress.Blazor;

namespace Barret.Web.Server.Services
{
    public static class ToastOptionsExtensions
    {
        /// <summary>
        /// Creates a toast options object with the appropriate styling based on the provided type
        /// </summary>
        public static ToastOptions CreateToast(string title, string text, ToastType type = ToastType.Info, TimeSpan? displayTime = null)
        {
            var options = new ToastOptions
            {
                Title = title,
                Text = text,
                ShowIcon = true,
                DisplayTime = displayTime ?? TimeSpan.FromSeconds(5)
            };

            // Set the appropriate render style based on the type
            switch (type)
            {
                case ToastType.Success:
                    options.RenderStyle = ToastRenderStyle.Success;
                    break;
                case ToastType.Error:
                    options.RenderStyle = ToastRenderStyle.Danger;
                    break;
                case ToastType.Warning:
                    options.RenderStyle = ToastRenderStyle.Warning;
                    break;
                case ToastType.Info:
                default:
                    options.RenderStyle = ToastRenderStyle.Info;
                    break;
            }

            return options;
        }
    }

    public enum ToastType
    {
        Success,
        Error,
        Warning,
        Info
    }
}
