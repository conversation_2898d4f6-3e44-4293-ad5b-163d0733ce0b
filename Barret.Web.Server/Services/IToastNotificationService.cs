using DevExpress.Blazor;

namespace Barret.Web.Server.Services
{
    public interface IBarretToastService
    {
        void ShowToast(ToastOptions options);
        void ShowToast(string title, string text, ToastType type = ToastType.Info, TimeSpan? displayTime = null);
        Task ShowToastWithSoundAsync(ToastOptions options, string soundType = null);
        Task ShowToastWithSoundAsync(string title, string text, ToastType type = ToastType.Info, TimeSpan? displayTime = null, string soundType = null);
    }

    public interface IBarretToastNotificationService
    {
        void ShowToast(ToastOptions options);
        void ShowToast(string title, string text, ToastType type = ToastType.Info, TimeSpan? displayTime = null);
        Task ShowToastWithSoundAsync(ToastOptions options, string soundType = null);
        Task ShowToastWithSoundAsync(string title, string text, ToastType type = ToastType.Info, TimeSpan? displayTime = null, string soundType = null);
    }
}