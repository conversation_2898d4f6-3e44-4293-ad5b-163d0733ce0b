using DevExpress.Blazor;
using System.Reflection;

namespace Barret.Web.Server.Services
{
    public class ToastNotificationService(DevExpress.Blazor.IToastNotificationService deviceExpressToastService) : IBarretToastService, IBarretToastNotificationService
    {
        private readonly DevExpress.Blazor.IToastNotificationService _deviceExpressToastService = deviceExpressToastService;

        public void ShowToast(ToastOptions options)
        {
            // Handle legacy CssClass and IconCssClass properties by converting to appropriate RenderStyle
            if (options.CssClass != null)
            {
                if (options.CssClass.Contains("success"))
                    options.RenderStyle = ToastRenderStyle.Success;
                else if (options.CssClass.Contains("error") || options.CssClass.Contains("danger"))
                    options.RenderStyle = ToastRenderStyle.Danger;
                else if (options.CssClass.Contains("warning"))
                    options.RenderStyle = ToastRenderStyle.Warning;
                else if (options.CssClass.Contains("info"))
                    options.RenderStyle = ToastRenderStyle.Info;
            }

            _deviceExpressToastService.ShowToast(options);
        }

        public void ShowToast(string title, string text, ToastType type = ToastType.Info, TimeSpan? displayTime = null)
        {
            var options = ToastOptionsExtensions.CreateToast(title, text, type, displayTime);
            ShowToast(options);
        }

        public async Task ShowToastWithSoundAsync(ToastOptions options, string soundType = null)
        {
            ShowToast(options);

            // Get the sound type based on the toast type if not provided
            if (string.IsNullOrEmpty(soundType))
            {
                soundType = options.RenderStyle switch
                {
                    ToastRenderStyle.Danger => "error",
                    ToastRenderStyle.Warning => "warning",
                    ToastRenderStyle.Success => "success",
                    _ => "info"
                };
            }

            // Play the sound
            await PlaySoundAsync(soundType);
        }

        public async Task ShowToastWithSoundAsync(string title, string text, ToastType type = ToastType.Info, TimeSpan? displayTime = null, string soundType = null)
        {
            var options = ToastOptionsExtensions.CreateToast(title, text, type, displayTime);
            await ShowToastWithSoundAsync(options, soundType);
        }

        private async Task PlaySoundAsync(string soundType)
        {
            // Logic to play a sound based on the sound type
            // This would typically involve JavaScript interop
            // For now, we'll just log the sound type
            Console.WriteLine($"Playing sound: {soundType}");
            await Task.CompletedTask;
        }
    }
}