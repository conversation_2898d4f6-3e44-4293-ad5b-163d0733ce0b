<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="DevExpress.Blazor" Version="24.2.5" />
    <PackageReference Include="DevExpress.Blazor.Themes" Version="24.2.5" />
    <PackageReference Include="FluentValidation" Version="11.11.0" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="8.0.2" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.UI" Version="8.0.2" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.5">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="9.0.3" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.3">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.5" />
    <PackageReference Include="Radzen.Blazor" Version="7.0.7" />
    <PackageReference Include="ReactiveUI" Version="19.5.41" />
    <PackageReference Include="ReactiveUI.Blazor" Version="19.5.41" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Barret.Core\Barret.Core.csproj" />
    <ProjectReference Include="..\Barret.Services.Core\Barret.Services.Core.csproj" />
    <ProjectReference Include="..\Barret.Services\Barret.Services.csproj" />
    <ProjectReference Include="..\Barret.Shared\Barret.Shared.csproj" />
  </ItemGroup>

  <ItemGroup>
    <!-- Exclude the Test directory from compilation -->
    <Compile Remove="Test\**" />
    <Content Remove="Test\**" />
    <EmbeddedResource Remove="Test\**" />
    <None Remove="Test\**" />
  </ItemGroup>

</Project>
