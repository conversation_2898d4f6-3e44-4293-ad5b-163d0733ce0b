@using Barret.Web.Server.Features.Vehicles.Editor.ViewModels
@using Barret.Web.Server.Features.Shared.Components.BarretDevExpress
@using DevExpress.Blazor
@inherits ConfigExportDialogBase

<DxPopup @bind-Visible="@Visible"
         HeaderText="Export Configuration"
         ShowFooter="true"
         Width="600px"
         CloseOnEscape="true"
         CloseOnOutsideClick="false"
         Closing="@HandleClosing">
    <HeaderTemplate>
        <div class="d-flex align-items-center">
            <i class="bi bi-file-earmark-code me-2"></i>
            <span>Export Configuration</span>
        </div>
    </HeaderTemplate>
    <ChildContent>
        <div class="p-3">
            <div class="mb-3">
                <p>This will generate configuration files for the current vehicle and download them as a ZIP file.</p>
            </div>

            @if (ViewModel.IsExporting)
            {
                <div class="my-3">
                    <div class="d-flex align-items-center mb-2">
                        <div class="spinner-border spinner-border-sm text-primary me-2" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <span>Generating configurations... @ViewModel.ExportProgress%</span>
                    </div>
                    <div class="progress">
                        <div class="progress-bar" role="progressbar" style="width: @(ViewModel.ExportProgress)%;"
                             aria-valuenow="@ViewModel.ExportProgress" aria-valuemin="0" aria-valuemax="100">
                            @ViewModel.ExportProgress%
                        </div>
                    </div>
                </div>
            }
            else if (ViewModel.IsLoading)
            {
                <div class="d-flex justify-content-center my-3">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            }

            @if (!string.IsNullOrEmpty(ViewModel.ErrorMessage))
            {
                <div class="alert alert-danger">
                    @ViewModel.ErrorMessage
                </div>
            }

            @if (!string.IsNullOrEmpty(ViewModel.WarningMessage))
            {
                <div class="alert alert-warning">
                    @ViewModel.WarningMessage
                </div>
            }

            @if (!string.IsNullOrEmpty(ViewModel.SuccessMessage))
            {
                <div class="alert alert-success">
                    <p>@ViewModel.SuccessMessage</p>
                </div>
            }
        </div>
    </ChildContent>
    <FooterTemplate>
        <div class="d-flex justify-content-end gap-2">
            @if (ViewModel.CanCancel)
            {
                <DxButton Text="Cancel"
                         RenderStyle="ButtonRenderStyle.Danger"
                         Click="@HandleCancelExport" />
            }
            <DxButton Text="Close"
                     RenderStyle="ButtonRenderStyle.Secondary"
                     Click="@HandleCloseAsync" />
            <DxButton Text="Export"
                     RenderStyle="ButtonRenderStyle.Primary"
                     Click="@HandleExportAsync"
                     Enabled="@(!ViewModel.IsLoading && !ViewModel.IsExporting)" />
        </div>
    </FooterTemplate>
</DxPopup>
