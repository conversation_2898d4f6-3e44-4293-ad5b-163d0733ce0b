using Barret.Services.Core.Areas.Vehicles;
using Barret.Services.Core.Areas.Vehicles.Queries;
using Barret.Shared.DTOs.Devices;
using Barret.Shared.DTOs.Vehicles.Vessels;
using Barret.Web.Server.Extensions;
using Barret.Web.Server.Features.Shared;
using Barret.Web.Server.Features.Shared.Components;
using Barret.Web.Server.Features.Shared.Components.BarretDevExpress;
using Barret.Web.Server.Features.Vehicles.Editor.Components.Import.ViewModels;
using DevExpress.Blazor;
using DevExpress.Blazor.Grid;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Rendering;
using Microsoft.Extensions.Logging;
using ReactiveUI.Blazor;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Barret.Web.Server.Features.Vehicles.Editor.Components.Import.Views
{
    /// <summary>
    /// Base class for the device import dialog view.
    /// </summary>
    public class DeviceImportDialogViewBase : ComponentBase
    {
        /// <summary>
        /// Gets or sets the vessel query service.
        /// </summary>
        [Inject]
        protected IVesselQueryService VesselQueryService { get; set; } = null!;

        /// <summary>
        /// Gets or sets the vessel service.
        /// </summary>
        [Inject]
        protected IVehicleService<Core.Areas.Vehicles.Models.Vessel.Vessel, VesselDto> VesselService { get; set; } = null!;

        /// <summary>
        /// Gets or sets the logger.
        /// </summary>
        [Inject]
        protected ILogger<DeviceImportDialogViewBase> Logger { get; set; } = null!;

        /// <summary>
        /// Gets or sets the view model logger.
        /// </summary>
        [Inject]
        protected ILogger<DeviceImportDialogViewModel> ViewModelLogger { get; set; } = null!;

        /// <summary>
        /// Gets or sets the current vehicle ID.
        /// </summary>
        [Parameter]
        public Guid? VehicleId { get; set; }

        /// <summary>
        /// Gets or sets whether the dialog is visible.
        /// </summary>
        [Parameter]
        public bool Visible { get; set; }

        /// <summary>
        /// Event callback for when the visibility changes.
        /// </summary>
        [Parameter]
        public EventCallback<bool> VisibleChanged { get; set; }

        /// <summary>
        /// Event callback for when devices are imported.
        /// </summary>
        [Parameter]
        public EventCallback<List<DeviceDto>> OnDevicesImported { get; set; }

        /// <summary>
        /// Gets or sets the view model.
        /// </summary>
        protected DeviceImportDialogViewModel ViewModel { get; set; } = null!;

        /// <summary>
        /// Initializes the component.
        /// </summary>
        protected override void OnInitialized()
        {
            // Create the view model
            ViewModel = new DeviceImportDialogViewModel(
                VesselQueryService,
                VesselService,
                ViewModelLogger);
        }

        /// <summary>
        /// Method called when parameters are set.
        /// </summary>
        protected override async Task OnParametersSetAsync()
        {
            // Update the view model's visibility
            if (ViewModel.IsVisible != Visible)
            {
                ViewModel.IsVisible = Visible;

                // Initialize the dialog when it becomes visible
                if (Visible && VehicleId.HasValue)
                {
                    await ViewModel.InitializeAsync(VehicleId.Value);
                }
            }
        }

        /// <summary>
        /// Toggles the expanded state of a vessel and loads its devices if needed.
        /// </summary>
        /// <param name="vesselViewModel">The vessel view model.</param>
        protected async Task ToggleVesselExpanded(VesselWithDevicesViewModel vesselViewModel)
        {
            await ViewModel.ToggleVesselExpandedAsync(vesselViewModel);
            StateHasChanged();
        }

        /// <summary>
        /// Handles the vessel row click event.
        /// </summary>
        /// <param name="e">The row click event arguments.</param>
        protected async Task OnVesselRowClick(GridRowClickEventArgs e)
        {
            if (e.Grid.GetDataItem(e.VisibleIndex) is VesselWithDevicesViewModel vesselViewModel)
            {
                await ViewModel.ToggleVesselExpandedAsync(vesselViewModel);
                StateHasChanged();
            }
        }

        /// <summary>
        /// Handles the device row click event.
        /// </summary>
        /// <param name="e">The row click event arguments.</param>
        protected void OnDeviceRowClick(GridRowClickEventArgs e)
        {
            if (e.Grid.GetDataItem(e.VisibleIndex) is DeviceDto device)
            {
                // Toggle the selection state of the device
                ToggleDeviceSelection(device.Id);
                StateHasChanged();
            }
        }



        /// <summary>
        /// Handles the import button click.
        /// </summary>
        protected async Task ImportDevices()
        {
            var importedDevices = await ViewModel.ImportDevicesCommand.Execute().ToTask();
            if (importedDevices != null && importedDevices.Count > 0)
            {
                await OnDevicesImported.InvokeAsync(importedDevices);
            }

            await CloseDialog();
        }

        /// <summary>
        /// Closes the dialog.
        /// </summary>
        protected async Task CloseDialog()
        {
            Visible = false;
            ViewModel.IsVisible = false;
            await VisibleChanged.InvokeAsync(false);
        }

        /// <summary>
        /// Handles the popup closing event.
        /// </summary>
        /// <param name="_">The closing event arguments (unused).</param>
        protected async Task HandleClosing(DevExpress.Blazor.PopupClosingEventArgs _)
        {
            // Ensure the parent component's state is updated
            if (Visible)
            {
                Visible = false;
                ViewModel.IsVisible = false;
                await VisibleChanged.InvokeAsync(false);
            }
        }

        /// <summary>
        /// Toggles the selection state of a device.
        /// </summary>
        protected void ToggleDeviceSelection(Guid deviceId)
        {
            ViewModel.ToggleDeviceSelection(deviceId);
        }

        /// <summary>
        /// Gets whether a device is selected.
        /// </summary>
        protected bool IsDeviceSelected(Guid deviceId)
        {
            return ViewModel.SelectedDevices.TryGetValue(deviceId, out bool selected) && selected;
        }

        /// <summary>
        /// Handles row click events in the grid.
        /// </summary>
        /// <param name="e">The row click event arguments.</param>
        protected void OnRowClick(GridRowClickEventArgs e)
        {
            if (e.Grid.GetDataItem(e.VisibleIndex) is DeviceDto device)
            {
                // Toggle the selection state of the device
                ToggleDeviceSelection(device.Id);
                StateHasChanged();
            }
        }



        /// <summary>
        /// Renders the vessel detail template.
        /// </summary>
        /// <param name="context">The grid detail row template context.</param>
        /// <returns>The render fragment.</returns>
        protected RenderFragment VesselDetailTemplate(GridDetailRowTemplateContext context)
        {
            return builder =>
            {
                var vesselViewModel = context.DataItem as VesselWithDevicesViewModel;
                if (vesselViewModel == null) return;

                if (!vesselViewModel.IsLoaded)
                {
                    builder.OpenElement(0, "div");
                    builder.AddAttribute(1, "class", "d-flex justify-content-center my-3");

                    builder.OpenElement(2, "div");
                    builder.AddAttribute(3, "class", "spinner-border text-primary");
                    builder.AddAttribute(4, "role", "status");

                    builder.OpenElement(5, "span");
                    builder.AddAttribute(6, "class", "visually-hidden");
                    builder.AddContent(7, "Loading...");
                    builder.CloseElement(); // span

                    builder.CloseElement(); // div spinner
                    builder.CloseElement(); // div flex
                }
                else if (vesselViewModel.Devices.Count == 0)
                {
                    builder.OpenElement(0, "div");
                    builder.AddAttribute(1, "class", "p-3");

                    builder.OpenElement(2, "div");
                    builder.AddAttribute(3, "class", "alert alert-info mb-0");

                    builder.OpenElement(4, "i");
                    builder.AddAttribute(5, "class", "bi bi-info-circle me-2");
                    builder.CloseElement(); // i

                    builder.AddContent(6, "No devices found in this vessel.");

                    builder.CloseElement(); // div alert
                    builder.CloseElement(); // div p-3
                }
                else
                {
                    builder.OpenComponent<DevExpress.Blazor.DxGrid>(0);
                    builder.AddAttribute(1, "Data", vesselViewModel.Devices);
                    builder.AddAttribute(2, "CssClass", "barret-data-grid device-list-grid mb-0");
                    builder.AddAttribute(3, "ShowFilterRow", true);
                    builder.AddAttribute(4, "ShowPager", true);
                    builder.AddAttribute(5, "PageSize", 5);
                    builder.AddAttribute(6, "KeyFieldName", "Id");
                    builder.AddAttribute(7, "RowClick", EventCallback.Factory.Create<GridRowClickEventArgs>(this, OnRowClick));
                    builder.AddAttribute(8, "CustomizeElement", EventCallback.Factory.Create<GridCustomizeElementEventArgs>(this, OnCustomizeRow));

                    builder.AddAttribute(9, "Columns", (RenderFragment)(columnsBuilder =>
                    {
                        columnsBuilder.OpenComponent<DxGridSelectionColumn>(10);
                        columnsBuilder.AddAttribute(11, "Width", "50px");
                        columnsBuilder.AddAttribute(12, "CellDisplayTemplate", (RenderFragment<GridDataColumnCellDisplayTemplateContext>)(cellContext =>
                        {
                            return builder2 =>
                            {
                                var device = cellContext.DataItem as DeviceDto;
                                if (device == null) return;

                                builder2.OpenComponent<BarretCheckBox>(13);
                                builder2.AddAttribute(14, "Checked", IsDeviceSelected(device.Id));
                                builder2.AddAttribute(15, "CheckedChanged", EventCallback.Factory.Create<bool>(this, value =>
                                {
                                    ToggleDeviceSelection(device.Id);
                                    StateHasChanged();
                                }));
                                builder2.CloseComponent(); // BarretCheckBox
                            };
                        }));
                        columnsBuilder.CloseComponent(); // DxGridSelectionColumn

                        columnsBuilder.OpenComponent<DxGridDataColumn>(16);
                        columnsBuilder.AddAttribute(17, "Caption", "Name");
                        columnsBuilder.AddAttribute(18, "FieldName", "Name");
                        columnsBuilder.AddAttribute(19, "CellDisplayTemplate", (RenderFragment<GridDataColumnCellDisplayTemplateContext>)(cellContext =>
                        {
                            return builder2 =>
                            {
                                var device = cellContext.DataItem as DeviceDto;
                                if (device == null) return;

                                builder2.OpenElement(20, "span");
                                builder2.AddAttribute(21, "class", IsDeviceSelected(device.Id) ? "fw-bold text-primary" : "");
                                builder2.AddContent(22, device.Name);
                                builder2.CloseElement(); // span
                            };
                        }));
                        columnsBuilder.CloseComponent(); // DxGridDataColumn Name

                        columnsBuilder.OpenComponent<DxGridDataColumn>(23);
                        columnsBuilder.AddAttribute(24, "Caption", "Role");
                        columnsBuilder.AddAttribute(25, "FieldName", "DeviceRole");
                        columnsBuilder.CloseComponent(); // DxGridDataColumn Role

                        columnsBuilder.OpenComponent<DxGridDataColumn>(26);
                        columnsBuilder.AddAttribute(27, "Caption", "Manufacturer");
                        columnsBuilder.AddAttribute(28, "FieldName", "ManufacturerName");
                        columnsBuilder.CloseComponent(); // DxGridDataColumn Manufacturer

                        columnsBuilder.OpenComponent<DxGridDataColumn>(29);
                        columnsBuilder.AddAttribute(30, "Caption", "Model");
                        columnsBuilder.AddAttribute(31, "FieldName", "ModelName");
                        columnsBuilder.CloseComponent(); // DxGridDataColumn Model

                        columnsBuilder.OpenComponent<DxGridDataColumn>(32);
                        columnsBuilder.AddAttribute(33, "Caption", "Group");
                        columnsBuilder.AddAttribute(34, "FieldName", "DeviceGroupName");
                        columnsBuilder.CloseComponent(); // DxGridDataColumn Group
                    }));

                    builder.AddAttribute(35, "DetailRowTemplate", (RenderFragment<GridDetailRowTemplateContext>)(deviceDetailContext =>
                    {
                        return builder2 =>
                        {
                            var device = deviceDetailContext.DataItem as DeviceDto;
                            if (device == null) return;

                            builder2.OpenElement(36, "div");
                            builder2.AddAttribute(37, "class", "p-3 bg-light rounded-3");

                            builder2.OpenElement(38, "div");
                            builder2.AddAttribute(39, "class", "row");

                            // Connection Details Column
                            builder2.OpenElement(40, "div");
                            builder2.AddAttribute(41, "class", "col-md-6");

                            builder2.OpenElement(42, "h6");
                            builder2.AddContent(43, "Connection Details");
                            builder2.CloseElement(); // h6

                            builder2.OpenElement(44, "div");
                            builder2.AddAttribute(45, "class", "mb-2");
                            builder2.OpenElement(46, "strong");
                            builder2.AddContent(47, "IP Address:");
                            builder2.CloseElement(); // strong
                            builder2.AddContent(48, " " + (device.Connection?.IPAddress ?? "N/A"));
                            builder2.CloseElement(); // div mb-2

                            builder2.OpenElement(49, "div");
                            builder2.AddAttribute(50, "class", "mb-2");
                            builder2.OpenElement(51, "strong");
                            builder2.AddContent(52, "Port:");
                            builder2.CloseElement(); // strong
                            builder2.AddContent(53, " " + (device.Connection?.Port ?? 0));
                            builder2.CloseElement(); // div mb-2

                            builder2.OpenElement(54, "div");
                            builder2.AddAttribute(55, "class", "mb-2");
                            builder2.OpenElement(56, "strong");
                            builder2.AddContent(57, "Protocol:");
                            builder2.CloseElement(); // strong
                            builder2.AddContent(58, " " + (device.Connection?.Protocol != null ? device.Connection.Protocol.ToString() : "N/A"));
                            builder2.CloseElement(); // div mb-2

                            builder2.CloseElement(); // div col-md-6

                            // Position Column
                            builder2.OpenElement(59, "div");
                            builder2.AddAttribute(60, "class", "col-md-6");

                            builder2.OpenElement(61, "h6");
                            builder2.AddContent(62, "Position");
                            builder2.CloseElement(); // h6

                            builder2.OpenElement(63, "div");
                            builder2.AddAttribute(64, "class", "mb-2");
                            builder2.OpenElement(65, "strong");
                            builder2.AddContent(66, "X:");
                            builder2.CloseElement(); // strong
                            builder2.AddContent(67, " " + device.Position.X);
                            builder2.CloseElement(); // div mb-2

                            builder2.OpenElement(68, "div");
                            builder2.AddAttribute(69, "class", "mb-2");
                            builder2.OpenElement(70, "strong");
                            builder2.AddContent(71, "Y:");
                            builder2.CloseElement(); // strong
                            builder2.AddContent(72, " " + device.Position.Y);
                            builder2.CloseElement(); // div mb-2

                            builder2.OpenElement(73, "div");
                            builder2.AddAttribute(74, "class", "mb-2");
                            builder2.OpenElement(75, "strong");
                            builder2.AddContent(76, "Z:");
                            builder2.CloseElement(); // strong
                            builder2.AddContent(77, " " + device.Position.Z);
                            builder2.CloseElement(); // div mb-2

                            builder2.CloseElement(); // div col-md-6

                            builder2.CloseElement(); // div row
                            builder2.CloseElement(); // div p-3
                        };
                    }));

                    builder.CloseComponent(); // DxGrid
                }
            };
        }

        /// <summary>
        /// Customizes the row element based on selection state.
        /// </summary>
        /// <param name="e">The customize element event arguments.</param>
        protected void OnCustomizeRow(GridCustomizeElementEventArgs e)
        {
            if (e.ElementType == GridElementType.DataRow && e.VisibleIndex >= 0)
            {
                var grid = e.Grid;
                if (grid.GetDataItem(e.VisibleIndex) is DeviceDto device && IsDeviceSelected(device.Id))
                {
                    e.CssClass = "selected-row";
                }
                else if (grid.GetDataItem(e.VisibleIndex) is VesselWithDevicesViewModel vesselViewModel)
                {
                    if (vesselViewModel.IsLoaded && vesselViewModel.Devices.Count > 0 &&
                        AreAllDevicesSelectedInVessel(vesselViewModel))
                    {
                        e.CssClass = "selected-row";
                    }
                }
            }
        }

        /// <summary>
        /// Checks if all devices in a vessel are selected.
        /// </summary>
        /// <param name="vesselViewModel">The vessel view model.</param>
        /// <returns>True if all devices in the vessel are selected; otherwise, false.</returns>
        protected bool AreAllDevicesSelectedInVessel(VesselWithDevicesViewModel vesselViewModel)
        {
            if (vesselViewModel == null || !vesselViewModel.IsLoaded || vesselViewModel.Devices.Count == 0)
            {
                return false;
            }

            return vesselViewModel.Devices.All(d =>
                ViewModel.SelectedDevices.TryGetValue(d.Id, out bool selected) && selected);
        }

        /// <summary>
        /// Toggles the selection state of all devices in a vessel.
        /// </summary>
        /// <param name="vesselViewModel">The vessel view model.</param>
        /// <param name="selected">The selection state to set.</param>
        protected void ToggleAllDevicesInVessel(VesselWithDevicesViewModel vesselViewModel, bool selected)
        {
            if (vesselViewModel == null || !vesselViewModel.IsLoaded)
            {
                return;
            }

            foreach (var device in vesselViewModel.Devices)
            {
                ViewModel.SelectedDevices[device.Id] = selected;
            }

            // Update UI
            StateHasChanged();
        }
    }
}
