@using Barret.Shared.DTOs.Devices
@using Barret.Shared.DTOs.Vehicles.Vessels
@using Barret.Web.Server.Features.Shared.Components.BarretDevExpress
@using DevExpress.Blazor
@using DevExpress.Blazor.Grid
@using Barret.Web.Server.Features.Vehicles.Editor.Components.Import.ViewModels
@inherits DeviceImportDialogViewBase



<DxPopup @bind-Visible="@Visible"
         HeaderText="Import Devices"
         ShowFooter="true"
         Width="900px"
         CloseOnEscape="true"
         CloseOnOutsideClick="false"
         ShowCloseButton="true"
         Closing="@HandleClosing">
    <HeaderTemplate>
        <div class="d-flex align-items-center">
            <i class="bi bi-download me-2"></i>
            <span>Import Devices</span>
        </div>
    </HeaderTemplate>
    <ChildContent>
        <div class="p-3">
            @if (ViewModel.IsLoading)
            {
                <div class="d-flex justify-content-center my-3">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            }
            else
            {
                <div class="mb-3">
                    <h5>Available Vessels and Devices</h5>
                    <p class="text-muted">Expand a vessel to view its devices and select the ones you want to import.</p>
                </div>

                <DxGrid Data="@ViewModel.Vessels"
                       CssClass="barret-data-grid device-import-grid mb-4"
                       ShowFilterRow="true"
                       ShowPager="true"
                       PageSize="10"
                       RowClick="@OnVesselRowClick"
                       CustomizeElement="@OnCustomizeRow">
                    <Columns>
                        <DxGridDataColumn Caption="Name">
                            <CellDisplayTemplate Context="cellContext">
                                @{
                                    var vesselViewModel = cellContext.DataItem as VesselWithDevicesViewModel;
                                    if (vesselViewModel != null)
                                    {
                                        <div class="d-flex align-items-center">
                                            <i class="bi bi-ship me-2"></i>
                                            <span class="fw-bold">@vesselViewModel.Vessel.Name</span>
                                            <span class="text-muted ms-2">(@vesselViewModel.Vessel.VehicleId)</span>
                                        </div>
                                    }
                                }
                            </CellDisplayTemplate>
                        </DxGridDataColumn>
                        <DxGridDataColumn Caption="Devices" Width="120px">
                            <CellDisplayTemplate Context="cellContext">
                                @{
                                    var vesselViewModel = cellContext.DataItem as VesselWithDevicesViewModel;
                                    if (vesselViewModel != null)
                                    {
                                        <div class="d-flex align-items-center">
                                            @if (vesselViewModel.IsLoaded)
                                            {
                                                <span class="badge bg-info">@vesselViewModel.Devices.Count</span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-secondary">-</span>
                                            }
                                        </div>
                                    }
                                }
                            </CellDisplayTemplate>
                        </DxGridDataColumn>
                        <DxGridDataColumn Caption="Select All" Width="120px">
                            <CellDisplayTemplate Context="cellContext">
                                @{
                                    var vesselViewModel = cellContext.DataItem as VesselWithDevicesViewModel;
                                    if (vesselViewModel != null && vesselViewModel.IsLoaded && vesselViewModel.Devices.Count > 0)
                                    {
                                        <BarretCheckBox Checked="@AreAllDevicesSelectedInVessel(vesselViewModel)"
                                                      CheckedChanged="@((value) => { ToggleAllDevicesInVessel(vesselViewModel, value); StateHasChanged(); })" />
                                    }
                                }
                            </CellDisplayTemplate>
                        </DxGridDataColumn>
                    </Columns>
                    <DetailRowTemplate Context="detailContext">
                        @{
                            var vesselViewModel = detailContext.DataItem as VesselWithDevicesViewModel;
                            if (vesselViewModel != null)
                            {
                                if (!vesselViewModel.IsLoaded)
                                {
                                    <div class="d-flex justify-content-center my-3">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                    </div>
                                }
                                else if (vesselViewModel.Devices.Count == 0)
                                {
                                    <div class="p-3">
                                        <div class="alert alert-info mb-0">
                                            <i class="bi bi-info-circle me-2"></i>
                                            No devices found in this vessel.
                                        </div>
                                    </div>
                                }
                                else
                                {
                                    <DxGrid Data="@vesselViewModel.Devices"
                                           CssClass="barret-data-grid device-list-grid mb-0"
                                           ShowFilterRow="true"
                                           ShowPager="true"
                                           PageSize="5"
                                           KeyFieldName="Id"
                                           RowClick="@OnDeviceRowClick"
                                           CustomizeElement="@OnCustomizeRow">
                                        <Columns>
                                            <DxGridSelectionColumn Width="50px">
                                                <CellDisplayTemplate Context="deviceCellContext">
                                                    @{
                                                        var device = deviceCellContext.DataItem as DeviceDto;
                                                        if (device != null)
                                                        {
                                                            <BarretCheckBox Checked="@IsDeviceSelected(device.Id)"
                                                                          CheckedChanged="@((value) => { ToggleDeviceSelection(device.Id); StateHasChanged(); })" />
                                                        }
                                                    }
                                                </CellDisplayTemplate>
                                            </DxGridSelectionColumn>
                                            <DxGridDataColumn Caption="Name" FieldName="Name">
                                                <CellDisplayTemplate Context="deviceCellContext">
                                                    @{
                                                        var device = deviceCellContext.DataItem as DeviceDto;
                                                        if (device != null)
                                                        {
                                                            <span class="@(IsDeviceSelected(device.Id) ? "fw-bold text-primary" : "")">@device.Name</span>
                                                        }
                                                    }
                                                </CellDisplayTemplate>
                                            </DxGridDataColumn>
                                            <DxGridDataColumn Caption="Role" FieldName="DeviceRole" />
                                            <DxGridDataColumn Caption="Manufacturer" FieldName="ManufacturerName" />
                                            <DxGridDataColumn Caption="Model" FieldName="ModelName" />
                                            <DxGridDataColumn Caption="Group" FieldName="DeviceGroupName" />
                                        </Columns>
                                    </DxGrid>
                                }
                            }
                        }
                    </DetailRowTemplate>
                </DxGrid>

                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <span class="badge bg-primary">@ViewModel.GetSelectedDeviceCount() devices selected</span>
                    </div>
                </div>
            }
        </div>
    </ChildContent>
    <FooterTemplate>
        <div class="d-flex justify-content-end">
            <BarretActionButton Text="Cancel"
                              RenderStyle="ButtonRenderStyle.Secondary"
                              OnClick="@CloseDialog"
                              CssClass="me-2" />
            <BarretActionButton Text="Import Selected Devices"
                              RenderStyle="ButtonRenderStyle.Primary"
                              OnClick="@ImportDevices"
                              IconCssClass="bi bi-download"
                              Enabled="@(ViewModel.GetSelectedDeviceCount() > 0)" />
        </div>
    </FooterTemplate>
</DxPopup>
