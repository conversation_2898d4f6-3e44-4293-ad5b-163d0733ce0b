@using Barret.Web.Server.Features.Vehicles.Editor.Components.Devices.Components.Common
@using Barret.Web.Server.Features.Shared.Components.BarretDevExpress
@using Barret.Web.Server.Features.Vehicles.Editor.Components.Devices
@using Barret.Web.Server.Features.Vehicles.Editor.Components.Devices.Components
@using Barret.Web.Server.Shared.Components.Dialogs
@using DevExpress.Blazor
@using DevExpress.Blazor.Grid
@using Barret.Shared.DTOs.Devices
@using Barret.Core.Areas.Devices.Enums
@using Barret.Core.Areas.DeviceGroups
@using Barret.Core.Areas.DeviceGroups.Attributes
@inherits DeviceGroupTabViewBaseNew

<div class="space-y-8">
    <div class="flex justify-between items-center">
        <h3 class="text-lg font-medium text-gray-900">
            @GroupType.GetDisplayName()
        </h3>
        <button class="flex items-center gap-2 h-10 px-4 rounded-full bg-gray-900 text-white hover:bg-gray-800 transition-colors"
                @onclick="AddDevice">
            <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <line x1="12" y1="5" x2="12" y2="19"></line>
                <line x1="5" y1="12" x2="19" y2="12"></line>
            </svg>
            Add Device
        </button>
    </div>

    @if (DeviceCollectionForGroup.Count == 0)
    {
        <div class="text-center py-16 bg-gray-50 rounded-xl">
            <div class="mx-auto w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                <svg class="h-8 w-8 text-gray-700" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    @switch (GroupType)
                    {
                        case DeviceGroups.CameraGroup:
                            <path d="M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"></path>
                            <circle cx="12" cy="13" r="4"></circle>
                            break;
                        case DeviceGroups.SensorGroup:
                            <circle cx="12" cy="12" r="10"></circle>
                            <path d="M16.2 7.8l-2 6.3-6.4 2.1 2-6.3z"></path>
                            break;
                        default:
                            <rect width="7" height="7" x="3" y="3" rx="1"></rect>
                            <rect width="7" height="7" x="14" y="3" rx="1"></rect>
                            <rect width="7" height="7" x="14" y="14" rx="1"></rect>
                            <rect width="7" height="7" x="3" y="14" rx="1"></rect>
                            break;
                    }
                </svg>
            </div>
            <h3 class="text-xl font-medium text-gray-900 mb-2">
                <span>No @GroupType.GetDisplayName() Found</span>
            </h3>
            <p class="text-gray-500 mb-6 max-w-md mx-auto">
                <span>You haven't added any @GroupType.GetDisplayName() yet. Add your first device to get started.</span>
            </p>
            <button class="flex items-center gap-2 h-10 px-4 rounded-full bg-gray-900 text-white hover:bg-gray-800 transition-colors mx-auto"
                    @onclick="AddDevice">
                <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <line x1="12" y1="5" x2="12" y2="19"></line>
                    <line x1="5" y1="12" x2="19" y2="12"></line>
                </svg>
                <span>Add @GroupType.GetDisplayName().TrimEnd('s')</span>
            </button>
        </div>
    }
    else
    {
        <div class="overflow-x-auto -mx-4 px-4 sm:mx-0 sm:px-0">
            <DxGrid Data="@DeviceCollectionForGroup"
                   CssClass="barret-data-grid bg-white rounded-xl overflow-hidden mb-8 border border-gray-100"
                   ShowFilterRow="true"
                   ShowPager="true"
                   PageSize="10"
                   KeyFieldName="Id"
                   AutoCollapseDetailRow="true"
                   DetailRowDisplayMode="GridDetailRowDisplayMode.Auto">
                <Columns>
                    <DxGridDataColumn Caption="Status" Width="100px">
                        <CellDisplayTemplate>
                            @{
                                var device = context.DataItem as DeviceDto;
                                if (device != null)
                                {
                                    if (IsDeviceConfigured(device))
                                    {
                                        <div class="flex items-center text-green-500">
                                            <svg class="h-4 w-4 mr-1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                                                <polyline points="22 4 12 14.01 9 11.01"></polyline>
                                            </svg>
                                            <span class="text-xs font-medium">OK</span>
                                        </div>
                                    }
                                    else
                                    {
                                        <div class="flex items-center text-red-500">
                                            <svg class="h-4 w-4 mr-1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <circle cx="12" cy="12" r="10"></circle>
                                                <line x1="12" y1="8" x2="12" y2="12"></line>
                                                <line x1="12" y1="16" x2="12.01" y2="16"></line>
                                            </svg>
                                            <span class="text-xs font-medium">Needs setup</span>
                                        </div>
                                    }
                                }
                            }
                        </CellDisplayTemplate>
                    </DxGridDataColumn>
                    <DxGridDataColumn Caption="Name" FieldName="Name" />
                    <DxGridDataColumn Caption="Role" FieldName="DeviceRole" />
                    <DxGridDataColumn Caption="Manufacturer" FieldName="ManufacturerName" />
                    <DxGridDataColumn Caption="Model" FieldName="ModelName" />
                    <DxGridDataColumn Caption="Actions" Width="120px">
                        <CellDisplayTemplate>
                            <div class="flex justify-end">
                                <button class="text-gray-500 hover:text-gray-900 hover:bg-gray-100 rounded-full h-8 w-8 p-0 flex items-center justify-center mr-1"
                                        @onclick="() => EditDevice(context.DataItem as DeviceDto)">
                                    <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M12 20h9"></path>
                                        <path d="M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z"></path>
                                    </svg>
                                </button>
                                <button class="text-gray-500 hover:text-gray-900 hover:bg-gray-100 rounded-full h-8 w-8 p-0 flex items-center justify-center"
                                        @onclick="() => ShowDeleteDeviceConfirmation(context.DataItem as DeviceDto)">
                                    <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M3 6h18"></path>
                                        <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                                        <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                                    </svg>
                                </button>
                            </div>
                        </CellDisplayTemplate>
                    </DxGridDataColumn>
                </Columns>
                <DetailRowTemplate>
                    <div class="relative overflow-visible">
                        <DeviceConnectionsPanel
                            Device="@((DeviceDto)context.DataItem)"
                            OnDeviceChanged="@HandleDeviceUpdated"
                            AllDevices="@Devices"
                            Vessel="@Vessel" />
                    </div>
                </DetailRowTemplate>
            </DxGrid>
        </div>
    }
</div>

<!-- Device Role Selector (hidden by default, shown when needed) -->
<DeviceRoleSelector @ref="RoleSelector"
                   OnRoleSelected="@HandleRoleSelected"
                   OnCancelled="@(() => StateHasChanged())" />

<!-- Device Editor Factory (hidden by default, shown when needed) -->
<DeviceEditorFactory @ref="DeviceEditorFactory"
                    OnDeviceEdited="@HandleDeviceEdited"
                    OnDevicePropertyChanged="@HandleDevicePropertyChanged"
                    IsAddingInterface="false" />

<!-- Delete Confirmation Dialog -->
@if (showDeleteConfirmation)
{
    <ConfirmationDialog
        IsVisible="true"
        Title="Delete Device"
        Message="Are you sure you want to delete this device? This action cannot be undone."
        ConfirmText="Delete"
        CancelText="Cancel"
        Icon="bi bi-exclamation-triangle"
        IconClass="text-red-600"
        ConfirmButtonClass="bg-red-600 hover:bg-red-700 text-white rounded-full px-4 py-2"
        CancelButtonClass="border border-gray-300 text-gray-700 hover:bg-gray-50 rounded-full px-4 py-2"
        DialogClass="bg-white rounded-xl shadow-lg p-6 max-w-md mx-auto"
        OnConfirm="ConfirmDeleteDevice"
        OnCancel="CancelDeleteDevice" />
}


