using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.DeviceGroups;
using Barret.Services.Core.Areas.DeviceModels.Queries;
using Barret.Services.Core.Areas.Manufacturers;
using Barret.Shared.DTOs.Devices;
using Barret.Shared.DTOs.Vehicles.Vessels;
using Barret.Shared.Factories;
using Barret.Web.Server.Features.Vehicles.Editor.Components.Devices.Components.Common;
using Barret.Web.Server.Features.Vehicles.Editor.Components.Devices.Services;
using Barret.Web.Server.Features.Vehicles.Editor.ViewModels;
using Barret.Web.Server.Features.Vehicles.Services;
using Barret.Web.Server.Services;
using DevExpress.Blazor;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;

namespace Barret.Web.Server.Features.Vehicles.Editor.Components.Tabs
{
    /// <summary>
    /// Base class for the device group tab view.
    /// </summary>
    public class DeviceGroupTabViewBaseNew : ComponentBase
    {
        /// <summary>
        /// Gets or sets the group type.
        /// This corresponds to the Type property in IDeviceGroup
        /// </summary>
        [Parameter]
        public DeviceGroups GroupType { get; set; }



        /// <summary>
        /// Gets or sets the device group.
        /// </summary>
        [Parameter]
        public DeviceGroupDto DeviceGroup { get; set; } = null!;

        /// <summary>
        /// Gets or sets the vehicle ID.
        /// </summary>
        [Parameter]
        public Guid VehicleId { get; set; }

        /// <summary>
        /// Gets or sets the vessel.
        /// </summary>
        [Parameter]
        public VesselDto Vessel { get; set; } = null!;

        /// <summary>
        /// Gets or sets the callback for when a device is added.
        /// </summary>
        [Parameter]
        public EventCallback<DeviceDto> OnDeviceAdded { get; set; }

        /// <summary>
        /// Gets or sets the callback for when a device is removed.
        /// </summary>
        [Parameter]
        public EventCallback<Guid> OnDeviceRemoved { get; set; }

        /// <summary>
        /// Gets or sets the callback for when a device is updated.
        /// </summary>
        [Parameter]
        public EventCallback<DeviceDto> OnDeviceUpdated { get; set; }

        /// <summary>
        /// Gets or sets the device editor service.
        /// </summary>
        [Inject]
        protected DeviceEditorService DeviceEditorService { get; set; } = null!;

        /// <summary>
        /// Gets or sets the device model query service.
        /// </summary>
        [Inject]
        protected IDeviceModelQueryService DeviceModelQueryService { get; set; } = null!;

        /// <summary>
        /// Gets or sets the manufacturer service.
        /// </summary>
        [Inject]
        protected IManufacturerService ManufacturerService { get; set; } = null!;

        /// <summary>
        /// Gets or sets the toast notification service.
        /// </summary>
        [Inject]
        protected IBarretToastNotificationService ToastService { get; set; } = null!;

        /// <summary>
        /// Gets or sets the logger.
        /// </summary>
        [Inject]
        protected ILogger<DeviceGroupTabViewBaseNew> Logger { get; set; } = null!;

        /// <summary>
        /// Gets or sets the vehicle editor view model.
        /// </summary>
        [Inject]
        protected VehicleEditorViewModel EditorViewModel { get; set; } = null!;

        /// <summary>
        /// Gets or sets the navigation manager.
        /// </summary>
        [Inject]
        protected NavigationManager NavigationManager { get; set; } = null!;

        /// <summary>
        /// Gets or sets the vehicle service.
        /// </summary>
        [Inject]
        protected IVehicleService VehicleService { get; set; } = null!;

        /// <summary>
        /// Gets or sets the device role selector.
        /// </summary>
        protected DeviceRoleSelector RoleSelector { get; set; } = null!;

        /// <summary>
        /// Gets the device collection for the current group from the view model.
        /// </summary>
        protected ObservableCollection<DeviceDto> DeviceCollectionForGroup =>
            EditorViewModel.DeviceCollections.TryGetValue(GroupType, out var collection)
                ? collection
                : new ObservableCollection<DeviceDto>();

        /// <summary>
        /// Gets all devices in the vehicle.
        /// </summary>
        protected List<DeviceDto> Devices => EditorViewModel.DeviceCollections.Values
            .SelectMany(collection => collection)
            .ToList();

        /// <summary>
        /// Gets or sets the device editor factory.
        /// </summary>
        protected Devices.DeviceEditorFactory DeviceEditorFactory { get; set; } = null!;

        /// <summary>
        /// Gets or sets a value indicating whether the delete confirmation dialog is visible.
        /// </summary>
        protected bool showDeleteConfirmation;

        /// <summary>
        /// Gets or sets the device to delete.
        /// </summary>
        protected DeviceDto? deviceToDelete;

        /// <summary>
        /// Determines if a device is properly configured.
        /// </summary>
        /// <param name="device">The device to check.</param>
        /// <returns>True if the device is configured, false otherwise.</returns>
        protected bool IsDeviceConfigured(DeviceDto device)
        {
            return !string.IsNullOrEmpty(device.ManufacturerName) && !string.IsNullOrEmpty(device.ModelName);
        }

        /// <summary>
        /// Adds a new device.
        /// </summary>
        protected async Task AddDevice()
        {
            try
            {
                // Get allowed roles directly from the enum value using the extension method
                var domainRoles = GroupType.GetAllowedRoles().ToList();
                Logger.LogInformation("Using {Count} allowed roles from enum for device group {GroupType}",
                    domainRoles.Count, GroupType);

                if (domainRoles.Count > 0)
                {
                    // Update the allowed roles in the device group
                    DeviceGroup.AllowedRoles = domainRoles;

                    // Also update the Type property to ensure it matches
                    DeviceGroup.Type = GroupType;
                }
                else
                {
                    Logger.LogWarning("No allowed roles found for device group {GroupType}, using existing roles",
                        GroupType);
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error loading allowed roles for device group {GroupType}, using existing roles",
                    GroupType);
            }

            // Show the role selector
            RoleSelector.AllowedRoles = DeviceGroup.AllowedRoles;
            RoleSelector.Open();
        }

        /// <summary>
        /// Shows the delete confirmation dialog for a device.
        /// </summary>
        /// <param name="device">The device to delete.</param>
        protected void ShowDeleteDeviceConfirmation(DeviceDto device)
        {
            deviceToDelete = device;
            showDeleteConfirmation = true;
            StateHasChanged();
        }

        /// <summary>
        /// Confirms the deletion of a device.
        /// </summary>
        protected async Task ConfirmDeleteDevice()
        {
            if (deviceToDelete != null)
            {
                await OnDeviceRemoved.InvokeAsync(deviceToDelete.Id);
            }
            showDeleteConfirmation = false;
            deviceToDelete = null;
            StateHasChanged();
        }

        /// <summary>
        /// Cancels the deletion of a device.
        /// </summary>
        protected void CancelDeleteDevice()
        {
            showDeleteConfirmation = false;
            deviceToDelete = null;
            StateHasChanged();
        }

        /// <summary>
        /// Edits a device.
        /// </summary>
        /// <param name="device">The device to edit.</param>
        protected void EditDevice(DeviceDto device)
        {
            if (device != null)
            {
                // Open the device editor for editing
                DeviceEditorFactory.OpenForEdit(device);
            }
        }



        /// <summary>
        /// Handles the role selection event from the device role selector.
        /// </summary>
        /// <param name="role">The selected device role.</param>
        protected void HandleRoleSelected(DeviceRole role)
        {
            try
            {
                // Create a new device with the selected role
                var device = DeviceEditorService.CreateNewDevice(role, GroupType.ToString());
                device.VehicleId = VehicleId;

                // Open the device editor for adding
                DeviceEditorFactory.OpenForAdd(device);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error creating device with role {DeviceRole}", role);
                ToastService.ShowToast("Error", $"Error creating device: {ex.Message}", ToastType.Error);
            }
        }

        /// <summary>
        /// Handles device edited event.
        /// </summary>
        /// <param name="device">The edited device.</param>
        protected async Task HandleDeviceEdited(DeviceDto device)
        {
            if (device != null)
            {
                // Check if this is a new device or an existing one
                bool isExisting = DeviceGroup.Devices.Any(d => d.Id == device.Id);

                if (isExisting)
                {
                    // Update the device
                    if (OnDeviceUpdated.HasDelegate)
                    {
                        await OnDeviceUpdated.InvokeAsync(device);
                    }
                }
                else
                {
                    // Add the device
                    if (OnDeviceAdded.HasDelegate)
                    {
                        await OnDeviceAdded.InvokeAsync(device);
                    }
                }
            }
        }

        /// <summary>
        /// Handles device property changed event.
        /// This is called when device properties are modified in the device editor
        /// but before the device is saved.
        /// </summary>
        /// <param name="device">The device with changed properties.</param>
        protected async Task HandleDevicePropertyChanged(DeviceDto device)
        {
            if (device != null)
            {
                // Immediately update the device to mark the vehicle as dirty
                if (OnDeviceUpdated.HasDelegate)
                {
                    await OnDeviceUpdated.InvokeAsync(device);
                }
            }
        }

        /// <summary>
        /// Handles device updated event from child components.
        /// </summary>
        /// <param name="device">The updated device.</param>
        protected async Task HandleDeviceUpdated(DeviceDto device)
        {
            if (device != null && OnDeviceUpdated.HasDelegate)
            {
                await OnDeviceUpdated.InvokeAsync(device);
            }
        }
    }
}
