@inherits BasicInfoTabViewBase

<div class="space-y-8">
    <!-- Vehicle Information Section -->
    <div>
        <h3 class="text-lg font-medium text-gray-900 mb-4">Vehicle Information</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-2">
                <label class="text-sm font-medium text-gray-700">Vehicle Name</label>
                <BarretTextBox Text="@Vessel.Name"
                              TextChanged="@(value => UpdateField(value, nameof(Vessel.Name)))"
                              Placeholder="Enter vehicle name"
                              CssClass="w-full h-11 rounded-lg border-gray-200 focus:border-gray-300 focus:ring focus:ring-gray-200 focus:ring-opacity-50" />
            </div>
            <div class="space-y-2">
                <label class="text-sm font-medium text-gray-700">Vehicle ID</label>
                <BarretTextBox Text="@Vessel.VehicleId"
                              TextChanged="@(value => UpdateField(value, nameof(Vessel.VehicleId)))"
                              Placeholder="Enter vehicle ID"
                              CssClass="w-full h-11 rounded-lg border-gray-200 focus:border-gray-300 focus:ring focus:ring-gray-200 focus:ring-opacity-50" />
            </div>
        </div>
    </div>

    <!-- Vessel Identifiers Section -->
    <div>
        <h3 class="text-lg font-medium text-gray-900 mb-4">Vessel Identifiers</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-2">
                <label class="text-sm font-medium text-gray-700">MMSI</label>
                <BarretTextBox Text="@Vessel.MMSI"
                              TextChanged="@(value => UpdateField(value, nameof(Vessel.MMSI)))"
                              Placeholder="Enter MMSI"
                              CssClass="w-full h-11 rounded-lg border-gray-200 focus:border-gray-300 focus:ring focus:ring-gray-200 focus:ring-opacity-50" />
                <p class="text-xs text-gray-500">Maritime Mobile Service Identity (9 digits)</p>
            </div>
            <div class="space-y-2">
                <label class="text-sm font-medium text-gray-700">ENI</label>
                <BarretTextBox Text="@Vessel.ENI"
                              TextChanged="@(value => UpdateField(value, nameof(Vessel.ENI)))"
                              Placeholder="Enter ENI (optional)"
                              CssClass="w-full h-11 rounded-lg border-gray-200 focus:border-gray-300 focus:ring focus:ring-gray-200 focus:ring-opacity-50" />
                <p class="text-xs text-gray-500">European Number of Identification (8 digits)</p>
            </div>
        </div>
    </div>

    <!-- Vessel Dimensions Section -->
    <div>
        <h3 class="text-lg font-medium text-gray-900 mb-4">Vessel Dimensions</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-2">
                <label class="text-sm font-medium text-gray-700">Distance GPS to Front (m)</label>
                <BarretNumberBox Value="@Vessel.Dimensions.DistanceGpsToFront"
                                ValueChanged="@(value => UpdateDimension(value, nameof(Vessel.Dimensions.DistanceGpsToFront)))"
                                CssClass="w-full h-11 rounded-lg border-gray-200 focus:border-gray-300 focus:ring focus:ring-gray-200 focus:ring-opacity-50" />
            </div>
            <div class="space-y-2">
                <label class="text-sm font-medium text-gray-700">Distance GPS to Back (m)</label>
                <BarretNumberBox Value="@Vessel.Dimensions.DistanceGpsToBack"
                                ValueChanged="@(value => UpdateDimension(value, nameof(Vessel.Dimensions.DistanceGpsToBack)))"
                                CssClass="w-full h-11 rounded-lg border-gray-200 focus:border-gray-300 focus:ring focus:ring-gray-200 focus:ring-opacity-50" />
            </div>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
            <div class="space-y-2">
                <label class="text-sm font-medium text-gray-700">Distance GPS to Left (m)</label>
                <BarretNumberBox Value="@Vessel.Dimensions.DistanceGpsToLeft"
                                ValueChanged="@(value => UpdateDimension(value, nameof(Vessel.Dimensions.DistanceGpsToLeft)))"
                                CssClass="w-full h-11 rounded-lg border-gray-200 focus:border-gray-300 focus:ring focus:ring-gray-200 focus:ring-opacity-50" />
            </div>
            <div class="space-y-2">
                <label class="text-sm font-medium text-gray-700">Distance GPS to Right (m)</label>
                <BarretNumberBox Value="@Vessel.Dimensions.DistanceGpsToRight"
                                ValueChanged="@(value => UpdateDimension(value, nameof(Vessel.Dimensions.DistanceGpsToRight)))"
                                CssClass="w-full h-11 rounded-lg border-gray-200 focus:border-gray-300 focus:ring focus:ring-gray-200 focus:ring-opacity-50" />
            </div>
        </div>
    </div>

    @* <!-- Wizard Navigation -->
    <div class="flex justify-end pt-8 border-t border-gray-100">
        <button class="flex items-center gap-2 px-4 py-2 rounded-full bg-gray-900 text-white hover:bg-gray-800"
                @onclick="NavigateToNextTab">
            Next
            <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="9 18 15 12 9 6"></polyline>
            </svg>
        </button>
    </div> *@
</div>

@code {
    private void NavigateToNextTab()
    {
        // This would need to be implemented based on your navigation logic
        // For now, we'll just call the OnChange callback
        OnChange.InvokeAsync();
    }
}
