using Barret.Core.Areas.Devices.Enums;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Barret.Web.Server.Features.Vehicles.Editor.Components.Devices.Components.Common
{
    /// <summary>
    /// Base class for the device role selector component.
    /// </summary>
    public class DeviceRoleSelectorBase : ComponentBase
    {
        /// <summary>
        /// Gets or sets the allowed roles.
        /// </summary>
        [Parameter]
        public List<DeviceRole> AllowedRoles { get; set; } = new();

        /// <summary>
        /// Gets or sets the callback for when a role is selected.
        /// </summary>
        [Parameter]
        public EventCallback<DeviceRole> OnRoleSelected { get; set; }

        /// <summary>
        /// Gets or sets the callback for when the selection is cancelled.
        /// </summary>
        [Parameter]
        public EventCallback OnCancelled { get; set; }

        /// <summary>
        /// Gets or sets the logger.
        /// </summary>
        [Inject]
        protected ILogger<DeviceRoleSelectorBase> Logger { get; set; } = null!;

        /// <summary>
        /// Gets or sets a value indicating whether the selector is visible.
        /// </summary>
        protected bool IsVisible { get; set; }

        /// <summary>
        /// Event callback for when the visibility changes.
        /// </summary>
        protected void VisibleChanged(bool value)
        {
            IsVisible = value;
            StateHasChanged();
        }

        /// <summary>
        /// Gets or sets the filtered roles.
        /// </summary>
        protected List<DeviceRoleViewModel> FilteredRoles { get; set; } = new();

        /// <summary>
        /// Gets or sets the search text.
        /// </summary>
        protected string SearchText { get; set; } = string.Empty;

        /// <summary>
        /// Opens the selector.
        /// </summary>
        public void Open()
        {
            IsVisible = true;
            SearchText = string.Empty;
            UpdateFilteredRoles();
            StateHasChanged();
        }

        /// <summary>
        /// Closes the selector.
        /// </summary>
        public void Close()
        {
            IsVisible = false;
            StateHasChanged();
        }

        /// <summary>
        /// Updates the filtered roles based on the search text.
        /// </summary>
        protected void UpdateFilteredRoles()
        {
            FilteredRoles = AllowedRoles
                .Select(role => new DeviceRoleViewModel
                {
                    Role = role,
                    DisplayName = GetDisplayName(role),
                    IconCssClass = GetIconClass(role)
                })
                .Where(vm => string.IsNullOrWhiteSpace(SearchText) ||
                             vm.DisplayName.Contains(SearchText, StringComparison.OrdinalIgnoreCase))
                .OrderBy(vm => vm.DisplayName)
                .ToList();
        }

        /// <summary>
        /// Handles search text change.
        /// </summary>
        protected void OnSearchTextChanged(string value)
        {
            SearchText = value;
            UpdateFilteredRoles();
        }

        /// <summary>
        /// Handles role selection.
        /// </summary>
        /// <param name="role">The selected role.</param>
        protected async Task SelectRoleAsync(DeviceRole role)
        {
            Logger.LogInformation("Selected device role: {DeviceRole}", role);
            IsVisible = false;
            if (OnRoleSelected.HasDelegate)
            {
                await OnRoleSelected.InvokeAsync(role);
            }
        }

        /// <summary>
        /// Handles cancellation.
        /// </summary>
        protected async Task CancelAsync()
        {
            Logger.LogInformation("Device role selection cancelled");
            IsVisible = false;
            if (OnCancelled.HasDelegate)
            {
                await OnCancelled.InvokeAsync();
            }
        }

        /// <summary>
        /// Gets the display name for a device role.
        /// </summary>
        /// <param name="role">The device role.</param>
        /// <returns>The display name.</returns>
        private string GetDisplayName(DeviceRole role)
        {
            return role.ToString();
        }

        /// <summary>
        /// Gets the icon class for a device role.
        /// </summary>
        /// <param name="role">The device role.</param>
        /// <returns>The icon class.</returns>
        private string GetIconClass(DeviceRole role)
        {
            return role switch
            {
                DeviceRole.Camera => "bi bi-camera-video",
                DeviceRole.Engine => "bi bi-gear",
                DeviceRole.Thruster => "bi bi-arrow-up-circle",
                DeviceRole.Radar => "bi bi-radar",
                DeviceRole.Antenna => "bi bi-broadcast",
                DeviceRole.Autopilot => "bi bi-compass",
                DeviceRole.Light => "bi bi-lightbulb",
                DeviceRole.Rudder => "bi bi-arrow-left-right",
                DeviceRole.Horn => "bi bi-megaphone",
                DeviceRole.VHFMariphone => "bi bi-telephone",
                DeviceRole.AudioHub => "bi bi-speaker",
                DeviceRole.Plc => "bi bi-cpu",
                _ => "bi bi-device-hdd"
            };
        }
    }

    /// <summary>
    /// View model for a device role.
    /// </summary>
    public class DeviceRoleViewModel
    {
        /// <summary>
        /// Gets or sets the device role.
        /// </summary>
        public DeviceRole Role { get; set; }

        /// <summary>
        /// Gets or sets the display name.
        /// </summary>
        public string DisplayName { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the icon CSS class.
        /// </summary>
        public string IconCssClass { get; set; } = string.Empty;
    }
}
