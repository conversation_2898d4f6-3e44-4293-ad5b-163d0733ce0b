using Barret.Core.Areas.Devices.Enums;
using Barret.Shared.DTOs.Devices;
using Barret.Web.Server.Shared.Components.DeviceEditors.Tabs;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Barret.Web.Server.Features.Vehicles.Editor.Components.Devices.Services
{
    /// <summary>
    /// Service for managing device editor tabs.
    /// </summary>
    public class DeviceEditorTabService
    {
        private readonly ILogger<DeviceEditorTabService> _logger;
        private readonly Dictionary<DeviceRole, DeviceTabConfiguration> _tabConfigurations;

        /// <summary>
        /// Initializes a new instance of the <see cref="DeviceEditorTabService"/> class.
        /// </summary>
        /// <param name="logger">The logger.</param>
        public DeviceEditorTabService(ILogger<DeviceEditorTabService> logger)
        {
            _logger = logger;
            _tabConfigurations = InitializeTabConfigurations();
        }

        /// <summary>
        /// Gets the tabs for a device.
        /// </summary>
        /// <param name="device">The device.</param>
        /// <returns>The tabs for the device.</returns>
        public List<DeviceTabInfo> GetTabsForDevice(DeviceDto device)
        {
            if (device == null)
            {
                _logger.LogWarning("Attempted to get tabs for null device");
                return new List<DeviceTabInfo>();
            }

            var tabs = new List<DeviceTabInfo>
            {
                new DeviceTabInfo { Name = "General", ComponentType = typeof(BasicInfoTab) },
                new DeviceTabInfo { Name = "Make/Model", ComponentType = typeof(ModelTab) }
            };

            // Add position tab based on device type
            if (SupportsMaritimePosition(device.DeviceRole))
            {
                tabs.Add(new DeviceTabInfo { Name = "Position", ComponentType = typeof(MaritimePositionTab) });
            }
            else
            {
                tabs.Add(new DeviceTabInfo { Name = "Position", ComponentType = typeof(PositionTab) });
            }

            tabs.Add(new DeviceTabInfo { Name = "Connection", ComponentType = typeof(ConnectionTab) });

            // Add device-specific tabs
            var config = GetConfigurationForRole(device.DeviceRole);
            if (config.SettingsTabType != null)
            {
                tabs.Add(new DeviceTabInfo { Name = "Settings", ComponentType = config.SettingsTabType });
            }

            return tabs;
        }

        /// <summary>
        /// Determines if a device role supports maritime positioning
        /// </summary>
        /// <param name="role">The device role</param>
        /// <returns>True if the device supports maritime positioning</returns>
        private bool SupportsMaritimePosition(DeviceRole role)
        {
            return role switch
            {
                DeviceRole.Radar => true,
                _ => false
            };
        }

        /// <summary>
        /// Gets the configuration for a device role.
        /// </summary>
        /// <param name="role">The device role.</param>
        /// <returns>The configuration for the device role.</returns>
        public DeviceTabConfiguration GetConfigurationForRole(DeviceRole role)
        {
            if (_tabConfigurations.TryGetValue(role, out var config))
            {
                return config;
            }

            // Return a default configuration if the role is not found
            _logger.LogWarning("No tab configuration found for device role {DeviceRole}", role);
            return new DeviceTabConfiguration
            {
                SettingsTabType = null,
                SupportsAlarms = false
            };
        }

        /// <summary>
        /// Initializes the tab configurations.
        /// </summary>
        /// <returns>The tab configurations.</returns>
        private Dictionary<DeviceRole, DeviceTabConfiguration> InitializeTabConfigurations()
        {
            return new Dictionary<DeviceRole, DeviceTabConfiguration>
            {
                {
                    DeviceRole.Camera,
                    new DeviceTabConfiguration
                    {
                        SettingsTabType = typeof(CameraTab),
                        SupportsAlarms = true
                    }
                },
                {
                    DeviceRole.Engine,
                    new DeviceTabConfiguration
                    {
                        SettingsTabType = typeof(EngineTab),
                        SupportsAlarms = true
                    }
                },
                {
                    DeviceRole.Thruster,
                    new DeviceTabConfiguration
                    {
                        SettingsTabType = typeof(ThrusterTab),
                        SupportsAlarms = true
                    }
                },
                {
                    DeviceRole.Radar,
                    new DeviceTabConfiguration
                    {
                        SettingsTabType = typeof(RadarTab),
                        SupportsAlarms = true
                    }
                },
                {
                    DeviceRole.Light,
                    new DeviceTabConfiguration
                    {
                        SettingsTabType = typeof(LightTab),
                        SupportsAlarms = false
                    }
                },
                {
                    DeviceRole.Rudder,
                    new DeviceTabConfiguration
                    {
                        SettingsTabType = typeof(RudderTab),
                        SupportsAlarms = true
                    }
                },
                {
                    DeviceRole.Horn,
                    new DeviceTabConfiguration
                    {
                        SettingsTabType = typeof(HornTab),
                        SupportsAlarms = false
                    }
                },
                {
                    DeviceRole.Antenna,
                    new DeviceTabConfiguration
                    {
                        SettingsTabType = typeof(AntennaTab),
                        SupportsAlarms = false
                    }
                },
                {
                    DeviceRole.Autopilot,
                    new DeviceTabConfiguration
                    {
                        SettingsTabType = typeof(AutopilotTab),
                        SupportsAlarms = true
                    }
                },
                {
                    DeviceRole.Generic,
                    new DeviceTabConfiguration
                    {
                        SettingsTabType = null,
                        SupportsAlarms = true
                    }
                },
                {
                    DeviceRole.Sensor,
                    new DeviceTabConfiguration
                    {
                        SettingsTabType = null,
                        SupportsAlarms = true
                    }
                },
                {
                    DeviceRole.AMP,
                    new DeviceTabConfiguration
                    {
                        SettingsTabType = null,
                        SupportsAlarms = true
                    }
                }
            };
        }
    }

    /// <summary>
    /// Information about a device tab.
    /// </summary>
    public class DeviceTabInfo
    {
        /// <summary>
        /// Gets or sets the name of the tab.
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the type of the component to render.
        /// </summary>
        public Type ComponentType { get; set; } = null!;
    }

    /// <summary>
    /// Configuration for a device tab.
    /// </summary>
    public class DeviceTabConfiguration
    {
        /// <summary>
        /// Gets or sets the type of the settings tab.
        /// </summary>
        public Type? SettingsTabType { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the device supports alarms.
        /// </summary>
        public bool SupportsAlarms { get; set; }
    }
}
