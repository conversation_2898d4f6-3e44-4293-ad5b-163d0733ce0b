using Barret.Core.Areas.Devices.Enums;
using Barret.Services.Core.Areas.DeviceModels.Queries;
using Barret.Services.Core.Areas.Manufacturers;
using Barret.Shared.DTOs.Devices;
using Barret.Web.Server.Features.Shared;
using Barret.Web.Server.Services;
using DevExpress.Blazor;
using Microsoft.Extensions.Logging;
using ReactiveUI;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reactive;
using System.Reactive.Disposables;
using System.Reactive.Linq;
using System.Threading.Tasks;

namespace Barret.Web.Server.Features.Vehicles.Editor.Components.Devices.ViewModels
{
    /// <summary>
    /// ViewModel for the device editor.
    /// </summary>
    public class DeviceEditorViewModel : ViewModelBase, IDisposable
    {
        private readonly IDeviceModelQueryService _deviceModelQueryService;
        private readonly IManufacturerService _manufacturerService;
        private readonly IBarretToastNotificationService _toastService;
        private readonly CompositeDisposable _disposables = new();

        private DeviceDto _device = new DeviceDto();
        private bool _isVisible;
        private bool _isAdding;
        private bool _isSaving;
        private bool _hasError;
        private string _errorMessage = string.Empty;
        private int _activeTabIndex;
        private List<ManufacturerInfo> _manufacturers = new();
        private List<DeviceModelInfo> _filteredModels = new();
        private Guid _selectedManufacturerId = Guid.Empty;

        /// <summary>
        /// Gets or sets the device being edited.
        /// </summary>
        public DeviceDto Device
        {
            get => _device;
            set => this.RaiseAndSetIfChanged(ref _device, value);
        }

        /// <summary>
        /// Gets or sets a value indicating whether the editor is visible.
        /// </summary>
        public bool IsVisible
        {
            get => _isVisible;
            set => this.RaiseAndSetIfChanged(ref _isVisible, value);
        }

        /// <summary>
        /// Gets or sets a value indicating whether a new device is being added.
        /// </summary>
        public bool IsAdding
        {
            get => _isAdding;
            set => this.RaiseAndSetIfChanged(ref _isAdding, value);
        }

        /// <summary>
        /// Gets or sets a value indicating whether the device is being saved.
        /// </summary>
        public bool IsSaving
        {
            get => _isSaving;
            set => this.RaiseAndSetIfChanged(ref _isSaving, value);
        }

        /// <summary>
        /// Gets or sets a value indicating whether the ViewModel has an error.
        /// </summary>
        public bool HasError
        {
            get => _hasError;
            set => this.RaiseAndSetIfChanged(ref _hasError, value);
        }

        /// <summary>
        /// Gets or sets the error message if an operation fails.
        /// </summary>
        public string ErrorMessage
        {
            get => _errorMessage;
            set => this.RaiseAndSetIfChanged(ref _errorMessage, value);
        }

        /// <summary>
        /// Gets or sets the active tab index.
        /// </summary>
        public int ActiveTabIndex
        {
            get => _activeTabIndex;
            set => this.RaiseAndSetIfChanged(ref _activeTabIndex, value);
        }

        /// <summary>
        /// Gets or sets the manufacturers.
        /// </summary>
        public List<ManufacturerInfo> Manufacturers
        {
            get => _manufacturers;
            set => this.RaiseAndSetIfChanged(ref _manufacturers, value);
        }

        /// <summary>
        /// Gets or sets the filtered device models.
        /// </summary>
        public List<DeviceModelInfo> FilteredModels
        {
            get => _filteredModels;
            set => this.RaiseAndSetIfChanged(ref _filteredModels, value);
        }

        /// <summary>
        /// Gets or sets the selected manufacturer ID.
        /// </summary>
        public Guid SelectedManufacturerId
        {
            get => _selectedManufacturerId;
            set => this.RaiseAndSetIfChanged(ref _selectedManufacturerId, value);
        }

        /// <summary>
        /// Gets a value indicating whether the device is valid.
        /// </summary>
        public bool IsDeviceValid => ValidateDevice(false);

        /// <summary>
        /// Gets the command to save the device.
        /// </summary>
        public ReactiveCommand<Unit, DeviceDto> SaveCommand { get; }

        /// <summary>
        /// Gets the command to cancel editing.
        /// </summary>
        public ReactiveCommand<Unit, Unit> CancelCommand { get; }

        /// <summary>
        /// Event that is raised when device properties change.
        /// </summary>
        public event Action<DeviceDto>? DevicePropertyChanged;

        /// <summary>
        /// Initializes a new instance of the <see cref="DeviceEditorViewModel"/> class.
        /// </summary>
        /// <param name="deviceModelQueryService">The device model query service.</param>
        /// <param name="manufacturerService">The manufacturer service.</param>
        /// <param name="toastService">The toast notification service.</param>
        /// <param name="logger">The logger.</param>
        public DeviceEditorViewModel(
            IDeviceModelQueryService deviceModelQueryService,
            IManufacturerService manufacturerService,
            IBarretToastNotificationService toastService,
            ILogger<DeviceEditorViewModel> logger) : base(logger)
        {
            _deviceModelQueryService = deviceModelQueryService;
            _manufacturerService = manufacturerService;
            _toastService = toastService;

            // Initialize commands
            SaveCommand = ReactiveCommand.CreateFromTask(
                SaveDeviceAsync,
                this.WhenAnyValue(x => x.IsDeviceValid, x => x.IsSaving,
                    (isValid, isSaving) => isValid && !isSaving));

            CancelCommand = ReactiveCommand.Create(
                () =>
                {
                    IsVisible = false;
                    return Unit.Default;
                });

            // Handle command errors
            SaveCommand.ThrownExceptions
                .Subscribe(ex =>
                {
                    Logger.LogError(ex, "Error saving device");
                    SetError($"Save failed: {ex.Message}");
                })
                .DisposeWith(_disposables);

            CancelCommand.ThrownExceptions
                .Subscribe(ex =>
                {
                    Logger.LogError(ex, "Error canceling device edit");
                    SetError($"Cancel failed: {ex.Message}");
                })
                .DisposeWith(_disposables);
        }

        /// <summary>
        /// Opens the editor for adding a new device.
        /// </summary>
        /// <param name="device">The device to add.</param>
        /// <param name="manufacturers">The manufacturers.</param>
        public async Task OpenForAddAsync(DeviceDto device, List<ManufacturerInfo> manufacturers)
        {
            Device = device;
            Manufacturers = manufacturers;
            IsAdding = true;
            IsVisible = true;
            ActiveTabIndex = 0;
            SelectedManufacturerId = Guid.Empty;
            FilteredModels = new List<DeviceModelInfo>();

            // Update filtered models
            await UpdateFilteredModelsAsync();
        }

        /// <summary>
        /// Opens the editor for editing an existing device.
        /// </summary>
        /// <param name="device">The device to edit.</param>
        /// <param name="manufacturers">The manufacturers.</param>
        public async Task OpenForEditAsync(DeviceDto device, List<ManufacturerInfo> manufacturers)
        {
            Device = device;
            Manufacturers = manufacturers;
            IsAdding = false;
            IsVisible = true;
            ActiveTabIndex = 0;

            // Set the selected manufacturer based on the device's model if it exists
            if (Device.ManufacturerId.HasValue && Device.ManufacturerId.Value != Guid.Empty)
            {
                SelectedManufacturerId = Device.ManufacturerId.Value;
            }
            else if (Device.DeviceModelId.HasValue && Device.DeviceModelId.Value != Guid.Empty)
            {
                await SetSelectedManufacturerFromModelAsync();
            }
            else
            {
                SelectedManufacturerId = Guid.Empty;
            }

            // Update filtered models
            await UpdateFilteredModelsAsync();
        }

        /// <summary>
        /// Validates the device.
        /// </summary>
        /// <param name="showToasts">Whether to show toast notifications for validation errors.</param>
        /// <returns>True if the device is valid; otherwise, false.</returns>
        public bool ValidateDevice(bool showToasts)
        {
            if (Device == null)
            {
                if (showToasts)
                {
                    _toastService.ShowToast("Error", "Device is null", ToastType.Error);
                }
                return false;
            }

            // Check for required fields
            if (string.IsNullOrWhiteSpace(Device.Name))
            {
                if (showToasts)
                {
                    _toastService.ShowToast("Error", "Device name is required", ToastType.Error);
                }
                return false;
            }

            // Add more validation as needed for specific device types

            return true;
        }

        /// <summary>
        /// Saves the device.
        /// </summary>
        /// <returns>The saved device.</returns>
        private async Task<DeviceDto> SaveDeviceAsync()
        {
            try
            {
                IsSaving = true;
                ClearError();

                // Validate the device
                if (!ValidateDevice(true))
                {
                    SetError("Device validation failed");
                    return Device;
                }

                // Return the device (actual saving is handled by the parent component)
                return Device;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error saving device");
                SetError($"Save failed: {ex.Message}");
                throw;
            }
            finally
            {
                IsSaving = false;
            }
        }

        /// <summary>
        /// Updates the filtered device models based on the selected manufacturer.
        /// </summary>
        public async Task UpdateFilteredModelsAsync()
        {
            try
            {
                // Clear the filtered models
                FilteredModels = new List<DeviceModelInfo>();

                // Get device models from the selected manufacturer
                if (SelectedManufacturerId != Guid.Empty)
                {
                    Logger.LogInformation("Selected manufacturer ID: {ManufacturerId}", SelectedManufacturerId);

                    // Get device models for the selected manufacturer and device role
                    var models = await _deviceModelQueryService.GetDeviceModelsForManufacturerWithRoleAsync(
                        SelectedManufacturerId, Device.DeviceRole);

                    // Update the filtered models
                    FilteredModels = models.ToList();

                    Logger.LogInformation("Added {Count} device models to filtered list", FilteredModels.Count);
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error updating filtered models");
                _toastService.ShowToast("Error", $"Error loading device models: {ex.Message}", ToastType.Error);
            }
        }

        /// <summary>
        /// Sets the selected manufacturer based on the device's model.
        /// </summary>
        private async Task SetSelectedManufacturerFromModelAsync()
        {
            if (Device.DeviceModelId.HasValue && Device.DeviceModelId.Value != Guid.Empty)
            {
                try
                {
                    // Get the device model
                    var model = await _deviceModelQueryService.GetDeviceModelByIdAsync(Device.DeviceModelId.Value);
                    if (model != null && model.ManufacturerId != Guid.Empty)
                    {
                        // Set the selected manufacturer
                        SelectedManufacturerId = model.ManufacturerId;
                        Device.ManufacturerId = model.ManufacturerId;
                        Device.ManufacturerName = model.ManufacturerName;
                    }
                }
                catch (Exception ex)
                {
                    Logger.LogError(ex, "Error setting selected manufacturer from model");
                }
            }
        }



        /// <summary>
        /// Notifies that a device property has changed.
        /// This should be called when any device property is modified in the UI.
        /// </summary>
        public void NotifyDevicePropertyChanged()
        {
            if (Device != null)
            {
                DevicePropertyChanged?.Invoke(Device);
            }
        }

        /// <summary>
        /// Sets an error message and marks the ViewModel as having an error.
        /// </summary>
        /// <param name="message">The error message.</param>
        private void SetError(string message)
        {
            ErrorMessage = message;
            HasError = !string.IsNullOrEmpty(message);
        }

        /// <summary>
        /// Clears the error message and marks the ViewModel as not having an error.
        /// </summary>
        private void ClearError()
        {
            ErrorMessage = string.Empty;
            HasError = false;
        }

        /// <summary>
        /// Disposes resources.
        /// </summary>
        public void Dispose()
        {
            _disposables.Dispose();
            GC.SuppressFinalize(this);
        }
    }
}
