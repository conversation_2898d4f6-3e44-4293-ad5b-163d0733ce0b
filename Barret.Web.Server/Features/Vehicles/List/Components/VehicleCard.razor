@using Barret.Web.Server.Features.Vehicles.Data
@using Barret.Web.Server.Features.Shared.Components.BarretDevExpress

<div class="group relative bg-white rounded-xl overflow-hidden border-0 transition-all duration-500 h-full card-with-shadow" @onclick="() => OnConfigureClick.InvokeAsync(Vehicle.Id)">
    <!-- Image Section -->
    <div class="relative h-48 bg-gray-100 overflow-hidden">
        <img src="/images/seafar_placeholder_grey.svg"
             alt="@Vehicle.Name"
             class="w-full h-full object-cover transform group-hover:scale-105 transition-transform duration-500" />

        <div class="absolute top-3 right-3 bg-white/90 backdrop-blur-sm rounded-full px-3 py-1 text-sm font-medium text-gray-700">
            @Vehicle.DeviceCount Devices
        </div>
    </div>

    <!-- Content Section -->
    <div class="px-4 py-3">
        <!-- Header -->
        <div class="flex justify-between items-center mb-1">
            <h4 class="text-lg font-medium text-gray-900 group-hover:text-gray-700 transition-colors duration-200">
                @Vehicle.Name
            </h4>
            <div class="bg-gray-100 rounded-full p-1.5 group-hover:bg-gray-200 transition-colors duration-200" @onclick:stopPropagation="true">
                <i class="@Vehicle.IconCssClass h-4 w-4 text-gray-700"></i>
            </div>
        </div>

        <!-- Description -->
        <p class="text-sm text-gray-500">@Vehicle.VehicleId</p>

        <!-- Footer -->
        <div class="flex justify-between items-center mt-3">
            <div class="flex items-center gap-2" @onclick:stopPropagation="true">
                <button class="text-gray-700 hover:text-gray-900 hover:bg-gray-100 rounded-full h-8 w-8 p-0 inline-flex items-center justify-center transition-colors duration-200"
                       title="Copy"
                       @onclick="@(() => OnCopyClick.InvokeAsync(Vehicle.Id))">
                    <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                        <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                    </svg>
                </button>
                <button class="text-gray-700 hover:text-gray-900 hover:bg-gray-100 rounded-full h-8 w-8 p-0 inline-flex items-center justify-center transition-colors duration-200"
                       title="Delete"
                       @onclick="@(() => OnDeleteClick.InvokeAsync(Vehicle.Id))">
                    <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <polyline points="3 6 5 6 21 6"></polyline>
                        <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                        <line x1="10" y1="11" x2="10" y2="17"></line>
                        <line x1="14" y1="11" x2="14" y2="17"></line>
                    </svg>
                </button>
            </div>
            <button class="text-gray-700 hover:text-gray-900 hover:bg-gray-100 rounded-full h-8 px-3 flex items-center gap-1 text-sm transition-colors duration-200"
                   @onclick:stopPropagation="true"
                   @onclick="@(() => OnConfigureClick.InvokeAsync(Vehicle.Id))">
                Configure
                <svg class="h-4 w-4 transform group-hover:translate-x-1 transition-transform duration-200" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <line x1="5" y1="12" x2="19" y2="12"></line>
                    <polyline points="12 5 19 12 12 19"></polyline>
                </svg>
            </button>
        </div>
    </div>
</div>
