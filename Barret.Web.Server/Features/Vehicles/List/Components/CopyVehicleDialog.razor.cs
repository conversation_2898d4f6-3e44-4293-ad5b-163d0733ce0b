using Microsoft.AspNetCore.Components;
using System.Threading.Tasks;

namespace Barret.Web.Server.Features.Vehicles.List.Components
{
    /// <summary>
    /// Code-behind for the CopyVehicleDialog component.
    /// </summary>
    public partial class CopyVehicleDialog : ComponentBase
    {
        /// <summary>
        /// Gets or sets whether the dialog is visible.
        /// </summary>
        [Parameter]
        public bool IsVisible { get; set; }

        /// <summary>
        /// Gets or sets the callback for when the visibility changes.
        /// </summary>
        [Parameter]
        public EventCallback<bool> IsVisibleChanged { get; set; }

        /// <summary>
        /// Gets or sets the name of the original vehicle being copied.
        /// </summary>
        [Parameter]
        public string OriginalName { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the callback for when the copy is confirmed.
        /// </summary>
        [Parameter]
        public EventCallback<string> OnConfirm { get; set; }

        /// <summary>
        /// Gets or sets the callback for when the copy is canceled.
        /// </summary>
        [Parameter]
        public EventCallback OnCancel { get; set; }

        /// <summary>
        /// Gets or sets the CSS class for the dialog.
        /// </summary>
        [Parameter]
        public string DialogClass { get; set; } = "bg-white rounded-xl shadow-lg";

        /// <summary>
        /// Gets or sets the CSS class for the confirm button.
        /// </summary>
        [Parameter]
        public string ConfirmButtonClass { get; set; } = "bg-gray-900 hover:bg-gray-800 text-white rounded-full px-4 py-2";

        /// <summary>
        /// Gets or sets the CSS class for the cancel button.
        /// </summary>
        [Parameter]
        public string CancelButtonClass { get; set; } = "border border-gray-300 text-gray-700 hover:bg-gray-50 rounded-full px-4 py-2";

        /// <summary>
        /// Gets or sets the name for the new vehicle.
        /// </summary>
        protected string VehicleName { get; set; } = string.Empty;

        /// <summary>
        /// Method called when the component parameters are set.
        /// </summary>
        protected override void OnParametersSet()
        {
            if (IsVisible && !string.IsNullOrEmpty(OriginalName) && string.IsNullOrEmpty(VehicleName))
            {
                // Set default name when popup becomes visible
                VehicleName = $"{OriginalName} (Copy)";
            }
        }

        /// <summary>
        /// Handles the confirm button click.
        /// </summary>
        protected async Task HandleConfirm()
        {
            if (!string.IsNullOrWhiteSpace(VehicleName))
            {
                await OnConfirm.InvokeAsync(VehicleName);
                await IsVisibleChanged.InvokeAsync(false);
                // Reset the name after confirmation
                VehicleName = string.Empty;
            }
        }

        /// <summary>
        /// Handles the cancel button click.
        /// </summary>
        protected async Task HandleCancel()
        {
            await OnCancel.InvokeAsync();
            await IsVisibleChanged.InvokeAsync(false);
            // Reset the name after cancellation
            VehicleName = string.Empty;
        }
    }
}
