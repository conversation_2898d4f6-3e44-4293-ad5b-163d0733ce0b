@using Barret.Web.Server.Features.Shared.Components.BarretDevExpress
@using DevExpress.Blazor

<DxPopup @bind-Visible="@IsVisible"
         HeaderText="Copy Vehicle"
         ShowFooter="true"
         Width="400px"
         CloseOnEscape="true"
         CloseOnOutsideClick="false"
         ShowCloseButton="true"
         CssClass="@DialogClass">
    <HeaderTemplate>
        <div class="flex items-center">
            <i class="bi bi-copy mr-2 text-gray-700"></i>
            <span class="text-gray-900 font-medium">Copy Vehicle</span>
        </div>
    </HeaderTemplate>
    <Content>
        <div class="p-4">
            <div class="text-center mb-4">
                <div class="mx-auto w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-2">
                    <i class="bi bi-copy text-2xl text-gray-700"></i>
                </div>
            </div>
            <p class="text-gray-700 mb-4">Please enter a name for the copied vehicle:</p>
            <div class="mb-4">
                <label for="vehicleName" class="block text-sm font-medium text-gray-700 mb-1">Vehicle Name</label>
                <BarretTextBox Text="@VehicleName"
                              TextChanged="@((value) => VehicleName = value)"
                              Placeholder="Enter vehicle name"
                              CssClass="w-full rounded-md border-gray-300 focus:border-gray-400 focus:ring focus:ring-gray-200 focus:ring-opacity-50" />
            </div>
        </div>
    </Content>
    <FooterTemplate>
        <div class="flex justify-end gap-3">
            <BarretActionButton Text="Cancel"
                              CssClass="@CancelButtonClass"
                              OnClick="HandleCancel" />
            <BarretActionButton Text="Copy"
                              CssClass="@ConfirmButtonClass"
                              OnClick="HandleConfirm"
                              Enabled="@(!string.IsNullOrWhiteSpace(VehicleName))" />
        </div>
    </FooterTemplate>
</DxPopup>
