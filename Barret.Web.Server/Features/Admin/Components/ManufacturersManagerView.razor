@using Barret.Shared.DTOs.Devices
@using Barret.Services.Core.Areas.Manufacturers
@using Barret.Core.Areas.Devices.Enums
@using DevExpress.Blazor
@using DevExpress.Blazor.Grid
@inherits ManufacturersManagerViewBase

<div class="space-y-6">
    <!-- Action Bar -->
    <div class="bg-white rounded-xl p-4 border border-gray-100 shadow-sm">
        <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div>
                <h3 class="text-xl font-medium text-gray-900">@Title</h3>
                <p class="text-sm text-gray-500">Manage manufacturers and their device models</p>
            </div>
            <div class="flex items-center gap-3">
                <button class="flex items-center gap-2 h-10 px-4 rounded-full bg-gray-900 text-white hover:bg-gray-800 transition-colors"
                        @onclick="OpenAddManufacturerDialog">
                    <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <line x1="12" y1="5" x2="12" y2="19"></line>
                        <line x1="5" y1="12" x2="19" y2="12"></line>
                    </svg>
                    <span>Add Manufacturer</span>
                </button>
            </div>
        </div>
    </div>

    @if (IsLoading)
    {
        <div class="flex justify-center items-center py-12">
            <div class="h-12 w-12 border-4 border-gray-200 border-t-gray-800 rounded-full animate-spin"></div>
        </div>
    }
    else if (!Manufacturers.Any())
    {
        <div class="flex flex-col items-center justify-center py-16 bg-white rounded-xl border border-gray-100 shadow-sm text-center">
            <div class="bg-gray-50 p-5 rounded-full mb-4">
                <svg class="h-14 w-14 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                    <rect width="7" height="7" x="3" y="3" rx="1"></rect>
                    <rect width="7" height="7" x="14" y="3" rx="1"></rect>
                    <rect width="7" height="7" x="14" y="14" rx="1"></rect>
                    <rect width="7" height="7" x="3" y="14" rx="1"></rect>
                </svg>
            </div>
            <h3 class="text-2xl font-medium text-gray-900 mb-2">No Manufacturers</h3>
            <p class="text-gray-500 mb-8 max-w-md mx-auto">
                There are no manufacturers in the system yet. Add a manufacturer to get started.
            </p>
            <button class="flex items-center gap-2 h-10 px-5 rounded-full bg-gray-900 text-white hover:bg-gray-800 transition-colors mx-auto"
                    @onclick="OpenAddManufacturerDialog">
                <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <line x1="12" y1="5" x2="12" y2="19"></line>
                    <line x1="5" y1="12" x2="19" y2="12"></line>
                </svg>
                <span>Add Manufacturer</span>
            </button>
        </div>
    }
    else
    {
        <DxGrid Data="@Manufacturers"
                @ref="Grid"
                CssClass="barret-data-grid bg-white rounded-xl overflow-hidden border border-gray-100"
                ShowFilterRow="true"
                ShowPager="true"
                PageSize="10"
                KeyFieldName="Id"
                AutoCollapseDetailRow="false"
                DetailRowDisplayMode="GridDetailRowDisplayMode.Auto">
            <Columns>
                <DxGridDataColumn FieldName="Name" Caption="Manufacturer Name" Width="60%">
                    <CellDisplayTemplate>
                        @{
                            var manufacturer = (ManufacturerInfo)context.DataItem;
                            <div class="flex items-center">
                                <span class="text-gray-900 font-medium">@manufacturer.Name</span>
                            </div>
                        }
                    </CellDisplayTemplate>
                </DxGridDataColumn>
                <DxGridDataColumn Caption="Actions" Width="40%">
                    <CellDisplayTemplate>
                        @{
                            var manufacturer = (ManufacturerInfo)context.DataItem;
                            var rowVisibleIndex = context.VisibleIndex;

                            <div class="flex items-center justify-end space-x-2">
                                <button class="flex items-center gap-1 px-3 py-1.5 rounded text-blue-600 hover:bg-blue-50 transition-colors"
                                        @onclick="() => ToggleDetailRow(rowVisibleIndex)">
                                    <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        @if (Grid.IsDetailRowExpanded(rowVisibleIndex))
                                        {
                                            <path d="M18 15l-6-6-6 6"/>
                                        }
                                        else
                                        {
                                            <path d="M6 9l6 6 6-6"/>
                                        }
                                    </svg>
                                    <span>@(Grid.IsDetailRowExpanded(rowVisibleIndex) ? "Hide" : "Show") Models</span>
                                </button>
                                <button class="flex items-center gap-1 px-3 py-1.5 rounded text-blue-600 hover:bg-blue-50 transition-colors"
                                        @onclick="() => EditManufacturer(manufacturer)">
                                    <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                                        <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                                    </svg>
                                    <span>Edit</span>
                                </button>
                                <button class="flex items-center gap-1 px-3 py-1.5 rounded text-red-600 hover:bg-red-50 transition-colors"
                                        @onclick="() => DeleteManufacturer(manufacturer)">
                                    <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M3 6h18"></path>
                                        <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6"></path>
                                        <path d="M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                                        <line x1="10" y1="11" x2="10" y2="17"></line>
                                        <line x1="14" y1="11" x2="14" y2="17"></line>
                                    </svg>
                                    <span>Delete</span>
                                </button>
                            </div>
                        }
                    </CellDisplayTemplate>
                </DxGridDataColumn>
            </Columns>
            <DetailRowTemplate>
                <DeviceModelsManagerView ManufacturerId="@((context.DataItem as ManufacturerInfo)?.Id ?? Guid.Empty)"
                                        ManufacturerName="@((context.DataItem as ManufacturerInfo)?.Name ?? string.Empty)" />
            </DetailRowTemplate>
        </DxGrid>
    }
</div>

<!-- Add/Edit Manufacturer Dialog -->
<DxPopup @bind-Visible="@isManufacturerDialogVisible"
         HeaderText="@manufacturerDialogTitle"
         ShowFooter="true"
         Width="500px"
         CloseOnEscape="true"
         CloseOnOutsideClick="false"
         CssClass="rounded-lg">
    <HeaderTemplate>
        <div class="flex items-center">
            <svg class="h-6 w-6 mr-2 text-blue-600" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <rect width="7" height="7" x="3" y="3" rx="1"></rect>
                <rect width="7" height="7" x="14" y="3" rx="1"></rect>
                <rect width="7" height="7" x="14" y="14" rx="1"></rect>
                <rect width="7" height="7" x="3" y="14" rx="1"></rect>
            </svg>
            <span class="text-gray-900 font-medium text-lg">@manufacturerDialogTitle</span>
        </div>
    </HeaderTemplate>
    <Content>
        <div class="p-6">
            <div class="mb-4">
                <label for="manufacturerName" class="block text-sm font-medium text-gray-700 mb-1">Manufacturer Name</label>
                <input type="text" id="manufacturerName"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                       @bind="manufacturerName"
                       placeholder="Enter manufacturer name" />
                @if (!string.IsNullOrEmpty(manufacturerNameError))
                {
                    <p class="mt-1 text-sm text-red-600">@manufacturerNameError</p>
                }
            </div>
        </div>
    </Content>
    <FooterTemplate>
        <div class="flex justify-end gap-3 p-4 bg-gray-50 rounded-b-lg">
            <button class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors"
                    @onclick="CloseManufacturerDialog">
                Cancel
            </button>
            <button class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none transition-colors"
                    @onclick="SaveManufacturer"
                    disabled="@string.IsNullOrWhiteSpace(manufacturerName)">
                <div class="flex items-center">
                    <svg class="h-4 w-4 mr-1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path>
                        <polyline points="17 21 17 13 7 13 7 21"></polyline>
                        <polyline points="7 3 7 8 15 8"></polyline>
                    </svg>
                    Save
                </div>
            </button>
        </div>
    </FooterTemplate>
</DxPopup>

<!-- Delete Confirmation Dialog -->
<DxPopup @bind-Visible="@isDeleteConfirmationVisible"
         HeaderText="Confirm Deletion"
         ShowFooter="true"
         Width="450px"
         CloseOnEscape="true"
         CloseOnOutsideClick="false"
         CssClass="rounded-lg">
    <HeaderTemplate>
        <div class="flex items-center">
            <svg class="h-6 w-6 mr-2 text-red-600" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
                <line x1="12" y1="9" x2="12" y2="13"></line>
                <line x1="12" y1="17" x2="12.01" y2="17"></line>
            </svg>
            <span class="text-gray-900 font-medium text-lg">Confirm Deletion</span>
        </div>
    </HeaderTemplate>
    <Content>
        <div class="p-6">
            <div class="flex items-start mb-4">
                <svg class="h-12 w-12 text-red-100 bg-red-600 p-2 rounded-full mr-4 flex-shrink-0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M3 6h18"></path>
                    <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6"></path>
                    <path d="M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                </svg>
                <div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Delete Manufacturer</h3>
                    <p class="text-gray-600">
                        Are you sure you want to delete the manufacturer "@(manufacturerToDelete?.Name)"? This action cannot be undone and will also delete all associated device models.
                    </p>
                </div>
            </div>
        </div>
    </Content>
    <FooterTemplate>
        <div class="flex justify-end gap-3 p-4 bg-gray-50 rounded-b-lg">
            <button class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors"
                    @onclick="() => isDeleteConfirmationVisible = false">
                Cancel
            </button>
            <button class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none transition-colors"
                    @onclick="ConfirmDeleteManufacturer">
                <div class="flex items-center">
                    <svg class="h-4 w-4 mr-1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M3 6h18"></path>
                        <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6"></path>
                        <line x1="10" y1="11" x2="10" y2="17"></line>
                        <line x1="14" y1="11" x2="14" y2="17"></line>
                    </svg>
                    Delete
                </div>
            </button>
        </div>
    </FooterTemplate>
</DxPopup>
