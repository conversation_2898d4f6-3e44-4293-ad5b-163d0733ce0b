using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;

namespace Barret.Web.Server.Features.Shared
{
    /// <summary>
    /// Base class for all View code-behind files in the application.
    /// </summary>
    /// <typeparam name="TViewModel">The type of the ViewModel.</typeparam>
    public abstract class ViewBase<TViewModel> : ComponentBase, IDisposable where TViewModel : ViewModelBase
    {
        /// <summary>
        /// Gets or sets the logger for this view.
        /// </summary>
        [Inject]
        protected ILogger<ViewBase<TViewModel>> Logger { get; set; } = null!;

        /// <summary>
        /// Gets or sets the ViewModel for this view.
        /// </summary>
        [Parameter]
        public TViewModel ViewModel { get; set; } = null!;

        /// <summary>
        /// Method called when the component is initialized.
        /// </summary>
        protected override void OnInitialized()
        {
            base.OnInitialized();

            if (ViewModel == null)
            {
                throw new InvalidOperationException($"ViewModel of type {typeof(TViewModel).Name} is required.");
            }
        }

        /// <summary>
        /// Method called when the component parameters are set.
        /// </summary>
        protected override void OnParametersSet()
        {
            base.OnParametersSet();

            if (ViewModel == null)
            {
                throw new InvalidOperationException($"ViewModel of type {typeof(TViewModel).Name} is required.");
            }
        }

        /// <summary>
        /// Method called after the component is rendered.
        /// </summary>
        /// <param name="firstRender">True if this is the first time the component has been rendered.</param>
        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            await base.OnAfterRenderAsync(firstRender);

            if (firstRender)
            {
                await OnFirstRenderAsync();
            }
        }

        /// <summary>
        /// Method called after the component is rendered for the first time.
        /// </summary>
        protected virtual Task OnFirstRenderAsync()
        {
            return Task.CompletedTask;
        }

        /// <summary>
        /// Executes an asynchronous operation and logs any exceptions.
        /// </summary>
        /// <param name="operation">The operation to execute.</param>
        /// <param name="errorMessage">The error message to log if an exception occurs.</param>
        protected async Task ExecuteWithErrorHandlingAsync(Func<Task> operation, string errorMessage)
        {
            try
            {
                await operation();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, errorMessage);
                throw;
            }
        }

        /// <summary>
        /// Disposes of resources used by this component.
        /// </summary>
        public virtual void Dispose()
        {
            // Cleanup code here
            GC.SuppressFinalize(this);
        }
    }
}
