using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using ReactiveUI;

namespace Barret.Web.Server.Features.Shared
{
    /// <summary>
    /// Base class for all ViewModels in the application.
    /// Implements INotifyPropertyChanged for data binding.
    /// </summary>
    public abstract class ViewModelBase : ReactiveObject
    {
        /// <summary>
        /// Gets the logger for this ViewModel.
        /// </summary>
        protected ILogger Logger { get; }

        /// <summary>
        /// Initializes a new instance of the ViewModelBase class.
        /// </summary>
        /// <param name="logger">The logger to use.</param>
        protected ViewModelBase(ILogger logger)
        {
            Logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Event that is raised when a property value changes.
        /// </summary>
        public event PropertyChangedEventHandler? PropertyChanged;

        /// <summary>
        /// Raises the PropertyChanged event for the specified property.
        /// </summary>
        /// <param name="propertyName">The name of the property that changed.</param>
        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        /// <summary>
        /// Sets the value of a property and raises the PropertyChanged event if the value changed.
        /// </summary>
        /// <typeparam name="T">The type of the property.</typeparam>
        /// <param name="field">The field to set.</param>
        /// <param name="value">The new value.</param>
        /// <param name="propertyName">The name of the property.</param>
        /// <returns>True if the value changed, false otherwise.</returns>
        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (Equals(field, value))
            {
                return false;
            }

            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        /// <summary>
        /// Executes an asynchronous operation and logs any exceptions.
        /// </summary>
        /// <param name="operation">The operation to execute.</param>
        /// <param name="errorMessage">The error message to log if an exception occurs.</param>
        /// <returns>A task representing the asynchronous operation.</returns>
        protected async Task ExecuteWithErrorHandlingAsync(Func<Task> operation, string errorMessage)
        {
            try
            {
                await operation();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, errorMessage);
                throw;
            }
        }

        /// <summary>
        /// Executes an asynchronous operation that returns a value and logs any exceptions.
        /// </summary>
        /// <typeparam name="T">The type of the return value.</typeparam>
        /// <param name="operation">The operation to execute.</param>
        /// <param name="errorMessage">The error message to log if an exception occurs.</param>
        /// <returns>A task representing the asynchronous operation.</returns>
        protected async Task<T> ExecuteWithErrorHandlingAsync<T>(Func<Task<T>> operation, string errorMessage)
        {
            try
            {
                return await operation();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, errorMessage);
                throw;
            }
        }
    }
}
