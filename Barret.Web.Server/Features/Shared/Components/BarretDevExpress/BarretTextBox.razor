@using DevExpress.Blazor

<DxTextBox Text="@Text"
          TextChanged="@((string text) => OnTextChanged(text))"
          CssClass="@($"barret-textbox {CssClass}")"
          NullText="@Placeholder"
          ReadOnly="@ReadOnly"
          Enabled="@Enabled"
          ClearButtonDisplayMode="@ClearButtonDisplayMode"
          SizeMode="@SizeMode">
</DxTextBox>

@code {
    /// <summary>
    /// Gets or sets the text value.
    /// </summary>
    [Parameter]
    public string Text { get; set; } = string.Empty;

    /// <summary>
    /// Event callback for when the text changes.
    /// </summary>
    [Parameter]
    public EventCallback<string> TextChanged { get; set; }

    /// <summary>
    /// Gets or sets the placeholder text.
    /// </summary>
    [Parameter]
    public string Placeholder { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets whether the textbox is read-only.
    /// </summary>
    [Parameter]
    public bool ReadOnly { get; set; }

    /// <summary>
    /// Gets or sets whether the textbox is enabled.
    /// </summary>
    [Parameter]
    public bool Enabled { get; set; } = true;

    /// <summary>
    /// Gets or sets the CSS class.
    /// </summary>
    [Parameter]
    public string CssClass { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the clear button display mode.
    /// </summary>
    [Parameter]
    public DataEditorClearButtonDisplayMode ClearButtonDisplayMode { get; set; } = DataEditorClearButtonDisplayMode.Auto;

    /// <summary>
    /// Gets or sets the size mode.
    /// </summary>
    [Parameter]
    public SizeMode SizeMode { get; set; } = SizeMode.Medium;

    /// <summary>
    /// Handles the text changed event.
    /// </summary>
    /// <param name="newValue">The new text value.</param>
    private async Task OnTextChanged(string newValue)
    {
        if (Text != newValue)
        {
            Text = newValue;
            if (TextChanged.HasDelegate)
            {
                await TextChanged.InvokeAsync(newValue);
            }
        }
    }
}
