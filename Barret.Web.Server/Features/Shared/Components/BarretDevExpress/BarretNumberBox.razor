@using DevExpress.Blazor

<DxSpinEdit Value="@Value"
           ValueChanged="@((double value) => OnValueChanged(value))"
           CssClass="@($"barret-number-box {CssClass}")"
           NullText="@Placeholder"
           ReadOnly="@ReadOnly"
           Enabled="@Enabled"
           ClearButtonDisplayMode="@ClearButtonDisplayMode"
           SizeMode="@SizeMode"
           MinValue="@MinValue"
           MaxValue="@MaxValue"
           Step="@Step">
</DxSpinEdit>

@code {
    /// <summary>
    /// Gets or sets the value.
    /// </summary>
    [Parameter]
    public double Value { get; set; }

    /// <summary>
    /// Event callback for when the value changes.
    /// </summary>
    [Parameter]
    public EventCallback<double> ValueChanged { get; set; }

    /// <summary>
    /// Gets or sets the placeholder text.
    /// </summary>
    [Parameter]
    public string Placeholder { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets whether the number box is read-only.
    /// </summary>
    [Parameter]
    public bool ReadOnly { get; set; }

    /// <summary>
    /// Gets or sets whether the number box is enabled.
    /// </summary>
    [Parameter]
    public bool Enabled { get; set; } = true;

    /// <summary>
    /// Gets or sets the CSS class.
    /// </summary>
    [Parameter]
    public string CssClass { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the clear button display mode.
    /// </summary>
    [Parameter]
    public DataEditorClearButtonDisplayMode ClearButtonDisplayMode { get; set; } = DataEditorClearButtonDisplayMode.Auto;

    /// <summary>
    /// Gets or sets the size mode.
    /// </summary>
    [Parameter]
    public SizeMode SizeMode { get; set; } = SizeMode.Medium;

    /// <summary>
    /// Gets or sets the minimum value.
    /// </summary>
    [Parameter]
    public double MinValue { get; set; } = double.MinValue;

    /// <summary>
    /// Gets or sets the maximum value.
    /// </summary>
    [Parameter]
    public double MaxValue { get; set; } = double.MaxValue;

    /// <summary>
    /// Gets or sets the step value.
    /// </summary>
    [Parameter]
    public double Step { get; set; } = 1;

    /// <summary>
    /// Handles the value changed event.
    /// </summary>
    /// <param name="newValue">The new value.</param>
    private async Task OnValueChanged(double newValue)
    {
        if (Value != newValue)
        {
            Value = newValue;
            if (ValueChanged.HasDelegate)
            {
                await ValueChanged.InvokeAsync(newValue);
            }
        }
    }
}
