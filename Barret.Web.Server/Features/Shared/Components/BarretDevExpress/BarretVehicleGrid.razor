@using DevExpress.Blazor
@using DevExpress.Blazor.Grid
@using Barret.Web.Server.Features.Vehicles.Data
@typeparam TItem

<DxGrid Data="@Data"
        CssClass="@($"barret-data-grid vehicle-list-grid {CssClass}")"
        ShowFilterRow="@ShowFilterRow"
        ShowPager="@ShowPager"
        PageSize="@PageSize"
        KeyFieldName="@KeyFieldName"
        RowClick="@(args => RowClick?.Invoke(args))"
        CustomizeElement="@(args => CustomizeElement?.Invoke(args))">
    <Columns>
        @ColumnsContent
    </Columns>
</DxGrid>

@code {
    /// <summary>
    /// Gets or sets the data for the grid.
    /// </summary>
    [Parameter]
    public IEnumerable<TItem> Data { get; set; } = Enumerable.Empty<TItem>();

    /// <summary>
    /// Gets or sets the CSS class for the grid.
    /// </summary>
    [Parameter]
    public string CssClass { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets whether to show the filter row.
    /// </summary>
    [Parameter]
    public bool ShowFilterRow { get; set; } = false;

    /// <summary>
    /// Gets or sets whether to show the pager.
    /// </summary>
    [Parameter]
    public bool ShowPager { get; set; } = true;

    /// <summary>
    /// Gets or sets the page size.
    /// </summary>
    [Parameter]
    public int PageSize { get; set; } = 10;

    /// <summary>
    /// Gets or sets the columns content.
    /// </summary>
    [Parameter]
    public RenderFragment ColumnsContent { get; set; } = null!;

    /// <summary>
    /// Gets or sets the key field name.
    /// </summary>
    [Parameter]
    public string? KeyFieldName { get; set; }

    /// <summary>
    /// Gets or sets the row click event handler.
    /// </summary>
    [Parameter]
    public Action<GridRowClickEventArgs>? RowClick { get; set; }

    /// <summary>
    /// Gets or sets the customize element event handler.
    /// </summary>
    [Parameter]
    public Action<GridCustomizeElementEventArgs>? CustomizeElement { get; set; }
}
