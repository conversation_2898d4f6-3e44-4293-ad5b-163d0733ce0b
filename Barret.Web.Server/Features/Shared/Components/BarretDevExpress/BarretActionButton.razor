@using DevExpress.Blazor
@using Microsoft.AspNetCore.Components.Web

<button class="@GetButtonClass()"
        @onclick="OnClickHandler"
        disabled="@(!Enabled)"
        title="@ToolTip">
    @if (!string.IsNullOrEmpty(IconCssClass) && IconPosition == ButtonIconPosition.BeforeText)
    {
        <i class="@IconCssClass me-2"></i>
    }
    @if (!string.IsNullOrEmpty(Text))
    {
        <span>@Text</span>
    }
    @if (!string.IsNullOrEmpty(IconCssClass) && IconPosition == ButtonIconPosition.AfterText)
    {
        <i class="@IconCssClass ms-2"></i>
    }
    @ChildContent
</button>

@code {
    /// <summary>
    /// Gets or sets the button text.
    /// </summary>
    [Parameter]
    public string? Text { get; set; }

    /// <summary>
    /// Gets or sets the icon CSS class.
    /// </summary>
    [Parameter]
    public string? IconCssClass { get; set; }

    /// <summary>
    /// Gets or sets the click event callback.
    /// </summary>
    [Parameter]
    public EventCallback<MouseEventArgs> OnClick { get; set; }

    /// <summary>
    /// Gets or sets the CSS class.
    /// </summary>
    [Parameter]
    public string CssClass { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the render style.
    /// </summary>
    [Parameter]
    public ButtonRenderStyle RenderStyle { get; set; } = ButtonRenderStyle.Primary;

    /// <summary>
    /// Gets or sets the render style mode.
    /// </summary>
    [Parameter]
    public ButtonRenderStyleMode RenderStyleMode { get; set; } = ButtonRenderStyleMode.Contained;

    /// <summary>
    /// Gets or sets whether the button is enabled.
    /// </summary>
    [Parameter]
    public bool Enabled { get; set; } = true;

    /// <summary>
    /// Gets or sets the tooltip.
    /// </summary>
    [Parameter]
    public string? ToolTip { get; set; }

    /// <summary>
    /// Gets or sets the icon position.
    /// </summary>
    [Parameter]
    public ButtonIconPosition IconPosition { get; set; } = ButtonIconPosition.BeforeText;

    /// <summary>
    /// Gets or sets the child content.
    /// </summary>
    [Parameter]
    public RenderFragment? ChildContent { get; set; }

    /// <summary>
    /// Gets the CSS class for the button.
    /// </summary>
    /// <returns>The CSS class.</returns>
    private string GetButtonClass()
    {
        string baseClass = "btn barret-action-button d-inline-flex align-items-center justify-content-center";
        string styleClass = RenderStyle switch
        {
            ButtonRenderStyle.Primary => "btn-primary",
            ButtonRenderStyle.Secondary => "btn-secondary",
            ButtonRenderStyle.Danger => "btn-danger",
            ButtonRenderStyle.Success => "btn-success",
            ButtonRenderStyle.Warning => "btn-warning",
            ButtonRenderStyle.Info => "btn-info",
            ButtonRenderStyle.Light => "btn-light",
            ButtonRenderStyle.Dark => "btn-dark",
            ButtonRenderStyle.Link => "btn-link",
            _ => "btn-primary"
        };

        string modeClass = "";

        // Check if we need to convert to outline style
        if (styleClass.StartsWith("btn-") && !styleClass.StartsWith("btn-outline-") && RenderStyleMode != ButtonRenderStyleMode.Contained)
        {
            styleClass = styleClass.Replace("btn-", "btn-outline-");
        }

        // Add ghost style for text mode
        if (RenderStyleMode == ButtonRenderStyleMode.Text)
        {
            modeClass = "btn-ghost";
        }

        return $"{baseClass} {styleClass} {modeClass} {CssClass}".Trim();
    }

    /// <summary>
    /// Handles the click event.
    /// </summary>
    /// <param name="args">The mouse event args.</param>
    private async Task OnClickHandler(MouseEventArgs args)
    {
        if (OnClick.HasDelegate)
        {
            await OnClick.InvokeAsync(args);
        }
    }
}


