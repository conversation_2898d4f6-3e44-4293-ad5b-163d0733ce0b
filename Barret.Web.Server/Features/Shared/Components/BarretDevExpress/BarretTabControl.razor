@using DevExpress.Blazor
@using DevExpress.Blazor.Internal

<div class="@CssClass" style="border-bottom: 1px solid var(--gray-200);">
    <DxTabs ActiveTabIndex="@ActiveTabIndex"
            ActiveTabIndexChanged="@ActiveTabIndexChanged"
            TabClick="@OnTabClickInternal"
            CssClass="barret-tabs"
            style="background-color: transparent !important; border: none !important; box-shadow: none !important;">
        @TabsContent
    </DxTabs>
</div>

@code {
    /// <summary>
    /// Gets or sets the active tab index.
    /// </summary>
    [Parameter]
    public int ActiveTabIndex { get; set; }

    /// <summary>
    /// Event callback for when the active tab index changes.
    /// </summary>
    [Parameter]
    public EventCallback<int> ActiveTabIndexChanged { get; set; }

    /// <summary>
    /// Gets or sets the tabs content.
    /// </summary>
    [Parameter]
    public RenderFragment TabsContent { get; set; } = null!;

    /// <summary>
    /// Gets or sets the CSS class for the component.
    /// </summary>
    [Parameter]
    public string CssClass { get; set; } = string.Empty;

    /// <summary>
    /// Event callback for when a tab is clicked.
    /// </summary>
    [Parameter]
    public EventCallback<int> OnTabClick { get; set; }

    /// <summary>
    /// Handles the tab click event.
    /// </summary>
    /// <param name="args">The tab click event args.</param>
    private async Task OnTabClickInternal(TabClickEventArgs args)
    {
        if (OnTabClick.HasDelegate)
        {
            await OnTabClick.InvokeAsync(args.TabIndex);
        }
    }
}
