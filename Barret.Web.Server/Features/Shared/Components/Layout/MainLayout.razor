@using DevExpress.Blazor
@using Microsoft.AspNetCore.Components.Web
@inherits LayoutComponentBase

<PageTitle>Barret - Vehicle Configurator</PageTitle>

<RadzenTheme Theme="material" @rendermode="RenderMode.InteractiveServer" />

<div class="min-h-screen flex flex-col bg-white">
    <!-- Header with user dropdown -->
    <header class="bg-white border-b border-gray-100">
        <div class="flex justify-end px-4 py-3">
            <Barret.Web.Server.Shared.LoginDisplay />
        </div>
    </header>

    <!-- Content -->
    <main class="flex-1 bg-white">
        @Body
    </main>
</div>

<RadzenComponents @rendermode="RenderMode.InteractiveServer" />

<DxToastProvider AnimationType="ToastAnimationType.Fade" HorizontalAlignment="HorizontalAlignment.Right"
    VerticalAlignment="VerticalEdge.Bottom" ThemeMode="ToastThemeMode.Saturated" DisplayTime="@TimeSpan.FromSeconds(5)"
    ShowCloseButton="true" MaxToastCount="5" />
