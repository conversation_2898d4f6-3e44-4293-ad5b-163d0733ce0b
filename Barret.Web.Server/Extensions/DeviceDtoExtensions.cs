using Barret.Core.Areas.Devices.Enums;
using Barret.Shared.DTOs.Devices;

namespace Barret.Web.Server.Extensions
{
    /// <summary>
    /// Extension methods for DeviceDto
    /// </summary>
    public static class DeviceDtoExtensions
    {
        /// <summary>
        /// Gets all connected devices for a device
        /// </summary>
        /// <param name="device">The device DTO</param>
        /// <param name="allDevices">All devices in the vehicle</param>
        /// <returns>A list of all connected devices</returns>
        public static List<DeviceDto> GetConnectedDevices(this DeviceDto device, List<DeviceDto> allDevices)
        {
            if (device?.Connections == null || allDevices == null)
            {
                return [];
            }

            return device.Connections
                .Select(c => allDevices.FirstOrDefault(d => d.Id == c.InterfaceDeviceId))
                .Where(d => d != null)
                .ToList();
        }

        /// <summary>
        /// Gets all connected devices with a specific role
        /// </summary>
        /// <param name="device">The device DTO</param>
        /// <param name="allDevices">All devices in the vehicle</param>
        /// <param name="role">The device role</param>
        /// <returns>A list of all connected devices with the specified role</returns>
        public static List<DeviceDto> GetConnectedDevicesByRole(this DeviceDto device, List<DeviceDto> allDevices, DeviceRole role)
        {
            return device.GetConnectedDevices(allDevices).Where(d => d.DeviceRole == role).ToList();
        }

        /// <summary>
        /// Gets a connected device by ID
        /// </summary>
        /// <param name="device">The device DTO</param>
        /// <param name="allDevices">All devices in the vehicle</param>
        /// <param name="connectedDeviceId">The connected device ID</param>
        /// <returns>The connected device DTO, or null if not found</returns>
        public static DeviceDto GetConnectedDeviceById(this DeviceDto device, List<DeviceDto> allDevices, Guid connectedDeviceId)
        {
            return device.GetConnectedDevices(allDevices).FirstOrDefault(d => d.Id == connectedDeviceId);
        }

        /// <summary>
        /// Checks if a device is connected to another device
        /// </summary>
        /// <param name="device">The device DTO</param>
        /// <param name="connectedDeviceId">The connected device ID</param>
        /// <returns>True if the device is connected to the specified device, false otherwise</returns>
        public static bool IsConnectedTo(this DeviceDto device, Guid connectedDeviceId)
        {
            return device?.Connections?.Any(c => c.InterfaceDeviceId == connectedDeviceId) == true;
        }

        /// <summary>
        /// Gets a formatted display name for a device
        /// </summary>
        /// <param name="device">The device DTO</param>
        /// <returns>A formatted display name</returns>
        public static string GetDisplayName(this DeviceDto device)
        {
            if (device == null)
            {
                return string.Empty;
            }

            string roleName = device.DeviceRole.GetDisplayName();
            return string.IsNullOrEmpty(device.Name) ? roleName : $"{device.Name} ({roleName})";
        }

        /// <summary>
        /// Gets a short display name for a device
        /// </summary>
        /// <param name="device">The device DTO</param>
        /// <returns>A short display name</returns>
        public static string GetShortDisplayName(this DeviceDto device)
        {
            if (device == null)
            {
                return string.Empty;
            }

            return string.IsNullOrEmpty(device.Name) ? device.DeviceRole.GetDisplayName() : device.Name;
        }
    }
}
