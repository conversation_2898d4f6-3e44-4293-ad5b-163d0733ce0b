/*
 * Barret DevExpress Component Styles
 * This file contains styles for Barret-wrapped DevExpress components
 * Based on the Minimalist Monochromatic Design Guide
 */

/* BarretActionButton */
.barret-action-button {
    border-radius: var(--button-border-radius);
    padding: var(--button-md-padding);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-sm);
    letter-spacing: var(--letter-spacing-wide);
    transition: all var(--transition-normal);
    min-width: auto;
    height: var(--button-md-height);
    line-height: var(--line-height-none);
}

.barret-action-button:hover {
    transform: translateY(-1px);
}

.barret-action-button:active {
    transform: translateY(1px);
}

.barret-action-button.btn-sm {
    padding: var(--button-sm-padding);
    height: var(--button-sm-height);
    font-size: var(--font-size-xs);
}

.barret-action-button.btn-lg {
    padding: var(--button-lg-padding);
    height: var(--button-lg-height);
    font-size: var(--font-size-base);
}

/* Fix for DevExpress button icons */
.barret-action-button :deep(.dxbl-btn-text) {
    display: flex;
    align-items: center;
    justify-content: center;
}

.barret-action-button :deep(.dxbl-btn-icon) {
    display: inline-flex;
    margin-right: var(--space-xs);
}

.barret-action-button :deep(.dxbl-btn-icon-right) {
    margin-right: 0;
    margin-left: var(--space-xs);
}

/* BarretTabControl - Minimalist with single bottom line for active */
.barret-tabs {
    border-bottom: 1px solid var(--gray-200);
}

/* Override all DevExpress tab styling */
.barret-tabs :deep(.dxbl-tabs-header),
.barret-tabs :deep(.dxbl-tabs),
.barret-tabs :deep(.dxbl-tab-item),
.barret-tabs :deep(.dxbl-tab-item-content),
.barret-tabs :deep(.dxbl-tab-text) {
    background-color: transparent !important;
    background-image: none !important;
    box-shadow: none !important;
}

.barret-tabs :deep(.dxbl-tabs-header) {
    border-bottom: none;
    background-color: transparent !important;
    padding: 0 !important;
}

.barret-tabs :deep(.dxbl-tab-item) {
    padding: var(--space-md) var(--space-lg) !important;
    font-weight: var(--font-weight-medium) !important;
    color: var(--gray-600) !important;
    border: none !important;
    border-bottom: 1px solid transparent !important;
    border-radius: 0 !important;
    margin-right: var(--space-md) !important;
    transition: all var(--transition-normal) !important;
    background-color: transparent !important;
    box-shadow: none !important;
}

.barret-tabs :deep(.dxbl-tab-item.dxbl-active) {
    color: var(--gray-900) !important;
    border-bottom: 2px solid var(--gray-900) !important;
    background-color: transparent !important;
    font-weight: var(--font-weight-semibold) !important;
    box-shadow: none !important;
}

.barret-tabs :deep(.dxbl-tab-item:hover:not(.dxbl-active)) {
    color: var(--gray-900) !important;
    border-bottom-color: var(--gray-300) !important;
    background-color: transparent !important;
    box-shadow: none !important;
}

/* Target the DxTab component specifically */
.barret-tabs :deep(.dxbl-tab-text) {
    color: inherit !important;
    font-weight: inherit !important;
    padding: 0 !important;
}

/* Override any DevExpress default tab styling */
.barret-tabs :deep(.dxbl-tabs) {
    background-color: transparent !important;
    border: none !important;
}

.barret-tabs :deep(.dxbl-tab-item-content) {
    background-color: transparent !important;
    padding: 0 !important;
}

/* BarretDataGrid - Minimalist with thin borders */
.barret-data-grid {
    border: none;
    border-radius: var(--border-radius-sm);
    overflow: hidden;
}

.barret-data-grid :deep(.dxbl-grid-header) {
    background-color: #ffffff;
    font-weight: var(--font-weight-semibold);
    color: var(--gray-800);
    border-bottom: var(--border-width-thin) solid var(--gray-300);
}

.barret-data-grid :deep(.dxbl-grid-header-content) {
    padding: var(--space-md) var(--space-lg);
}

.barret-data-grid :deep(.dxbl-grid-data-row) {
    border-bottom: var(--border-width-thin) solid var(--gray-200);
    background-color: #ffffff;
}

.barret-data-grid :deep(.dxbl-grid-data-row:hover) {
    background-color: var(--gray-50);
}

.barret-data-grid :deep(.dxbl-grid-cell-data) {
    padding: var(--space-md) var(--space-lg);
    color: var(--gray-800);
}

.barret-data-grid :deep(.dxbl-btn) {
    border-radius: var(--button-border-radius);
}

.barret-data-grid :deep(.dxbl-grid-empty-data) {
    padding: var(--space-xl);
    color: var(--gray-500);
    font-style: italic;
    background-color: #ffffff;
}

/* BarretTextBox */
.barret-textbox {
    border-radius: var(--input-border-radius);
}

.barret-textbox :deep(.dxbl-textbox) {
    border-color: var(--gray-300);
    height: var(--input-height);
}

.barret-textbox :deep(.dxbl-textbox:focus) {
    border-color: var(--accent-500);
    box-shadow: var(--input-focus-shadow);
}

.barret-textbox :deep(.dxbl-textbox-input) {
    padding: var(--input-padding);
    font-size: var(--font-size-base);
    color: var(--gray-900);
}

.barret-textbox :deep(.dxbl-textbox-input::placeholder) {
    color: var(--gray-500);
}

.barret-textbox :deep(.dxbl-textbox:disabled) {
    background-color: var(--gray-100);
    border-color: var(--gray-200);
    color: var(--gray-400);
}

/* BarretCheckBox */
.barret-checkbox {
    margin-bottom: var(--space-xs);
}

.barret-checkbox :deep(.dxbl-checkbox-icon) {
    border-radius: var(--border-radius-sm);
    border-color: var(--gray-300);
    width: 18px;
    height: 18px;
}

.barret-checkbox :deep(.dxbl-checkbox-icon.dxbl-checkbox-checked) {
    background-color: var(--accent-500);
    border-color: var(--accent-500);
}

.barret-checkbox :deep(.dxbl-checkbox-text) {
    margin-left: var(--space-xs);
    color: var(--gray-900);
    font-size: var(--font-size-base);
}

.barret-checkbox :deep(.dxbl-checkbox:disabled) .dxbl-checkbox-icon {
    background-color: var(--gray-100);
    border-color: var(--gray-200);
}

.barret-checkbox :deep(.dxbl-checkbox:disabled) .dxbl-checkbox-text {
    color: var(--gray-400);
}

/* BarretNumberBox */
.barret-number-box {
    border-radius: var(--input-border-radius);
}

.barret-number-box :deep(.dxbl-spin-edit) {
    border-color: var(--gray-300);
    height: var(--input-height);
}

.barret-number-box :deep(.dxbl-spin-edit:focus) {
    border-color: var(--accent-500);
    box-shadow: var(--input-focus-shadow);
}

.barret-number-box :deep(.dxbl-spin-edit-input) {
    padding: var(--input-padding);
    font-size: var(--font-size-base);
    color: var(--gray-900);
}

.barret-number-box :deep(.dxbl-spin-edit:disabled) {
    background-color: var(--gray-100);
    border-color: var(--gray-200);
    color: var(--gray-400);
}

/* Device Import Dialog */
.device-import-grid {
    border-radius: var(--border-radius-sm);
    overflow: hidden;
    border: var(--border-width-thin) solid var(--gray-300);
}

.device-details {
    background-color: var(--gray-100);
    border-radius: var(--border-radius-sm);
    padding: var(--space-md);
}

/* Device Role Selector */
.device-role-grid {
    max-height: 400px;
    overflow-y: auto;
}

.device-role-card {
    display: flex;
    align-items: center;
    padding: var(--space-lg);
    border: var(--border-width-thin) solid var(--gray-200);
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    transition: all var(--transition-normal);
    margin-bottom: var(--space-xs);
    background-color: #ffffff;
}

.device-role-card:hover {
    border-color: var(--gray-400);
    box-shadow: var(--shadow-hover);
}

.device-role-icon {
    font-size: 1.5rem;
    margin-right: var(--space-md);
    color: var(--gray-800);
}

.device-role-name {
    font-weight: var(--font-weight-medium);
    color: var(--gray-900);
}

/* DevExpress Dialog */
.dxbl-popup-content {
    padding: var(--space-lg);
}

.dxbl-popup-header {
    padding: var(--space-md) var(--space-lg);
    border-bottom: var(--border-width-thin) solid var(--gray-200);
}

.dxbl-popup-footer {
    padding: var(--space-md) var(--space-lg);
    border-top: var(--border-width-thin) solid var(--gray-200);
}

/* Ghost button style for DevExpress buttons */
.dxbl-btn-ghost {
    background-color: transparent;
    border-color: transparent;
    color: var(--gray-600);
}

.dxbl-btn-ghost:hover {
    background-color: var(--gray-50);
    color: var(--gray-900);
}

.dxbl-btn-ghost:active {
    background-color: var(--gray-100);
    transform: translateY(1px);
}

/* Fix for DevExpress button sizing */
.dxbl-btn {
    min-width: auto;
    border-radius: var(--button-border-radius);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-sm);
    letter-spacing: var(--letter-spacing-wide);
    transition: all var(--transition-normal);
}

.dxbl-btn-sm {
    padding: var(--button-sm-padding);
    height: var(--button-sm-height);
    font-size: var(--font-size-xs);
}

.dxbl-btn-lg {
    padding: var(--button-lg-padding);
    height: var(--button-lg-height);
    font-size: var(--font-size-base);
}
