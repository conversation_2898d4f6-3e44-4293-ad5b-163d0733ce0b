/*
 * Home Page Styles (New)
 * This file contains styles specific to the new home page
 * Using Tail<PERSON>'s @apply directive for component-based styling
 */

/* Main container */
.home-container-new {
  @apply min-h-screen bg-white flex flex-col max-w-[1200px] mx-auto px-6 py-8 flex-1;
}

/* Header section */
.home-header-new {
  @apply mb-16 text-center;
}

.home-title-new {
  @apply text-3xl font-medium text-gray-900;
}

.home-subtitle-new {
  @apply text-gray-500 mt-2 text-lg;
}

/* Loading state */
.loading-container-new {
  @apply flex-1 flex flex-col items-center justify-center;
}

.loading-spinner-new {
  @apply h-12 w-12 border-4 border-gray-200 border-t-gray-800 rounded-full animate-spin;
}

.loading-text-new {
  @apply mt-4 text-gray-500;
}

/* Vehicle type card container */
.vehicle-types-container-new {
  @apply flex-1 flex flex-col justify-center max-w-5xl mx-auto w-full;
}

.vehicle-types-grid-new {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8;
}

/* Vehicle type card with shadow */
.vehicle-type-card-with-shadow {
  @apply bg-gray-50 rounded-lg p-8 flex flex-col items-center text-center;
  transition: box-shadow 0.3s ease, transform 0.3s ease;
}

.vehicle-type-card-with-shadow:hover {
  box-shadow:
    0 3px 6px -2px rgba(0,0,0,0.12),
    0 4px 8px -1px rgba(0,0,0,0.07),
    0 8px 16px -4px rgba(0,0,0,0.1);
  transform: translateY(-1px);
}
