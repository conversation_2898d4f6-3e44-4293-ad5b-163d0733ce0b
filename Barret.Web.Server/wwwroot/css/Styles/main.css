/*
 * Main CSS file for Barret Vehicle Configurator
 * This file imports all other CSS files in the correct order
 */

/* Tailwind directives */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import design tokens and variables first */
@import url('./_variables.css');

/* Import base styles */
@import url('./_base.css');

/* Import component styles */
@import url('./_components.css');

/* Import layout styles */
@import url('./_layout.css');

/* Import utility classes */
@import url('./_utilities.css');

/* Import DevExpress component styles */
@import url('./_devexpress.css');

/* Import Barret DevExpress component styles */
@import url('./_barret-devexpress.css');

/* Import DevExpress overrides - must be after other DevExpress styles */
@import url('./_devexpress-overrides.css');

/* Import custom tab styles */
@import url('./_custom-tabs.css');

/* Import feature-specific styles */
@import url('./home.css');
@import url('./home-new.css');
@import url('./vehicle-list.css');
@import url('./vehicle-list-tabs.css');
@import url('./vehicle-editor.css');
@import url('./vehicle-card.css');
@import url('./vehicle-type-card.css');
@import url('./vehicle-type-card-new.css');
@import url('./custom-shadows.css');

/* Import page-specific styles */
@import url('./device-editor.css');
@import url('./device-import.css');

/* Import dialog component styles */
@import url('./confirmation-dialog.css');
