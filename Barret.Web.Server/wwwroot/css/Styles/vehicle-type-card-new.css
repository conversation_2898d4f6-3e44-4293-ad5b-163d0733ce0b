/*
 * Vehicle Type Card Component Styles (New)
 * Using Tailwind's @apply directive to create reusable component classes
 */

.vehicle-type-card-new {
  @apply border-0 shadow-sm cursor-pointer bg-gray-50 rounded-xl overflow-hidden h-full;
  transition: box-shadow 0.3s ease, transform 0.3s ease;
}

.vehicle-type-card-new:hover {
  /* Custom shadow with more pronounced bottom shadow */
  box-shadow: 0 3px 5px -1px rgba(0,0,0,0.06),
              0 2px 4px -1px rgba(0,0,0,0.04),
              0 8px 10px -4px rgba(0,0,0,0.08);
  transform: translateY(-1px);
}

.vehicle-type-card-inner-new {
  @apply p-8 flex flex-col items-center text-center h-full;
}

.vehicle-type-icon-new {
  @apply h-16 w-16 bg-gray-100 rounded-full flex items-center justify-center mb-6;
}

.vehicle-type-icon-new svg {
  @apply h-8 w-8 text-gray-700;
}

.vehicle-type-title-new {
  @apply text-xl font-medium text-gray-900 mb-2;
}

.vehicle-type-description-new {
  @apply text-gray-500 mb-6;
}

.vehicle-type-button-new {
  @apply mt-auto rounded-full bg-gray-900 text-white hover:bg-gray-800 transition-colors px-4 py-2 text-sm font-medium;
}
