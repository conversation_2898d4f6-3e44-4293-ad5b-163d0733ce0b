/*
 * Component Styles
 * This file contains styles for UI components
 * Based on the Minimalist Monochromatic Design Guide
 */

/* Cards - Minimalist with very thin borders */
.card {
  background-color: #ffffff;
  border-radius: var(--card-border-radius);
  border: var(--border-width-thin) solid var(--gray-200);
  box-shadow: none;
  transition: all var(--transition-normal);
  overflow: hidden;
  height: 100%;
  width: 100%;
  transform: translateY(0);
}

.card:hover {
  box-shadow: var(--shadow-hover);
  border-color: var(--gray-200);
  transform: translateY(0);
}

.card-header {
  padding: var(--card-header-padding);
  border-bottom: var(--border-width-thin) solid var(--gray-200);
  background-color: transparent;
}

.card-body {
  padding: var(--card-padding);
  width: 100%;
}

.card-footer {
  padding: var(--card-footer-padding);
  border-top: var(--border-width-thin) solid var(--gray-200);
  background-color: transparent;
}

/* Compact Card */
.card-compact {
  border-radius: var(--card-compact-border-radius);
}

.card-compact .card-header {
  padding: var(--space-md) var(--space-md) var(--space-sm);
}

.card-compact .card-body {
  padding: var(--card-compact-padding);
}

.card-compact .card-footer {
  padding: var(--space-sm) var(--space-md) var(--space-md);
}

/* Card utilities */
.card-equal-height {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
}

.card-equal-height .card-body {
  flex: 1 0 auto;
  display: flex;
  flex-direction: column;
}

.card-equal-height .card-footer {
  margin-top: auto;
}

/* Buttons */
.btn {
  border-radius: var(--button-border-radius);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
  letter-spacing: var(--letter-spacing-wide);
  border: var(--border-width-thin) solid transparent;
  transition: all var(--transition-normal);
  line-height: var(--line-height-none);
  min-width: auto;
}

/* Button Sizes */
.btn-sm {
  padding: var(--button-sm-padding);
  height: var(--button-sm-height);
  font-size: var(--font-size-xs);
}

.btn-md, .btn {
  padding: var(--button-md-padding);
  height: var(--button-md-height);
}

.btn-lg {
  padding: var(--button-lg-padding);
  height: var(--button-lg-height);
  font-size: var(--font-size-base);
}

/* Button Variants */
.btn-primary {
  background: var(--gray-900);
  color: #ffffff;
}

.btn-primary:hover {
  background-color: var(--gray-800);
  border-color: var(--gray-800);
  box-shadow: var(--shadow-hover);
}

.btn-primary:active {
  background-color: var(--gray-900);
  border-color: var(--gray-900);
  transform: translateY(1px);
  box-shadow: none;
}

.btn-primary:focus {
  box-shadow: 0 0 0 3px rgba(23, 23, 23, 0.3);
}

.btn-secondary {
  background: transparent;
  border-color: var(--gray-300);
  color: var(--gray-900);
}

.btn-secondary:hover {
  background-color: var(--gray-50);
  border-color: var(--gray-400);
  box-shadow: var(--shadow-hover);
}

.btn-secondary:active {
  background-color: var(--gray-100);
  border-color: var(--gray-500);
  transform: translateY(1px);
  box-shadow: none;
}

/* Text Button */
.btn-text {
  background-color: transparent;
  border-color: transparent;
  color: var(--gray-800);
  padding: var(--space-xs) var(--space-sm);
}

.btn-text:hover {
  color: var(--gray-900);
  text-decoration: underline;
}

/* Ghost Buttons */
.btn-ghost {
  background-color: transparent;
  border-color: transparent;
  color: var(--gray-600);
}

.btn-ghost:hover {
  background-color: var(--gray-50);
  color: var(--gray-900);
}

.btn-ghost:active {
  background-color: var(--gray-100);
  transform: translateY(1px);
}

/* Icon Button */
.btn-icon {
  width: 40px;
  height: 40px;
  padding: 0;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--button-border-radius);
}

.btn-icon.btn-sm {
  width: 32px;
  height: 32px;
}

.btn-icon.btn-lg {
  width: 48px;
  height: 48px;
}

/* Forms */
.form-control, .form-select {
  border-radius: var(--input-border-radius);
  border: var(--border-width-thin) solid var(--gray-300);
  padding: var(--input-padding);
  transition: border-color var(--transition-normal), box-shadow var(--transition-normal);
  height: var(--input-height);
  font-size: var(--font-size-base);
  color: var(--gray-900);
  background-color: #ffffff;
}

.form-control::placeholder, .form-select::placeholder {
  color: var(--gray-500);
}

.form-control:focus, .form-select:focus {
  border-color: var(--accent-500);
  box-shadow: var(--input-focus-shadow);
  outline: none;
}

.form-control:disabled, .form-select:disabled {
  background-color: var(--gray-100);
  border-color: var(--gray-200);
  color: var(--gray-400);
  cursor: not-allowed;
}

.form-control.is-invalid, .form-select.is-invalid {
  border-color: var(--error);
  box-shadow: 0 0 0 3px rgba(248, 113, 113, 0.2);
}

.form-label {
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--space-xs);
  font-size: var(--font-size-sm);
  color: var(--gray-700);
}

.form-text {
  color: var(--gray-500);
  font-size: var(--font-size-sm);
  margin-top: var(--space-2xs);
}

.invalid-feedback {
  color: var(--error);
  font-size: var(--font-size-sm);
  margin-top: var(--space-2xs);
}

/* Form Group */
.form-group {
  margin-bottom: var(--space-md);
}

/* Search inputs */
input[type="search"] {
  padding-left: var(--space-xl);
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%23737373' class='bi bi-search' viewBox='0 0 16 16'%3E%3Cpath d='M11.742 10.344a6.5 6.5 0 1 0-1.397 1.398h-.001c.03.04.062.078.098.115l3.85 3.85a1 1 0 0 0 1.415-1.414l-3.85-3.85a1.007 1.007 0 0 0-.115-.1zM12 6.5a5.5 5.5 0 1 1-11 0 5.5 5.5 0 0 1 11 0z'%3E%3C/path%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: var(--space-md) center;
  background-size: 16px;
}

/* Search container */
.search-container .input-group-text {
  background-color: #ffffff;
  border-right: none;
  border-color: var(--gray-300);
  color: var(--gray-500);
}

.search-container .form-control {
  border-left: none;
}

/* Alerts */
.alert {
  border-radius: var(--border-radius-sm);
  padding: var(--space-md) var(--space-lg);
  margin-bottom: var(--space-md);
  border: var(--border-width-thin) solid transparent;
  display: flex;
  align-items: flex-start;
}

.alert-icon {
  margin-right: var(--space-sm);
  font-size: 1.25rem;
  line-height: 1;
}

.alert-content {
  flex: 1;
}

.alert-title {
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--space-2xs);
}

.alert-primary {
  background-color: var(--accent-100);
  border-color: var(--accent-300);
  color: var(--accent-700);
}

.alert-secondary {
  background-color: var(--gray-100);
  border-color: var(--gray-300);
  color: var(--gray-700);
}

.alert-success {
  background-color: rgba(74, 222, 128, 0.1);
  border-color: rgba(74, 222, 128, 0.2);
  color: var(--success);
}

.alert-danger {
  background-color: rgba(248, 113, 113, 0.1);
  border-color: rgba(248, 113, 113, 0.2);
  color: var(--error);
}

.alert-warning {
  background-color: rgba(251, 191, 36, 0.1);
  border-color: rgba(251, 191, 36, 0.2);
  color: var(--warning);
}

.alert-info {
  background-color: rgba(96, 165, 250, 0.1);
  border-color: rgba(96, 165, 250, 0.2);
  color: var(--info);
}

/* Badges */
.badge {
  border-radius: var(--border-radius-pill);
  padding: var(--space-2xs) var(--space-xs);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-xs);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
}

.badge-primary {
  background-color: var(--accent-500);
  color: #ffffff;
}

.badge-secondary {
  background-color: var(--gray-200);
  color: var(--gray-700);
}

.badge-success {
  background-color: var(--success);
  color: #ffffff;
}

.badge-danger {
  background-color: var(--error);
  color: #ffffff;
}

.badge-warning {
  background-color: var(--warning);
  color: var(--gray-900);
}

.badge-info {
  background-color: var(--info);
  color: #ffffff;
}

.badge-outline {
  background-color: transparent;
  border: 1px solid currentColor;
}

.badge-outline.badge-primary {
  color: var(--accent-500);
}

.badge-outline.badge-secondary {
  color: var(--gray-500);
}

/* Empty state */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-2xl);
  text-align: center;
  background-color: #ffffff;
  border: var(--border-width-thin) dashed var(--gray-300);
  border-radius: var(--border-radius-md);
}

.empty-state-icon {
  font-size: 3rem;
  color: var(--gray-400);
  margin-bottom: var(--space-lg);
}

.empty-state-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--space-xs);
  color: var(--gray-900);
}

.empty-state-description {
  color: var(--gray-500);
  margin-bottom: var(--space-lg);
  max-width: 400px;
}

/* Vehicle type card */
.vehicle-type-card {
  cursor: pointer;
  transition: all var(--transition-normal);
  background-color: #ffffff;
  border: var(--border-width-thin) solid var(--gray-200);
  box-shadow: none;
  transform: translateY(0);
}

.vehicle-type-card:hover {
  border-color: var(--gray-200);
  box-shadow: var(--shadow-hover);
  transform: translateY(0);
}

.vehicle-type-card .card-footer {
  padding-top: 0;
  background-color: transparent;
  border-top: none;
}

/* Icon containers - Minimalist with subtle backgrounds */
.icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: var(--gray-50);
  color: var(--gray-800);
  font-size: 2rem;
  border: var(--border-width-thin) solid var(--gray-200);
}

/* Icon sizes */
.icon-xs { font-size: 1rem; }
.icon-sm { font-size: 1.25rem; }
.icon-md { font-size: 1.5rem; }
.icon-lg { font-size: 2rem; }
.icon-xl { font-size: 2.5rem; }

/* Smaller icon container variant */
.icon-container.icon-container-sm {
  width: 40px;
  height: 40px;
  font-size: 1.25rem;
  background-color: var(--gray-800);
  color: white;
  border: none;
}

.icon-container.icon-container-xs {
  width: 32px;
  height: 32px;
  font-size: 1rem;
}

.icon-container.icon-container-lg {
  width: 96px;
  height: 96px;
  font-size: 2.5rem;
}

/* Device icon styling */
.device-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: var(--border-radius-sm);
  background-color: var(--gray-50);
  color: var(--gray-800);
  border: var(--border-width-thin) solid var(--gray-200);
}

/* Vehicle icon styling */
.vehicle-icon-sm {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: var(--gray-50);
  color: var(--gray-800);
  border: var(--border-width-thin) solid var(--gray-200);
}

/* Tab navigation - Minimalist with single bottom line for active */
.nav-tabs {
  border-bottom: 1px solid var(--gray-200);
  background-color: transparent !important;
}

.nav-tabs .nav-link {
  color: var(--gray-600);
  border: none !important;
  border-bottom: 1px solid transparent !important;
  padding: var(--space-md) var(--space-lg);
  margin-right: var(--space-md);
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-normal);
  background-color: transparent !important;
  box-shadow: none !important;
  border-radius: 0 !important;
}

.nav-tabs .nav-link:hover {
  color: var(--gray-900);
  border-bottom-color: var(--gray-300) !important;
  background-color: transparent !important;
}

.nav-tabs .nav-link.active {
  color: var(--gray-900);
  border-bottom: 2px solid var(--gray-900) !important;
  font-weight: var(--font-weight-semibold);
  background-color: transparent !important;
  box-shadow: none !important;
}

/* Vertical Navigation - Minimalist with subtle indicators */
.nav-vertical {
  width: var(--nav-vertical-width);
}

.nav-vertical .nav-link {
  display: flex;
  align-items: center;
  padding: var(--nav-vertical-item-padding);
  margin-bottom: var(--nav-vertical-item-gap);
  border-radius: var(--border-radius-sm);
  color: var(--gray-700);
  transition: all var(--transition-normal);
  border-left: 2px solid transparent;
}

.nav-vertical .nav-link:hover {
  color: var(--gray-900);
  background-color: transparent;
  border-left-color: var(--gray-300);
}

.nav-vertical .nav-link.active {
  background-color: transparent;
  color: var(--gray-900);
  border-left-color: var(--gray-900);
  font-weight: var(--font-weight-medium);
}

.nav-vertical .nav-icon {
  margin-right: var(--space-sm);
  color: var(--gray-500);
}

.nav-vertical .nav-link.active .nav-icon {
  color: var(--gray-900);
}

/* Tables - Minimalist with thin borders */
.table {
  border-collapse: separate;
  border-spacing: 0;
  width: 100%;
}

.table th {
  text-align: left;
  padding: var(--space-md);
  border-bottom: var(--border-width-thin) solid var(--gray-300);
  font-weight: var(--font-weight-semibold);
  color: var(--gray-800);
  background-color: #ffffff;
}

.table td {
  padding: var(--space-md);
  border-bottom: var(--border-width-thin) solid var(--gray-200);
  color: var(--gray-800);
}

.table tr:hover {
  background-color: var(--gray-50);
}

.table-striped tbody tr:nth-of-type(odd) {
  background-color: var(--gray-50);
}

.table-bordered {
  border: var(--border-width-thin) solid var(--gray-200);
  border-radius: var(--border-radius-sm);
  overflow: hidden;
}

.table-bordered th,
.table-bordered td {
  border: var(--border-width-thin) solid var(--gray-200);
}

/* Focus states */
:focus-visible {
  outline: var(--border-width-medium) solid var(--gray-800);
  outline-offset: 2px;
}
