/*
 * Home Page Styles
 * This file contains styles specific to the home page
 * Using Tailwind's @apply directive for component-based styling
 */

/* Main container */
.home-container {
  @apply min-h-screen bg-white flex flex-col max-w-[1200px] mx-auto px-6 py-8 flex-1;
}

/* Header section */
.home-header {
  @apply mb-8 md:mb-16;
}

.home-title {
  @apply text-3xl font-medium text-gray-900;
}

.home-subtitle {
  @apply text-gray-500 mt-2 text-lg;
}

/* Loading state */
.loading-container {
  @apply flex-1 flex flex-col items-center justify-center;
}

.loading-spinner {
  @apply h-12 w-12 border-4 border-gray-200 border-t-gray-800 rounded-full animate-spin;
}

.loading-text {
  @apply mt-4 text-gray-500;
}

/* Vehicle types grid */
.vehicle-types-container {
  @apply flex-1 flex flex-col justify-center max-w-4xl mx-auto w-full;
}

.vehicle-types-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6;
}
