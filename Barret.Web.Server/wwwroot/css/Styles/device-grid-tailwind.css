/*
 * Device Grid Tailwind CSS Overrides
 * This file contains Tailwind CSS overrides for DevExpress grids
 */

/* Ensure Tailwind classes have higher specificity for DevExpress grids */
.barret-data-grid :deep(.dxbl-grid-header) {
  @apply bg-white font-medium text-gray-500 border-b border-gray-100 !important;
}

.barret-data-grid :deep(.dxbl-grid-header-content) {
  @apply py-3 px-4 !important;
}

.barret-data-grid :deep(.dxbl-grid-data-row) {
  @apply border-b border-gray-50 hover:bg-gray-50 !important;
}

.barret-data-grid :deep(.dxbl-grid-cell-data) {
  @apply py-3 px-4 text-gray-600 !important;
}

.barret-data-grid :deep(.dxbl-grid-empty-data) {
  @apply p-8 text-gray-500 italic bg-white !important;
}

/* Status column styling */
.barret-data-grid :deep(.status-ok) {
  @apply text-green-500 !important;
}

.barret-data-grid :deep(.status-warning) {
  @apply text-red-500 !important;
}

/* Action buttons styling */
.barret-data-grid :deep(.action-button) {
  @apply text-gray-500 hover:text-gray-900 hover:bg-gray-100 rounded-full h-8 w-8 p-0 flex items-center justify-center !important;
}

/* Filter row styling */
.barret-data-grid :deep(.dxbl-grid-filter-row) {
  @apply bg-gray-50 !important;
}

.barret-data-grid :deep(.dxbl-grid-filter-row-cell) {
  @apply py-2 px-4 !important;
}

/* Pager styling */
.barret-data-grid :deep(.dxbl-grid-pager) {
  @apply bg-white border-t border-gray-100 py-2 px-4 !important;
}

.barret-data-grid :deep(.dxbl-btn) {
  @apply rounded-md !important;
}

/* Responsive styling */
@media (max-width: 768px) {
  .barret-data-grid :deep(.dxbl-grid-header-content),
  .barret-data-grid :deep(.dxbl-grid-cell-data) {
    @apply py-2 px-3 !important;
  }
}
