/*
 * Vehicle Type Card Component Styles
 * Using Tailwind's @apply directive to create reusable component classes
 */

.vehicle-type-card {
  @apply border-0 shadow-sm hover:shadow-md transition-shadow duration-300 cursor-pointer bg-gray-50 rounded-xl overflow-hidden h-full;
}

.vehicle-type-card-inner {
  @apply p-8 flex flex-col items-center text-center h-full;
}

.vehicle-type-icon {
  @apply h-16 w-16 bg-gray-100 rounded-full flex items-center justify-center mb-6;
}

.vehicle-type-icon i {
  @apply text-gray-700 text-2xl;
}

.vehicle-type-title {
  @apply text-xl font-medium text-gray-900 mb-2;
}

.vehicle-type-description {
  @apply text-gray-500 mb-6;
}

.vehicle-type-button {
  @apply mt-auto rounded-full bg-gray-900 text-white hover:bg-gray-800 transition-colors px-4 py-2;
}
