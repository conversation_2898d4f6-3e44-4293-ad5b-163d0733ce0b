@using Barret.Core.Areas.Devices.Enums
@using Barret.Shared.DTOs.Devices
@using Barret.Shared.DTOs.Devices.Alarms

<div class="alarms-section">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h5 class="mb-0">Device Alarms</h5>
        <button class="btn btn-sm btn-primary" @onclick="AddAlarm">
            <i class="bi bi-plus-lg"></i> Add Alarm
        </button>
    </div>

    @if (Device.Alarms?.Any() == true)
    {
        <div class="accordion" id="alarmsAccordion">
            @foreach (var alarm in Device.Alarms.Select((alarm, index) => new { alarm, index }))
            {
                <div class="accordion-item">
                    <h2 class="accordion-header" id="heading@(alarm.index)">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                                data-bs-target="#collapse@(alarm.index)" aria-expanded="false"
                                aria-controls="collapse@(alarm.index)">
                            <div class="d-flex justify-content-between align-items-center w-100 me-3">
                                <span class="fw-bold">@(string.IsNullOrEmpty(alarm.alarm.Description) ? "New Alarm" : alarm.alarm.Description)</span>
                                <span class="badge @(alarm.alarm.NotificationType == NotificationType.Alarm ? "bg-danger" : "bg-warning")">
                                    @alarm.alarm.NotificationType
                                </span>
                            </div>
                        </button>
                    </h2>
                    <div id="collapse@(alarm.index)" class="accordion-collapse collapse"
                         aria-labelledby="heading@(alarm.index)" data-bs-parent="#alarmsAccordion">
                        <div class="accordion-body">
                            <div class="row g-3">
                                <!-- Basic Properties -->
                                <div class="col-md-6">
                                    <label class="form-label">Description</label>
                                    <input type="text" class="form-control" @bind="alarm.alarm.Description"
                                           @oninput="e => UpdateAlarmProperty(alarm.alarm, nameof(alarm.alarm.Description), e.Value?.ToString())" />
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">Notification Type</label>
                                    <select class="form-select" @bind="alarm.alarm.NotificationType"
                                            @bind:after="() => UpdateAlarmProperty(alarm.alarm, nameof(alarm.alarm.NotificationType), alarm.alarm.NotificationType)">
                                        <option value="@NotificationType.Undefined">Select Type</option>
                                        <option value="@NotificationType.Alarm">Alarm</option>
                                        <option value="@NotificationType.Warning">Warning</option>
                                    </select>
                                </div>

                                <!-- Enhanced Properties -->
                                <div class="col-md-12">
                                    <label class="form-label">Message</label>
                                    <input type="text" class="form-control" @bind="alarm.alarm.Message"
                                           @oninput="e => UpdateAlarmProperty(alarm.alarm, nameof(alarm.alarm.Message), e.Value?.ToString())"
                                           placeholder="Alarm message for Michelangelo integration" />
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">Notification Group</label>
                                    <select class="form-select" @bind="alarm.alarm.NotificationGroupId"
                                            @bind:after="() => UpdateAlarmProperty(alarm.alarm, nameof(alarm.alarm.NotificationGroupId), alarm.alarm.NotificationGroupId)">
                                        <option value="@NotificationGroupId.Undefined">Select Group</option>
                                        <option value="@NotificationGroupId.ControlSystemGeneral">Control System General</option>
                                        <option value="@NotificationGroupId.ControlSystemPLC">Control System PLC</option>
                                        <option value="@NotificationGroupId.PowerGeneral">Power General</option>
                                        <option value="@NotificationGroupId.SafetySystems">Safety Systems</option>
                                        <option value="@NotificationGroupId.NavigationEquipment">Navigation Equipment</option>
                                        <option value="@NotificationGroupId.SensorProcessor">Sensor Processor</option>
                                        <option value="@NotificationGroupId.AutonomyProcessor">Autonomy Processor</option>
                                        <option value="@NotificationGroupId.Communication">Communication</option>
                                        <option value="@NotificationGroupId.Propulsion">Propulsion</option>
                                        <option value="@NotificationGroupId.PowerSystems">Power Systems</option>
                                        <option value="@NotificationGroupId.MeasurementEquipment">Measurement Equipment</option>
                                        <option value="@NotificationGroupId.ShoreControlStation">Shore Control Station</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">Entity ID</label>
                                    <input type="text" class="form-control" @bind="alarm.alarm.EntityId"
                                           @oninput="e => UpdateAlarmProperty(alarm.alarm, nameof(alarm.alarm.EntityId), e.Value?.ToString())"
                                           placeholder="Associated entity identifier" />
                                </div>
                                <div class="col-md-12">
                                    <label class="form-label">Warning ID (Domain Driver ID)</label>
                                    <select class="form-select" @bind="alarm.alarm.WarningId"
                                            @bind:after="() => UpdateAlarmProperty(alarm.alarm, nameof(alarm.alarm.WarningId), alarm.alarm.WarningId)">
                                        <option value="@WarningId.Undefined">Select Warning ID</option>
                                        <!-- Bridge and Control -->
                                        <optgroup label="Bridge and Control">
                                            <option value="@WarningId.BridgeWarningIndicator">Bridge Warning Indicator</option>
                                            <option value="@WarningId.BridgeAlarmIndicator">Bridge Alarm Indicator</option>
                                            <option value="@WarningId.AlarmGeneral">Alarm General</option>
                                            <option value="@WarningId.AlarmFire">Alarm Fire</option>
                                        </optgroup>
                                        <!-- Propulsion -->
                                        <optgroup label="Propulsion">
                                            <option value="@WarningId.SternEngine1">Stern Engine 1</option>
                                            <option value="@WarningId.SternEngine2">Stern Engine 2</option>
                                            <option value="@WarningId.SternThruster1">Stern Thruster 1</option>
                                            <option value="@WarningId.SternThruster2">Stern Thruster 2</option>
                                            <option value="@WarningId.Bow360Thruster1">Bow 360 Thruster 1</option>
                                            <option value="@WarningId.Bow360Thruster2">Bow 360 Thruster 2</option>
                                        </optgroup>
                                        <!-- Michelangelo System -->
                                        <optgroup label="Michelangelo System">
                                            <option value="@WarningId.MiBatteryLowVoltage">Battery Low Voltage</option>
                                            <option value="@WarningId.MiBatteryHighVoltage">Battery High Voltage</option>
                                            <option value="@WarningId.MiPropulsionNotReady">Propulsion Not Ready</option>
                                            <option value="@WarningId.MiOilPressureOutOfBounds">Oil Pressure Out of Bounds</option>
                                            <option value="@WarningId.MiTemperatureExceededThresholds">Temperature Exceeded Thresholds</option>
                                            <option value="@WarningId.MiPowerGeneral">Power General</option>
                                            <option value="@WarningId.MiCommunicationGeneral">Communication General</option>
                                        </optgroup>
                                    </select>
                                </div>
                            </div>
                            <div class="mt-3 d-flex justify-content-end">
                                <button class="btn btn-sm btn-outline-danger" @onclick="() => RemoveAlarm(alarm.alarm)">
                                    <i class="bi bi-trash"></i> Remove Alarm
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>
    }
    else
    {
        <div class="alert alert-info">
            <i class="bi bi-info-circle me-2"></i>
            No alarms configured for this device. Click "Add Alarm" to create one.
        </div>
    }
</div>

@code {
    [Parameter]
    public DeviceDto Device { get; set; }

    [Parameter]
    public EventCallback<DeviceDto> OnDeviceChanged { get; set; }

    private void AddAlarm()
    {
        if (Device.Alarms == null)
            Device.Alarms = new List<AlarmDto>();

        Device.Alarms.Add(new AlarmDto
        {
            Id = Guid.NewGuid(),
            Description = "New Alarm",
            NotificationType = NotificationType.Warning, // Default to Warning instead of Undefined
            Message = string.Empty,
            NotificationGroupId = NotificationGroupId.Undefined,
            EntityId = string.Empty,
            WarningId = WarningId.Undefined
        });

        OnDeviceChanged.InvokeAsync(Device);
    }

    private void RemoveAlarm(AlarmDto alarm)
    {
        Device.Alarms?.Remove(alarm);
        OnDeviceChanged.InvokeAsync(Device);
    }

    private async Task UpdateAlarmProperty(AlarmDto alarm, string propertyName, object? value)
    {
        if (alarm == null) return;

        switch (propertyName)
        {
            case nameof(AlarmDto.Description):
                alarm.Description = value?.ToString() ?? string.Empty;
                break;
            case nameof(AlarmDto.NotificationType):
                if (value is NotificationType notificationType)
                    alarm.NotificationType = notificationType;
                else if (Enum.TryParse<NotificationType>(value?.ToString(), out var parsedNotificationType))
                    alarm.NotificationType = parsedNotificationType;
                break;
            case nameof(AlarmDto.Message):
                alarm.Message = value?.ToString() ?? string.Empty;
                break;
            case nameof(AlarmDto.NotificationGroupId):
                if (value is NotificationGroupId notificationGroupId)
                    alarm.NotificationGroupId = notificationGroupId;
                else if (Enum.TryParse<NotificationGroupId>(value?.ToString(), out var parsedNotificationGroupId))
                    alarm.NotificationGroupId = parsedNotificationGroupId;
                break;
            case nameof(AlarmDto.EntityId):
                alarm.EntityId = value?.ToString() ?? string.Empty;
                break;
            case nameof(AlarmDto.WarningId):
                if (value is WarningId warningId)
                    alarm.WarningId = warningId;
                else if (Enum.TryParse<WarningId>(value?.ToString(), out var parsedWarningId))
                    alarm.WarningId = parsedWarningId;
                break;
        }

        await OnDeviceChanged.InvokeAsync(Device);
    }
}