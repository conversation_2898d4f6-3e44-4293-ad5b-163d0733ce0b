@namespace Barret.Web.Server.Shared.Components.DeviceManagers

@using Barret.Core.Areas.DeviceGroups
@using Barret.Shared.DTOs.Devices
@using Barret.Shared.DTOs.Vehicles.Vessels

<div>
    @if (Visible)
    {
        <ImportDevicesPopup
            Visible="Visible"
            VisibleChanged="VisibleChanged"
            DeviceCategory="DeviceCategory"
            TargetVessel="TargetVessel"
            OnVesselUpdated="OnVesselUpdated" />
    }
</div>

@code {
    [Parameter]
    public bool Visible { get; set; }

    [Parameter]
    public DeviceGroups DeviceCategory { get; set; } = DeviceGroups.CameraGroup;

    [Parameter]
    public EventCallback<bool> VisibleChanged { get; set; }

    [Parameter]
    public EventCallback<VesselDto> OnVesselUpdated { get; set; }

    [Parameter]
    public VesselDto TargetVessel { get; set; } = null!;
}
