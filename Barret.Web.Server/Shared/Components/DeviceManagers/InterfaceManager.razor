@using Barret.Core.Areas.Devices.Enums
@using Barret.Web.Server.Shared.Components.DeviceEditors

@using DevExpress.Blazor
@using Barret.Services.Core.Areas.Devices.Queries
@using Barret.Services.Core.Areas.Manufacturers
@using Barret.Services.Core.Areas.DeviceModels.Queries
@using Barret.Services.Core.Areas.Vehicles
@using Microsoft.Extensions.Logging
@using Barret.Shared.DTOs.Devices
@using Microsoft.AspNetCore.Components.Web
@using Barret.Web.Server.Services
@using Barret.Core.Areas.Vehicles.Models.Vessel
@using Barret.Shared.DTOs.Vehicles.Vessels

@using Barret.Shared.Factories
@using Barret.Web.Server.Extensions
@using Barret.Web.Server.Services.DTO
@inject IJSRuntime JSRuntime
@inject IDeviceQueryService DeviceQueryService
@inject IVehicleService<Vessel, VesselDto> VehicleService
@inject DeviceDtoService DeviceDtoService
@inject IManufacturerService ManufacturerService
@inject IDeviceModelQueryService DeviceModelQueryService
@inject IBarretToastNotificationService ToastService
@inject ILogger<InterfaceManager> _logger
@inject IDialogService DialogService

<div class="alert alert-warning">
    <i class="bi bi-exclamation-triangle-fill me-2"></i>
    <strong>Deprecated:</strong> Interfaces are being replaced by Connections. Please use the Connections tab instead.
</div>

<div class="connection-section">
    <div class="d-flex justify-content-between align-items-center mb-2">
        <h6 class="mb-0">Connections</h6>
        <button class="btn btn-sm btn-primary" @onclick="RedirectToConnectionManager">
            <i class="bi bi-arrow-right"></i> Go to Connection Manager
        </button>
    </div>

    @if (ParentDevice.Connections != null && ParentDevice.Connections.Any())
    {
        <div class="connection-container p-2">
            <DxGrid Data="@ParentDevice.Connections"
                    CssClass="compact-grid"
                    ShowFilterRow="true"
                    PageSize="5">
                <Columns>
                    <DxGridDataColumn Caption="Target Device" Width="30%">
                        <CellDisplayTemplate>
                            @{
                                var connection = (context.DataItem as DeviceConnectionDto);
                                <span>@GetTargetDeviceName(connection?.InterfaceDeviceId)</span>
                            }
                        </CellDisplayTemplate>
                    </DxGridDataColumn>
                    <DxGridDataColumn FieldName="Type" Caption="Type" Width="25%" />
                    <DxGridDataColumn FieldName="Direction" Caption="Direction" Width="25%" />
                    <DxGridDataColumn Caption="Actions" Width="20%">
                        <CellDisplayTemplate>
                            @{
                                var connection = (context.DataItem as DeviceConnectionDto);
                                if (connection != null)
                                {
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-sm btn-outline-primary" @onclick="RedirectToConnectionManager">
                                            <i class="bi bi-pencil"></i>
                                        </button>
                                    </div>
                                }
                            }
                        </CellDisplayTemplate>
                    </DxGridDataColumn>
                </Columns>
            </DxGrid>
        </div>
    }
    else
    {
        <div class="alert alert-light py-2">No connections configured</div>
    }
</div>

@code {
    [Parameter]
    public DeviceDto ParentDevice { get; set; }

    [Parameter]
    public EventCallback<DeviceDto> OnDeviceChanged { get; set; }

    [Parameter]
    public List<DeviceDto> AllDevices { get; set; }

    private bool isLoading;
    private string errorMessage;
    private bool _operationInProgress = false;

    private string GetTargetDeviceName(Guid? interfaceDeviceId)
    {
        if (interfaceDeviceId == null || AllDevices == null)
            return "Unknown Device";

        var device = AllDevices.FirstOrDefault(d => d.Id == interfaceDeviceId);
        return device?.Name ?? "Unknown Device";
    }

    private async Task RedirectToConnectionManager()
    {
        // Notify the parent that the user wants to switch to the Connection Manager
        await OnDeviceChanged.InvokeAsync(ParentDevice);

        // Show a toast notification
        ToastService.ShowToast(new ToastOptions
        {
            Title = "Information",
            Text = "Please use the Connections tab to manage device connections",
            IconCssClass = "bi bi-info-circle-fill",
            CssClass = "toast-info",
            ShowIcon = true
        });
    }
}
