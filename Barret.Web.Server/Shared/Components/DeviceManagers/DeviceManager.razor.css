/* Table styling */
.table-responsive {
    overflow-x: auto;
    max-width: 100%;
    margin-bottom: 1.5rem;
}

.table {
    width: 100%;
    margin-bottom: 0;
    color: #212529;
    vertical-align: middle;
    border-color: #dee2e6;
}

.table > thead {
    background-color: #f8f9fa;
}

.table > :not(:first-child) {
    border-top: 2px solid currentColor;
}

.table > tbody > tr:hover {
    background-color: rgba(0,0,0,.075);
}

/* Button styling */
.btn-group {
    display: flex;
    gap: 0.25rem;
}

/* Modal dialog */
.modal {
    display: block;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1050;
    width: 100%;
    height: 100%;
    overflow: hidden;
    outline: 0;
    background-color: rgba(0,0,0,0.5);
}

.modal-dialog {
    position: relative;
    width: auto;
    margin: 1.75rem auto;
    max-width: 500px;
}

.modal-content {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 100%;
    pointer-events: auto;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid rgba(0,0,0,.2);
    border-radius: 0.3rem;
    outline: 0;
}

.modal-header {
    display: flex;
    flex-shrink: 0;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    border-bottom: 1px solid #dee2e6;
    border-top-left-radius: calc(0.3rem - 1px);
    border-top-right-radius: calc(0.3rem - 1px);
}

.modal-body {
    position: relative;
    flex: 1 1 auto;
    padding: 1rem;
}

.modal-footer {
    display: flex;
    flex-wrap: wrap;
    flex-shrink: 0;
    align-items: center;
    justify-content: flex-end;
    padding: 0.75rem;
    border-top: 1px solid #dee2e6;
    border-bottom-right-radius: calc(0.3rem - 1px);
    border-bottom-left-radius: calc(0.3rem - 1px);
    gap: 0.5rem;
}

/* Form controls */
.form-label {
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.form-control {
    display: block;
    width: 100%;
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #212529;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #ced4da;
    appearance: none;
    border-radius: 0.25rem;
    transition: border-color .15s ease-in-out,box-shadow .15s ease-in-out;
}

.form-control:focus {
    color: #212529;
    background-color: #fff;
    border-color: #86b7fe;
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Alert styling */
.alert-info {
    color: #055160;
    background-color: #cff4fc;
    border-color: #b6effb;
}

/* Container spacing */
.container {
    padding-left: 1rem;
    padding-right: 1rem;
}

/* Responsive adjustments */
@media (max-width: 576px) {
    .modal-dialog {
        margin: 0.5rem;
        max-width: calc(100% - 1rem);
    }
    
    .btn-group {
        flex-direction: column;
    }
    
    .d-flex {
        flex-direction: column;
    }
    
    .d-flex > * {
        margin-bottom: 0.5rem;
    }
}