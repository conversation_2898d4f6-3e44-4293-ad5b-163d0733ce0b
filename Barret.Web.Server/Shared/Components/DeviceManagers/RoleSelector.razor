@using Barret.Core.Areas.Devices.Enums
@using Barret.Core.Areas.Devices.Extensions
@using Barret.Shared.DTOs.Devices
@using DevExpress.Blazor
@using Microsoft.Extensions.Logging
@inject ILogger<RoleSelector> Logger

<DxPopup HeaderText="Select Device Role"
         @bind-Visible="_popupVisible"
         Width="400px"
         CloseOnEscape="true"
         CloseOnOutsideClick="true"
         ShowFooter="true"
         Closing="@OnClosing">
    <HeaderContentTemplate>
        <h5 class="mb-0">Select Device Role</h5>
    </HeaderContentTemplate>
    <Content>
        <div class="mb-3">
            <p>Select a compatible device role for the interface:</p>

            @if (IsLoading)
            {
                <div class="d-flex justify-content-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            }
            else if (ErrorMessage != null)
            {
                <div class="alert alert-danger">@ErrorMessage</div>
            }
            else if (CompatibleRoles == null || !CompatibleRoles.Any())
            {
                <div class="alert alert-warning">No compatible device roles found for this device model.</div>
            }
            else
            {
                <div class="list-group">
                    @foreach (var role in CompatibleRoles)
                    {
                        <button type="button" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center"
                                @onclick="() => SelectRole(role)">
                            <span>@role</span>
                            <i class="bi bi-chevron-right"></i>
                        </button>
                    }
                </div>
            }
        </div>
    </Content>
    <FooterContentTemplate>
        <button class="btn btn-secondary" @onclick="HandleCancel">Cancel</button>
    </FooterContentTemplate>
</DxPopup>

@code {
    [Parameter]
    public DeviceDto SourceDevice { get; set; }

    [Parameter]
    public EventCallback<DeviceRole> OnRoleSelected { get; set; }

    [Parameter]
    public EventCallback OnCancelled { get; set; }

    // Private backing field for popup visibility
    private bool _popupVisible;

    // Flag to track if operation is in progress to avoid duplicate callbacks
    private bool _operationInProgress;

    // Public properties for component state
    public bool IsLoading { get; private set; }
    public string ErrorMessage { get; private set; }
    public List<DeviceRole> CompatibleRoles { get; private set; } = new();

    /// <summary>
    /// Allows setting compatible roles directly instead of loading them from the source device
    /// </summary>
    /// <param name="roles">The list of compatible roles</param>
    public void SetCompatibleRoles(List<DeviceRole> roles)
    {
        CompatibleRoles = roles ?? new List<DeviceRole>();
        IsLoading = false;
        ErrorMessage = null;
        StateHasChanged();
    }

    /// <summary>
    /// Shows the role selector popup and loads compatible roles.
    /// </summary>
    public void Show()
    {
        Logger.LogDebug("Showing role selector for device {DeviceId}", SourceDevice?.Id);
        _popupVisible = true;

        // We'll load roles when the popup is visible
        LoadCompatibleRoles();
        StateHasChanged();
    }

    /// <summary>
    /// Handles the popup closing event
    /// </summary>
    private void OnClosing(PopupClosingEventArgs args)
    {
        // If we're already handling an operation, let it proceed
        if (_operationInProgress) return;

        Logger.LogDebug("Role selector popup closing without explicit action");

        // Set the flag to avoid duplicate events
        _operationInProgress = true;

        // Schedule the callback after the current cycle
        InvokeAsync(async () =>
        {
            try
            {
                await OnCancelled.InvokeAsync();
            }
            finally
            {
                _operationInProgress = false;
            }
        });
    }

    /// <summary>
    /// Loads compatible roles for the current source device
    /// </summary>
    private void LoadCompatibleRoles()
    {
        IsLoading = true;
        ErrorMessage = null;

        try
        {
            // If we already have compatible roles set from the parent component, use those
            // Otherwise, fall back to the DeviceRoleExtensions if we have a source device
            if (CompatibleRoles.Count == 0 && SourceDevice != null)
            {
                Logger.LogDebug("Loading compatible roles for device {DeviceId} with role {DeviceRole}",
                    SourceDevice.Id, SourceDevice.DeviceRole);

                // Use the DeviceRoleExtensions to get compatible roles
                CompatibleRoles = SourceDevice.DeviceRole.GetCompatibleRoles().ToList();
                Logger.LogDebug("Found {Count} compatible roles using DeviceRoleExtensions", CompatibleRoles.Count);
            }
            else
            {
                Logger.LogDebug("Using {Count} pre-set compatible roles from collection", CompatibleRoles.Count);
            }

            if (CompatibleRoles.Count == 0)
            {
                ErrorMessage = "No compatible roles available.";
            }
        }
        catch (Exception ex)
        {
            ErrorMessage = $"Error loading compatible roles: {ex.Message}";
            Logger.LogError(ex, "Error loading compatible roles for device {DeviceId}", SourceDevice?.Id);
        }
        finally
        {
            IsLoading = false;
            StateHasChanged();
        }
    }



    /// <summary>
    /// Handles role selection and notifies the parent component
    /// </summary>
    private async Task SelectRole(DeviceRole role)
    {
        // Prevent multiple operations
        if (_operationInProgress) return;

        try
        {
            Logger.LogDebug("Role selected: {Role}", role);
            _operationInProgress = true;

            // First hide the popup
            _popupVisible = false;

            // Then notify the parent of the selection
            await OnRoleSelected.InvokeAsync(role);
        }
        finally
        {
            _operationInProgress = false;
        }
    }

    /// <summary>
    /// Handles cancel button click
    /// </summary>
    private async Task HandleCancel()
    {
        // Prevent multiple operations
        if (_operationInProgress) return;

        try
        {
            Logger.LogDebug("Role selection cancelled by user");
            _operationInProgress = true;

            // First hide the popup
            _popupVisible = false;

            // Then notify the parent of the cancellation
            await OnCancelled.InvokeAsync();
        }
        finally
        {
            _operationInProgress = false;
        }
    }
}