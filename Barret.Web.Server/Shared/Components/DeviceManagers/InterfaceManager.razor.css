.interface-container {
    border-radius: 4px;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
}

::deep .compact-grid {
    font-size: 0.875rem;
}

::deep .compact-grid .dxbs-grid-data td {
    padding: 0.4rem 0.5rem;
}

.interface-section {
    margin-bottom: 1rem;
}

.interface-section h6 {
    font-weight: 500;
}

.alert-light {
    background-color: #f8f9fa;
    border-color: #f5f5f5;
    color: #6c757d;
    text-align: center;
    font-size: 0.875rem;
}