using Barret.Core.Areas.Common.ValueObjects;
using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.Devices.Models;
using Barret.Core.Areas.Devices.Models.Alarms;
using Barret.Core.Areas.Devices.Models.Antennas;
using Barret.Core.Areas.Devices.Models.Audio;
using Barret.Core.Areas.Devices.Models.Autopilots;
using Barret.Core.Areas.Devices.Models.Cameras;
using Barret.Core.Areas.Devices.Models.DeviceModels;
using Barret.Core.Areas.Devices.Models.Engines;
using Barret.Core.Areas.Devices.Models.GenericDevices;
using Barret.Core.Areas.Devices.Models.Network;
using Barret.Core.Areas.Devices.Models.NVR;
using Barret.Core.Areas.Devices.Models.Radars;
using Barret.Core.Areas.Devices.Models.Radios;
using Barret.Core.Areas.Devices.Models.Sensors;
using Barret.Core.Areas.Devices.Models.Thrusters;
using Barret.Core.Areas.Devices.Models.Trackpilots;
using Barret.Core.Areas.Devices.Models.VCS;
using Barret.Core.Areas.Devices.ValueObjects;
using Barret.Core.Areas.Manufacturers.Models;
using Barret.Core.Areas.Vehicles.Models;
using Barret.Core.Areas.Vehicles.Models.Vessel;
using Barret.Core.Areas.Vehicles.ValueObjects;
using Barret.Services.Core.Areas.Contexts;
using Barret.Web.Server.Data.ValueConverters;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using System;

namespace Barret.Web.Server.Data.Contexts
{
    public class BarretDbContext(DbContextOptions<BarretDbContext> options) : IdentityDbContext(options), IBarretDbContext
    {
        public DbSet<Vehicle> Vehicles { get; set; }
        public DbSet<Vessel> Vessels { get; set; }
        public DbSet<GenericDevice> Devices { get; set; }
        public DbSet<Manufacturer> Manufacturers { get; set; }
        public DbSet<DeviceModel> DeviceModels { get; set; }

        public override int SaveChanges()
        {
            return base.SaveChanges();
        }

        public override Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            return base.SaveChangesAsync(cancellationToken);
        }


        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Register value converters for immutable value objects
            var connectionConverter = new ConnectionHandlerConverter();
            var positionConverter = new RelativePositionConverter();
            var dimensionsConverter = new DimensionsConverter();
            var deviceConnectionConverter = new DeviceConnectionConverter();

            // Configure GenericDevice entity to use value converters
            modelBuilder.Entity<GenericDevice>()
                .Property(d => d.Connection)
                .HasConversion(connectionConverter);

            modelBuilder.Entity<GenericDevice>()
                .Property(d => d.Position)
                .HasConversion(positionConverter);

            // Configure the Alarms collection as a JSON column (following Vehicle.DeviceConnections pattern)
            modelBuilder.Entity<GenericDevice>()
                .Property(d => d.Alarms)
                .HasConversion(
                    v => System.Text.Json.JsonSerializer.Serialize(v.Select(alarm => new Barret.Shared.DTOs.Devices.Alarms.AlarmDto
                    {
                        Id = alarm.Id,
                        Description = alarm.Description,
                        NotificationType = alarm.NotificationType,
                        Message = alarm.Message,
                        NotificationGroupId = alarm.NotificationGroupId,
                        EntityId = alarm.EntityId,
                        WarningId = alarm.WarningId
                    }), new System.Text.Json.JsonSerializerOptions()),
                    v => ConvertJsonToAlarms(v)
                );

            // Configure Vehicle entity to use value converters
            modelBuilder.Entity<Vehicle>()
                .Property(v => v.Dimensions)
                .HasConversion(dimensionsConverter);

            // ===== Configure Vehicle entities =====
            ConfigureVehicleEntities(modelBuilder);

            // ===== Configure Device entities =====
            ConfigureDeviceEntities(modelBuilder);

            // Device Group Mapping has been removed

            // ===== Configure Manufacturer entities =====
            ConfigureManufacturerEntities(modelBuilder);

            // ===== Configure DeviceModel entities =====
            ConfigureDeviceModelEntities(modelBuilder);
        }

        private static void ConfigureVehicleEntities(ModelBuilder modelBuilder)
        {
            // Configure Vehicle base entity
            modelBuilder.Entity<Vehicle>(builder =>
            {
                // Configure the primary key
                builder.HasKey(v => v.Id);

                // Configure table name and use TPH inheritance for better performance
                builder.ToTable("Vehicles");
                builder.UseTphMappingStrategy();

                // Configure the VehicleId property - this is abstract in the base class
                builder.Property(v => v.VehicleId)
                    .HasColumnName("VehicleId")
                    .IsRequired()
                    .HasMaxLength(50);

                // Configure the VehicleName property
                builder.Property(v => v.VehicleName)
                    .HasColumnName("VehicleName")
                    .IsRequired()
                    .HasMaxLength(100);

                // Configure the Dimensions property to use the value converter
                // The converter is registered in OnModelCreating

                // Configure the DeviceConnections collection as a JSON column
                builder.Property(v => v.DeviceConnections)
                    .HasColumnName("DeviceConnections")
                    .HasConversion(
                        v => System.Text.Json.JsonSerializer.Serialize(v, new System.Text.Json.JsonSerializerOptions()),
                        v => System.Text.Json.JsonSerializer.Deserialize<List<DeviceConnection>>(v, new System.Text.Json.JsonSerializerOptions()) ?? new List<DeviceConnection>()
                    );

                // Configure discriminator for TPH inheritance
                builder.HasDiscriminator<string>("VehicleType")
                    .HasValue<Vehicle>("Vehicle")
                    .HasValue<Vessel>("Vessel");

                // Explicitly ignore base Vehicle device group properties
                // These are in-memory domain objects and should not be persisted
                builder.Ignore(v => v.CameraGroup);
                builder.Ignore(v => v.EngineGroup);
            });

            // Configure Vessel entity (now using TPH inheritance)
            modelBuilder.Entity<Vessel>(builder =>
            {
                // Configure the Name property
                builder.Property(v => v.Name)
                    .HasColumnName("Name")
                    .IsRequired()
                    .HasMaxLength(100);

                // Configure the ENI property
                builder.Property(v => v.ENI)
                    .HasColumnName("ENI")
                    .HasMaxLength(8);

                // Configure the MMSI property
                builder.Property(v => v.MMSI)
                    .HasColumnName("MMSI")
                    .HasMaxLength(9);

                // Configure generic relationship for all devices
                // All devices are now managed through simple lists
                // and are accessed via the GetAllDevices() method

                // Explicitly ignore Vessel-specific device group properties
                // These are in-memory domain objects and should not be persisted
                // ALL DeviceGroup types not in Vehicle base class
                builder.Ignore(v => v.ThrusterGroup);
                builder.Ignore(v => v.AntennaGroup);
                builder.Ignore(v => v.AudioGroup);
                builder.Ignore(v => v.AutopilotGroup);
                builder.Ignore(v => v.HornGroup);
                builder.Ignore(v => v.LightGroup);
                builder.Ignore(v => v.RadarGroup);
                builder.Ignore(v => v.RadioGroup);
                builder.Ignore(v => v.RudderGroup);
                builder.Ignore(v => v.SeafarNetworkGroup);
                builder.Ignore(v => v.SeafarVCSGroup);
                builder.Ignore(v => v.SensorGroup);
                builder.Ignore(v => v.TrackpilotGroup);
            });
        }

        private static void ConfigureDeviceEntities(ModelBuilder modelBuilder)
        {
            // Configure GenericDevice base entity
            modelBuilder.Entity<GenericDevice>(builder =>
            {
                // Configure the primary key
                builder.HasKey(d => d.Id);

                // Configure the Name property
                builder.Property(d => d.Name)
                    .HasColumnName("Name")
                    .IsRequired()
                    .HasMaxLength(100);

                // Configure the Position and Connection properties to use value converters
                // The converters are registered in OnModelCreating

                // Configure the relationship with Model using the explicit DeviceModelId property
                var navigation = builder.Metadata.FindNavigation(nameof(GenericDevice.Model));
                navigation?.SetPropertyAccessMode(PropertyAccessMode.Field);

                // Configure the DeviceModelId property
                builder.Property(d => d.DeviceModelId)
                    .HasColumnName("DeviceModelId");

                // Configure the relationship using the explicit property
                builder.HasOne(d => d.Model)
                    .WithMany()
                    .HasForeignKey(d => d.DeviceModelId)
                    .OnDelete(DeleteBehavior.Restrict);

                // Configure TPH inheritance using the DeviceRole property directly as the discriminator
                // This ensures that the discriminator value in the database matches the DeviceRole enum value
                // and EF Core will correctly materialize the right type based on the DeviceRole
                builder.HasDiscriminator(d => d.DeviceRole)
                    // Camera System
                    .HasValue<Camera>(DeviceRole.Camera)
                    .HasValue<NVRScreen>(DeviceRole.NVRScreen)
                    .HasValue<NVRRecording>(DeviceRole.NVRRecording)

                    // Propulsion
                    .HasValue<Engine>(DeviceRole.Engine)
                    .HasValue<Thruster>(DeviceRole.Thruster)

                    // Navigation
                    .HasValue<Radar>(DeviceRole.Radar)
                    .HasValue<Antenna>(DeviceRole.Antenna)
                    .HasValue<Autopilot>(DeviceRole.Autopilot)
                    .HasValue<Trackpilot>(DeviceRole.Trackpilot)

                    // Radios
                    .HasValue<VHFMariphone>(DeviceRole.VHFMariphone)
                    .HasValue<VHFNetworkInterface>(DeviceRole.VHFNetworkInterface)

                    // Audio
                    .HasValue<AudioHub>(DeviceRole.AudioHub)
                    .HasValue<PAAudio>(DeviceRole.PAAudio)

                    // VCS
                    .HasValue<AMP>(DeviceRole.AMP)
                    .HasValue<SPAP>(DeviceRole.SPAP)
                    .HasValue<HMI>(DeviceRole.HMI)
                    .HasValue<CabinetReadoutIO>(DeviceRole.CabinetReadoutIO)
                    .HasValue<OperatorPanelIO>(DeviceRole.OperatorPanelIO)
                    .HasValue<GPU>(DeviceRole.GPU)
                    .HasValue<SafetySystemHead>(DeviceRole.SafetySystemHead)

                    // Network
                    .HasValue<Firewall>(DeviceRole.Firewall)
                    .HasValue<Gateway>(DeviceRole.Gateway)
                    .HasValue<Switch>(DeviceRole.Switch)
                    .HasValue<Plc>(DeviceRole.Plc)

                    // Sensors
                    .HasValue<NavData>(DeviceRole.NavData)
                    .HasValue<Sensor>(DeviceRole.Sensor)

                    // Default
                    .HasValue<GenericDevice>(DeviceRole.Generic)

                    // Map remaining roles to GenericDevice
                    .HasValue<GenericDevice>(DeviceRole.Framegrabber)
                    .HasValue<GenericDevice>(DeviceRole.Light)
                    .HasValue<GenericDevice>(DeviceRole.Rudder)
                    .HasValue<GenericDevice>(DeviceRole.Horn)
                    .HasValue<GenericDevice>(DeviceRole.Undefined);

                // Configure discriminator to never be null and use exact matching
                builder.Property(d => d.DeviceRole)
                    .IsRequired()
                    .HasConversion<int>();

                // Explicitly tell EF Core to use TPH mapping strategy
                builder.UseTphMappingStrategy();

                // Force EF Core to always include the discriminator in queries
                builder.HasQueryFilter(d => true);

                // Configure the VehicleId property for the relationship with Vehicle
                builder.Property(d => d.VehicleId);
                builder.HasOne<Vehicle>()
                    .WithMany()
                    .HasForeignKey(d => d.VehicleId)
                    .OnDelete(DeleteBehavior.Cascade);

                // Index the VehicleId for better performance when querying devices by vehicle
                builder.HasIndex(d => d.VehicleId);

                // Explicitly configure that we don't want EF Core to try to map the device groups
                // as these are handled by our custom device group classes and should not be persisted
                // This configuration is now handled in the Vehicle entity configuration

                // Interfaces property has been removed from the domain model
                // No need to ignore it anymore
            });

            // Configure Radar-specific properties
            ConfigureRadarEntities(modelBuilder);
        }

        // ConfigureDeviceGroupMappings method has been removed

        private static void ConfigureRadarEntities(ModelBuilder modelBuilder)
        {
            // Configure Radar entity
            modelBuilder.Entity<Radar>(builder =>
            {
                // Configure the MaritimePosition owned entity
                builder.OwnsOne(r => r.MaritimePosition, p =>
                {
                    p.Property(pos => pos.ForeAft)
                        .HasColumnName("MaritimePosition_ForeAft")
                        .HasConversion<string>()
                        .IsRequired();

                    p.Property(pos => pos.Lateral)
                        .HasColumnName("MaritimePosition_Lateral")
                        .HasConversion<string>()
                        .IsRequired();

                    p.Property(pos => pos.Facing)
                        .HasColumnName("MaritimePosition_Facing")
                        .HasConversion<string>();
                });
            });
        }

        private static void ConfigureManufacturerEntities(ModelBuilder modelBuilder)
        {
            // Configure Manufacturer entity
            modelBuilder.Entity<Manufacturer>(builder =>
            {
                // Configure the primary key
                builder.HasKey(m => m.Id);

                // Configure the Name property
                builder.Property(m => m.Name)
                    .HasColumnName("Name")
                    .IsRequired()
                    .HasMaxLength(100);

                // Configure the relationship with DeviceModels (owned by this aggregate)
                builder.HasMany(m => m.DeviceModels)
                    .WithOne(dm => dm.Manufacturer)
                    .HasForeignKey(dm => dm.ManufacturerId)
                    .OnDelete(DeleteBehavior.Cascade);
            });
        }

        private static void ConfigureDeviceModelEntities(ModelBuilder modelBuilder)
        {
            // Configure DeviceModel entity (part of Manufacturer aggregate)
            modelBuilder.Entity<DeviceModel>(builder =>
            {
                // Configure the primary key
                builder.HasKey(dm => dm.Id);

                // Configure the Name property
                builder.Property(dm => dm.Name)
                    .HasColumnName("Name")
                    .IsRequired()
                    .HasMaxLength(100);

                // Configure the DeviceRole property
                builder.Property(dm => dm.DeviceRole)
                    .HasColumnName("DeviceRole")
                    .HasConversion<int>();

                // Configure the ManufacturerId property (relationship to aggregate root)
                builder.Property(dm => dm.ManufacturerId)
                    .HasColumnName("ManufacturerId")
                    .IsRequired();

                // Compatible roles are now handled by DeviceRoleExtensions instead of being stored in the database

                // Configure auto-loading behavior for Manufacturer
                builder.Navigation(dm => dm.Manufacturer)
                    .AutoInclude();
            });
        }

        /// <summary>
        /// Helper method to convert JSON string to alarm collection.
        /// This is needed because expression trees cannot contain null propagating operators.
        /// Returns List&lt;Alarm&gt; to match EF Core's expectations for the backing field.
        /// </summary>
        private static List<Barret.Core.Areas.Devices.Models.Alarms.Alarm> ConvertJsonToAlarms(string json)
        {
            if (string.IsNullOrEmpty(json) || json == "[]")
                return new List<Barret.Core.Areas.Devices.Models.Alarms.Alarm>();

            try
            {
                var dtos = System.Text.Json.JsonSerializer.Deserialize<List<Barret.Shared.DTOs.Devices.Alarms.AlarmDto>>(json, new System.Text.Json.JsonSerializerOptions());
                if (dtos == null)
                    return new List<Barret.Core.Areas.Devices.Models.Alarms.Alarm>();

                var alarms = dtos
                    .Where(dto => dto.Id != Guid.Empty && !string.IsNullOrWhiteSpace(dto.Description) && dto.NotificationType != Barret.Core.Areas.Devices.Enums.NotificationType.Undefined)
                    .Select(dto => new Barret.Core.Areas.Devices.Models.Alarms.Alarm(
                        dto.Id,
                        dto.Description,
                        dto.NotificationType,
                        dto.Message ?? string.Empty,
                        dto.NotificationGroupId,
                        dto.EntityId ?? string.Empty,
                        dto.WarningId))
                    .ToList();

                return alarms;
            }
            catch (Exception)
            {
                // Fallback to empty list if there's any error
                return new List<Barret.Core.Areas.Devices.Models.Alarms.Alarm>();
            }
        }

    }
}
