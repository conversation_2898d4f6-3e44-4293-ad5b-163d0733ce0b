# Blazor Server MVVM Architecture Guide

## Overview

This guide outlines the recommended structure and architecture for Blazor Server applications using the MVVM (Model-View-ViewModel) pattern with ReactiveUI. This architecture is designed to be applied consistently across all features of the application, providing a standardized approach to frontend development.

The architecture emphasizes:

- **Clear separation of concerns** using the MVVM pattern
- **Component-based development** with DevExpress Blazor components
- **Clean separation of files**:
  - Razor files (.razor) for HTML markup
  - Code-behind files (.razor.cs) for component logic
  - Tailwind CSS for styling
- **Reactive programming** with ReactiveUI for responsive UI
- **DTO-based editing workflow** for data manipulation
- **Domain-driven design** principles respecting aggregate boundaries
- **Maintainable and testable code** with proper separation of responsibilities
- **Efficient state management** with clear ownership and lifecycle management
- **Optimized performance** through proper component design and data flow patterns

## Core Architectural Principles

Before diving into the specific implementation details, it's important to understand the core principles that guide this architecture:

### 1. Single Source of Truth

- Each piece of data should have a single, authoritative source
- Avoid duplicating state across components or services
- Use a clear ownership model for data (typically the ViewModel)
- Derive UI state from the core data model rather than maintaining separate state

### 2. Unidirectional Data Flow

- Data should flow in one direction: from parent to child components
- Child components should communicate changes back to parents via events/callbacks
- Avoid bidirectional data binding that can lead to cascading updates
- Use a clear pattern for state updates (e.g., command → service → state change → UI update)

### 3. Clear Separation of Concerns

- **Views** (Razor components): Responsible for rendering UI and handling user input
- **ViewModels**: Contain presentation logic, state, and commands
- **Services**: Handle business logic, data access, and cross-cutting concerns
- **DTOs**: Transfer data between layers without exposing domain implementation details

### 4. Immutability and Predictability

- Prefer immutable data structures where possible
- Make state changes explicit and traceable
- Avoid side effects in property getters or setters
- Use commands to encapsulate user actions and their effects

### 5. Composition Over Inheritance

- Build complex UIs by composing smaller, focused components
- Limit inheritance depth for components and ViewModels
- Use interfaces and dependency injection for shared behavior
- Prefer delegation over inheritance for reusing functionality

### 6. Proper Resource Management

- Dispose of resources properly (subscriptions, event handlers, etc.)
- Implement IDisposable and IAsyncDisposable where appropriate
- Clean up event handlers and subscriptions to prevent memory leaks
- Use a consistent pattern for resource cleanup

### 7. Performance by Design

- Design components with performance in mind from the start
- Minimize unnecessary renders and state updates
- Use appropriate data structures for efficient operations
- Implement virtualization for large data sets

## Project Structure

### Solution Structure

```
YourSolution/
├── YourApp.Web.Server/           # Main Blazor Server project
├── YourApp.Core/                 # Core domain models and business logic
├── YourApp.Services.Core/        # Core services and application logic
└── YourApp.Shared/               # Shared DTOs and interfaces
```

### Main Blazor Server Project Structure

```
YourApp.Web.Server/
├── Features/                     # Feature-based organization
│   ├── FeatureOne/               # e.g., Vehicles
│   │   ├── Components/           # Feature-specific components
│   │   │   ├── ComponentName.razor       # Component UI markup
│   │   │   └── ComponentName.razor.cs    # Component code-behind
│   │   ├── Data/                 # Feature-specific data models
│   │   │   └── FeatureData.cs            # Data models for the feature
│   │   ├── Services/             # Feature-specific services
│   │   │   └── FeatureService.cs         # Services for the feature
│   │   ├── ViewModels/           # Feature-specific view models
│   │   │   └── FeatureViewModel.cs       # View models for the feature
│   │   └── Views/                # Feature-specific views
│   │       ├── FeatureView.razor         # View UI markup
│   │       └── FeatureView.razor.cs      # View code-behind
│   │
│   ├── Shared/                   # Shared feature components
│   │   ├── Components/           # Shared components across features
│   │   │   └── Layout/           # Layout components
│   │   │       ├── MainLayout.razor      # Main layout UI markup
│   │   │       └── MainLayout.razor.cs   # Main layout code-behind
│   │   ├── ViewBase.cs           # Base class for views
│   │   └── ViewModelBase.cs      # Base class for view models
│   │
│   └── Admin/                    # Admin feature
│       ├── Components/           # Admin-specific components
│       ├── Data/                 # Admin-specific data models
│       ├── ViewModels/           # Admin-specific view models
│       └── Views/                # Admin-specific views
│
├── Services/                     # Frontend services
│   ├── Dialog/                   # Dialog services
│   ├── Notification/             # Notification services
│   ├── Navigation/               # Navigation services
│   └── State/                    # State management services
│
├── Shared/                       # Shared resources
│   ├── Components/               # Shared UI components
│   │   ├── DevExpressWrappers/   # DevExpress component wrappers
│   │   │   ├── AppGrid.razor     # Grid component wrapper
│   │   │   └── AppButton.razor   # Button component wrapper
│   │   └── Common/               # Common UI components
│   └── Extensions/               # Extension methods
│
└── wwwroot/                      # Static web assets
    ├── css/                      # CSS files
    │   └── app.css               # Main CSS file with Tailwind imports
    └── js/                       # JavaScript files
```

## File Naming Conventions

### Razor Components

- **Views**: `FeatureNameView.razor` (e.g., `VehicleEditorView.razor`)
- **Components**: `ComponentName.razor` (e.g., `DeviceList.razor`)
- **Layouts**: `LayoutName.razor` (e.g., `MainLayout.razor`)

### Code-Behind Files

- **Views**: `FeatureNameView.razor.cs` (e.g., `VehicleEditorView.razor.cs`)
- **Components**: `ComponentName.razor.cs` (e.g., `DeviceList.razor.cs`)
- **Layouts**: `LayoutName.razor.cs` (e.g., `MainLayout.razor.cs`)

### ViewModels

- **Views**: `FeatureNameViewModel.cs` (e.g., `VehicleEditorViewModel.cs`)
- **Components**: `ComponentNameViewModel.cs` (e.g., `DeviceListViewModel.cs`)

### Services

- **Services**: `ServiceName.cs` (e.g., `NotificationService.cs`)
- **Interfaces**: `IServiceName.cs` (e.g., `INotificationService.cs`)

## Core Architectural Components

### 1. Views with ReactiveUI

Views are implemented as Razor components with separate code-behind files, using ReactiveUI for reactive UI capabilities.

#### View Implementation Pattern

```razor
@page "/feature/action"
@using YourApp.Web.Server.Features.Feature.ViewModels
@inherits YourApp.Web.Server.Features.Shared.ViewBase<FeatureViewModel>

<PageTitle>Feature Title</PageTitle>

<div class="container mx-auto p-4">
    <h1 class="text-2xl font-bold mb-4">@ViewModel.Title</h1>

    @if (ViewModel.IsLoading)
    {
        <div class="flex justify-center">
            <div class="animate-spin rounded-full h-12 w-12 border-4 border-gray-300 border-t-gray-800"></div>
        </div>
    }
    else
    {
        <!-- Feature content -->
        <FeatureComponent Data="@ViewModel.Data" OnAction="@ViewModel.HandleAction" />
    </div>
</div>
```

#### Code-Behind Implementation Pattern

```csharp
using Microsoft.AspNetCore.Components;
using YourApp.Web.Server.Features.Feature.ViewModels;
using YourApp.Web.Server.Features.Shared;
using YourApp.Web.Server.Services.Notification;

namespace YourApp.Web.Server.Features.Feature.Views
{
    public partial class FeatureView
    {
        [Inject]
        private INotificationService NotificationService { get; set; } = null!;

        [Inject]
        private IFeatureViewModelFactory ViewModelFactory { get; set; } = null!;

        protected override void OnInitialized()
        {
            // Initialize ViewModel
            ViewModel = ViewModelFactory.Create();

            base.OnInitialized();
        }

        protected override async Task OnParametersSetAsync()
        {
            await ViewModel.LoadDataAsync();
            await base.OnParametersSetAsync();
        }
    }
}
```

### 2. ViewModels with ReactiveUI

ViewModels contain the presentation logic and state for the views using ReactiveUI for reactive programming features. They should be designed to fully leverage ReactiveUI's capabilities for reactive programming.

#### ViewModel Design Principles

1. **Single Responsibility**: Each ViewModel should have a single responsibility and represent a single logical unit of UI.

2. **State as Properties**: All UI state should be exposed as properties, not fields.

3. **Commands for Actions**: All user actions should be represented as ReactiveUI commands.

4. **Derived Properties**: Use derived properties (via ObservableAsPropertyHelper) for values that can be calculated from other properties.

5. **Immutable Data**: Prefer immutable data structures for complex objects to prevent unintended side effects.

6. **Proper Resource Management**: Implement IDisposable to clean up resources and subscriptions.

7. **Error Handling**: Implement consistent error handling for all operations.

8. **Minimal Dependencies**: Depend only on services and interfaces, not on UI components or other ViewModels.

#### Reactive Properties Implementation

There are three main approaches to implementing reactive properties in ReactiveUI:

1. **Manual Implementation** (most verbose but most control):

```csharp
private string _searchText = string.Empty;
public string SearchText
{
    get => _searchText;
    set => this.RaiseAndSetIfChanged(ref _searchText, value);
}
```

2. **ReactiveUI.Fody** (concise with [Reactive] attribute):

```csharp
[Reactive]
public string SearchText { get; set; } = string.Empty;
```

3. **Derived Properties** (for values calculated from other properties):

```csharp
// Field to store the helper
private readonly ObservableAsPropertyHelper<bool> _canSearch;

// Property that exposes the value
public bool CanSearch => _canSearch.Value;

// In constructor or initialization method
this.WhenAnyValue(x => x.SearchText)
    .Select(text => !string.IsNullOrWhiteSpace(text))
    .ToProperty(this, x => x.CanSearch, out _canSearch);
```

#### Commands Implementation

ReactiveUI commands should be used for all user actions:

```csharp
// Command declaration
public ReactiveCommand<Unit, Unit> SearchCommand { get; }

// In constructor
// Create a command with a can-execute observable
SearchCommand = ReactiveCommand.CreateFromTask(
    SearchAsync,
    this.WhenAnyValue(
        x => x.IsLoading,
        x => x.CanSearch,
        (isLoading, canSearch) => !isLoading && canSearch)
);

// Handle command errors
SearchCommand.ThrownExceptions
    .Subscribe(ex =>
    {
        _logger.LogError(ex, "Error executing search");
        ErrorMessage = $"Search failed: {ex.Message}";
    });
```

#### ViewModel Implementation Pattern

```csharp
using System;
using System.Collections.ObjectModel;
using System.Reactive;
using System.Reactive.Linq;
using System.Reactive.Disposables;
using Microsoft.Extensions.Logging;
using ReactiveUI;
using ReactiveUI.Fody.Helpers;
using YourApp.Shared.DTOs;
using YourApp.Web.Server.Features.Shared;
using YourApp.Web.Server.Services.Feature;

namespace YourApp.Web.Server.Features.Feature.ViewModels
{
    public class FeatureViewModel : ViewModelBase, IDisposable
    {
        private readonly IFeatureService _featureService;
        private readonly ILogger<FeatureViewModel> _logger;
        private readonly CompositeDisposable _disposables = new();

        // Reactive properties using ReactiveUI.Fody
        [Reactive]
        public bool IsLoading { get; private set; }

        [Reactive]
        public string SearchText { get; set; } = string.Empty;

        [Reactive]
        public FeatureDto? SelectedItem { get; set; }

        [Reactive]
        public string ErrorMessage { get; private set; } = string.Empty;

        // Derived properties
        private readonly ObservableAsPropertyHelper<bool> _canSearch;
        public bool CanSearch => _canSearch.Value;

        private readonly ObservableAsPropertyHelper<bool> _isDirty;
        public bool IsDirty => _isDirty.Value;

        // Observable collections
        private readonly ReadOnlyObservableCollection<FeatureDto> _items;
        public ReadOnlyObservableCollection<FeatureDto> Items => _items;

        // Source collection for _items
        private readonly SourceCache<FeatureDto, Guid> _itemsSource;

        // ReactiveUI commands
        public ReactiveCommand<Unit, Unit> SearchCommand { get; }
        public ReactiveCommand<Unit, Unit> SaveCommand { get; }
        public ReactiveCommand<Unit, Unit> RefreshCommand { get; }
        public ReactiveCommand<FeatureDto, Unit> DeleteItemCommand { get; }

        // Constructor
        public FeatureViewModel(
            IFeatureService featureService,
            ILogger<FeatureViewModel> logger)
        {
            _featureService = featureService;
            _logger = logger;

            // Initialize source cache for items
            _itemsSource = new SourceCache<FeatureDto, Guid>(item => item.Id);

            // Connect the source cache to the read-only observable collection
            _itemsSource.Connect()
                .Bind(out _items)
                .Subscribe()
                .DisposeWith(_disposables);

            // Set up derived properties
            this.WhenAnyValue(x => x.SearchText)
                .Select(text => !string.IsNullOrWhiteSpace(text))
                .ToProperty(this, x => x.CanSearch, out _canSearch)
                .DisposeWith(_disposables);

            // Track dirty state based on changes to the items collection
            _itemsSource.Connect()
                .WhenAnyPropertyChanged()
                .Select(_ => true)
                .ToProperty(this, x => x.IsDirty, out _isDirty, false)
                .DisposeWith(_disposables);

            // Initialize commands
            SearchCommand = ReactiveCommand.CreateFromTask(
                SearchAsync,
                this.WhenAnyValue(
                    x => x.IsLoading,
                    x => x.CanSearch,
                    (isLoading, canSearch) => !isLoading && canSearch)
            );

            SaveCommand = ReactiveCommand.CreateFromTask(
                SaveAsync,
                this.WhenAnyValue(
                    x => x.IsLoading,
                    x => x.IsDirty,
                    (isLoading, isDirty) => !isLoading && isDirty)
            );

            RefreshCommand = ReactiveCommand.CreateFromTask(
                LoadDataAsync,
                this.WhenAnyValue(x => x.IsLoading)
                    .Select(isLoading => !isLoading)
            );

            DeleteItemCommand = ReactiveCommand.Create<FeatureDto>(
                item =>
                {
                    _itemsSource.Remove(item);
                },
                this.WhenAnyValue(x => x.IsLoading)
                    .Select(isLoading => !isLoading)
            );

            // Set up property changed observables for automatic search
            this.WhenAnyValue(x => x.SearchText)
                .Throttle(TimeSpan.FromMilliseconds(500))
                .Where(x => !string.IsNullOrWhiteSpace(x))
                .Select(_ => Unit.Default)
                .InvokeCommand(SearchCommand)
                .DisposeWith(_disposables);

            // Handle command errors
            SearchCommand.ThrownExceptions
                .Subscribe(ex =>
                {
                    _logger.LogError(ex, "Error executing search");
                    ErrorMessage = $"Search failed: {ex.Message}";
                })
                .DisposeWith(_disposables);

            SaveCommand.ThrownExceptions
                .Subscribe(ex =>
                {
                    _logger.LogError(ex, "Error saving data");
                    ErrorMessage = $"Save failed: {ex.Message}";
                })
                .DisposeWith(_disposables);

            RefreshCommand.ThrownExceptions
                .Subscribe(ex =>
                {
                    _logger.LogError(ex, "Error refreshing data");
                    ErrorMessage = $"Refresh failed: {ex.Message}";
                })
                .DisposeWith(_disposables);
        }

        // Methods
        public async Task LoadDataAsync()
        {
            try
            {
                IsLoading = true;
                ErrorMessage = string.Empty;

                var items = await _featureService.GetAllAsync();

                // Update the source cache
                _itemsSource.Edit(innerCache =>
                {
                    innerCache.Clear();
                    innerCache.AddOrUpdate(items);
                });

                // Reset dirty state after loading
                ResetDirtyState();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading data");
                ErrorMessage = $"Failed to load data: {ex.Message}";
                throw;
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task SearchAsync()
        {
            try
            {
                IsLoading = true;
                ErrorMessage = string.Empty;

                var items = await _featureService.SearchAsync(SearchText);

                // Update the source cache
                _itemsSource.Edit(innerCache =>
                {
                    innerCache.Clear();
                    innerCache.AddOrUpdate(items);
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching");
                ErrorMessage = $"Search failed: {ex.Message}";
                throw;
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task SaveAsync()
        {
            try
            {
                IsLoading = true;
                ErrorMessage = string.Empty;

                // Save changes
                await _featureService.SaveAsync(Items.ToList());

                // Reset dirty state after saving
                ResetDirtyState();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving data");
                ErrorMessage = $"Save failed: {ex.Message}";
                throw;
            }
            finally
            {
                IsLoading = false;
            }
        }

        private void ResetDirtyState()
        {
            // Reset the dirty tracking by re-establishing the connection
            // This is needed because ToProperty doesn't have a way to directly set the value
            _itemsSource.Connect()
                .WhenAnyPropertyChanged()
                .Select(_ => true)
                .ToProperty(this, x => x.IsDirty, out _isDirty, false)
                .DisposeWith(_disposables);
        }

        public void Dispose()
        {
            // Dispose all subscriptions
            _disposables.Dispose();
            GC.SuppressFinalize(this);
        }
    }
}
```

### 3. Base Classes

#### ViewBase

The `ViewBase<TViewModel>` class provides a common base for all views that use a ViewModel. It should handle common concerns like component lifecycle, ReactiveUI integration, and resource management.

```csharp
using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Reactive.Disposables;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using ReactiveUI;

namespace YourApp.Web.Server.Features.Shared
{
    /// <summary>
    /// Base class for all views that use a ViewModel.
    /// Provides common functionality for component lifecycle management,
    /// ReactiveUI integration, and resource management.
    /// </summary>
    /// <typeparam name="TViewModel">The type of ViewModel used by this view.</typeparam>
    public abstract class ViewBase<TViewModel> : ComponentBase, IDisposable, IAsyncDisposable
        where TViewModel : ViewModelBase
    {
        private bool _isDisposed;
        private bool _isInitialized;
        private bool _isFirstRender = true;

        /// <summary>
        /// Composite disposable for managing subscriptions.
        /// </summary>
        protected readonly CompositeDisposable Disposables = new();

        /// <summary>
        /// Logger for this view.
        /// </summary>
        [Inject]
        protected ILogger<ViewBase<TViewModel>> Logger { get; set; } = null!;

        /// <summary>
        /// The ViewModel for this view.
        /// </summary>
        [Parameter]
        public TViewModel ViewModel { get; set; } = null!;

        /// <summary>
        /// Called when the component is initialized.
        /// </summary>
        protected override void OnInitialized()
        {
            Logger.LogDebug("Initializing {ComponentType}", GetType().Name);

            base.OnInitialized();

            if (ViewModel == null)
            {
                throw new InvalidOperationException($"ViewModel of type {typeof(TViewModel).Name} is required.");
            }

            // Set up any initial subscriptions here
            SetupSubscriptions();

            _isInitialized = true;
        }

        /// <summary>
        /// Called when the component parameters are set.
        /// </summary>
        protected override void OnParametersSet()
        {
            base.OnParametersSet();

            if (ViewModel == null)
            {
                throw new InvalidOperationException($"ViewModel of type {typeof(TViewModel).Name} is required.");
            }

            // If parameters have changed and the component is already initialized,
            // you might need to update subscriptions or perform other actions
            if (_isInitialized)
            {
                // Handle parameter changes here
                HandleParameterChanges();
            }
        }

        /// <summary>
        /// Called after the component is rendered.
        /// </summary>
        /// <param name="firstRender">True if this is the first time the component has been rendered.</param>
        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            await base.OnAfterRenderAsync(firstRender);

            if (firstRender)
            {
                _isFirstRender = false;
                await OnFirstRenderAsync();
            }
        }

        /// <summary>
        /// Called after the component is rendered for the first time.
        /// Override this method to perform one-time initialization that requires the component to be rendered.
        /// </summary>
        protected virtual Task OnFirstRenderAsync()
        {
            return Task.CompletedTask;
        }

        /// <summary>
        /// Sets up subscriptions to ViewModel properties.
        /// Override this method to set up subscriptions to ViewModel properties.
        /// </summary>
        protected virtual void SetupSubscriptions()
        {
            // Example: Subscribe to ViewModel property changes
            // this.WhenAnyValue(x => x.ViewModel.SomeProperty)
            //     .Subscribe(_ => StateHasChanged())
            //     .DisposeWith(Disposables);
        }

        /// <summary>
        /// Handles parameter changes.
        /// Override this method to handle parameter changes.
        /// </summary>
        protected virtual void HandleParameterChanges()
        {
            // Handle parameter changes here
        }

        /// <summary>
        /// Creates a subscription to a ViewModel property that triggers StateHasChanged.
        /// </summary>
        /// <typeparam name="T">The type of the property.</typeparam>
        /// <param name="property">An expression that returns the property to subscribe to.</param>
        /// <param name="action">An optional action to perform when the property changes.</param>
        protected void WhenPropertyChanged<T>(Expression<Func<TViewModel, T>> property, Action? action = null)
        {
            ViewModel.WhenAnyValue(property)
                .Subscribe(_ =>
                {
                    action?.Invoke();
                    InvokeAsync(StateHasChanged);
                })
                .DisposeWith(Disposables);
        }

        /// <summary>
        /// Executes an operation with error handling.
        /// </summary>
        /// <param name="operation">The operation to execute.</param>
        /// <param name="errorMessage">The error message to log if the operation fails.</param>
        protected async Task ExecuteWithErrorHandlingAsync(Func<Task> operation, string errorMessage)
        {
            try
            {
                await operation();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, errorMessage);
                throw;
            }
        }

        /// <summary>
        /// Disposes the component.
        /// </summary>
        public virtual void Dispose()
        {
            if (_isDisposed)
                return;

            Logger.LogDebug("Disposing {ComponentType}", GetType().Name);

            // Dispose all subscriptions
            Disposables.Dispose();

            _isDisposed = true;
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Disposes the component asynchronously.
        /// </summary>
        public virtual async ValueTask DisposeAsync()
        {
            if (_isDisposed)
                return;

            Logger.LogDebug("Async disposing {ComponentType}", GetType().Name);

            // Perform async cleanup here
            await PerformAsyncCleanupAsync();

            // Call synchronous dispose
            Dispose();
        }

        /// <summary>
        /// Performs asynchronous cleanup.
        /// Override this method to perform asynchronous cleanup.
        /// </summary>
        protected virtual Task PerformAsyncCleanupAsync()
        {
            return Task.CompletedTask;
        }
    }
}
```

#### ViewModelBase

The `ViewModelBase` class provides a common base for all ViewModels. It should implement ReactiveUI patterns and provide common functionality for state management, error handling, and resource management.

```csharp
using System;
using System.Reactive;
using System.Reactive.Disposables;
using System.Reactive.Linq;
using Microsoft.Extensions.Logging;
using ReactiveUI;
using ReactiveUI.Fody.Helpers;

namespace YourApp.Web.Server.Features.Shared
{
    /// <summary>
    /// Base class for all ViewModels.
    /// Provides common functionality for state management, error handling, and resource management.
    /// </summary>
    public abstract class ViewModelBase : ReactiveObject, IDisposable
    {
        private readonly CompositeDisposable _disposables = new();
        private readonly ILogger _logger;

        /// <summary>
        /// Initializes a new instance of the <see cref="ViewModelBase"/> class.
        /// </summary>
        /// <param name="logger">The logger.</param>
        protected ViewModelBase(ILogger logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// Gets a value indicating whether the ViewModel is loading data.
        /// </summary>
        [Reactive]
        public bool IsLoading { get; protected set; }

        /// <summary>
        /// Gets a value indicating whether the ViewModel has unsaved changes.
        /// </summary>
        [Reactive]
        public bool IsDirty { get; protected set; }

        /// <summary>
        /// Gets the error message if an operation fails.
        /// </summary>
        [Reactive]
        public string ErrorMessage { get; protected set; } = string.Empty;

        /// <summary>
        /// Gets a value indicating whether the ViewModel has an error.
        /// </summary>
        [Reactive]
        public bool HasError { get; protected set; }

        /// <summary>
        /// Marks the ViewModel as dirty.
        /// </summary>
        public virtual void MarkAsDirty()
        {
            IsDirty = true;
        }

        /// <summary>
        /// Marks the ViewModel as clean.
        /// </summary>
        public virtual void MarkAsClean()
        {
            IsDirty = false;
        }

        /// <summary>
        /// Sets an error message and marks the ViewModel as having an error.
        /// </summary>
        /// <param name="message">The error message.</param>
        protected virtual void SetError(string message)
        {
            ErrorMessage = message;
            HasError = !string.IsNullOrEmpty(message);
        }

        /// <summary>
        /// Clears the error message and marks the ViewModel as not having an error.
        /// </summary>
        protected virtual void ClearError()
        {
            ErrorMessage = string.Empty;
            HasError = false;
        }

        /// <summary>
        /// Executes an operation with loading state and error handling.
        /// </summary>
        /// <typeparam name="T">The type of the result.</typeparam>
        /// <param name="operation">The operation to execute.</param>
        /// <param name="errorMessage">The error message to set if the operation fails.</param>
        /// <returns>The result of the operation, or default if the operation fails.</returns>
        protected async Task<T?> ExecuteWithLoadingAsync<T>(Func<Task<T>> operation, string errorMessage)
        {
            try
            {
                IsLoading = true;
                ClearError();

                return await operation();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, errorMessage);
                SetError($"{errorMessage}: {ex.Message}");
                return default;
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// Executes an operation with loading state and error handling.
        /// </summary>
        /// <param name="operation">The operation to execute.</param>
        /// <param name="errorMessage">The error message to set if the operation fails.</param>
        /// <returns>True if the operation succeeds, false otherwise.</returns>
        protected async Task<bool> ExecuteWithLoadingAsync(Func<Task> operation, string errorMessage)
        {
            try
            {
                IsLoading = true;
                ClearError();

                await operation();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, errorMessage);
                SetError($"{errorMessage}: {ex.Message}");
                return false;
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// Creates a command that executes an operation with loading state and error handling.
        /// </summary>
        /// <param name="operation">The operation to execute.</param>
        /// <param name="canExecute">An observable that determines when the command can execute.</param>
        /// <param name="errorMessage">The error message to set if the operation fails.</param>
        /// <returns>A ReactiveCommand that executes the operation.</returns>
        protected ReactiveCommand<Unit, Unit> CreateCommand(
            Func<Task> operation,
            IObservable<bool>? canExecute = null,
            string errorMessage = "Operation failed")
        {
            // Default can-execute observable is !IsLoading
            canExecute ??= this.WhenAnyValue(x => x.IsLoading).Select(isLoading => !isLoading);

            // Create the command
            var command = ReactiveCommand.CreateFromTask(
                async () => await ExecuteWithLoadingAsync(operation, errorMessage),
                canExecute);

            // Handle errors
            command.ThrownExceptions
                .Subscribe(ex =>
                {
                    _logger.LogError(ex, errorMessage);
                    SetError($"{errorMessage}: {ex.Message}");
                })
                .DisposeWith(_disposables);

            return command;
        }

        /// <summary>
        /// Creates a command that executes an operation with loading state and error handling.
        /// </summary>
        /// <typeparam name="TParam">The type of the parameter.</typeparam>
        /// <param name="operation">The operation to execute.</param>
        /// <param name="canExecute">An observable that determines when the command can execute.</param>
        /// <param name="errorMessage">The error message to set if the operation fails.</param>
        /// <returns>A ReactiveCommand that executes the operation.</returns>
        protected ReactiveCommand<TParam, Unit> CreateCommand<TParam>(
            Func<TParam, Task> operation,
            IObservable<bool>? canExecute = null,
            string errorMessage = "Operation failed")
        {
            // Default can-execute observable is !IsLoading
            canExecute ??= this.WhenAnyValue(x => x.IsLoading).Select(isLoading => !isLoading);

            // Create the command
            var command = ReactiveCommand.CreateFromTask<TParam>(
                async param =>
                {
                    try
                    {
                        IsLoading = true;
                        ClearError();

                        await operation(param);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, errorMessage);
                        SetError($"{errorMessage}: {ex.Message}");
                        throw;
                    }
                    finally
                    {
                        IsLoading = false;
                    }
                },
                canExecute);

            // Handle errors
            command.ThrownExceptions
                .Subscribe(ex =>
                {
                    _logger.LogError(ex, errorMessage);
                    SetError($"{errorMessage}: {ex.Message}");
                })
                .DisposeWith(_disposables);

            return command;
        }

        /// <summary>
        /// Disposes the ViewModel.
        /// </summary>
        public virtual void Dispose()
        {
            _disposables.Dispose();
            GC.SuppressFinalize(this);
        }
    }
}
```

### 4. DevExpress Integration with Tailwind CSS

#### DevExpress Component Wrappers

Create wrapper components for DevExpress components to apply consistent Tailwind CSS styling.

```razor
@using DevExpress.Blazor
@typeparam TItem

<DxGrid Data="@Data"
        CssClass="@($"app-grid {CssClass}")"
        ShowFilterRow="@ShowFilterRow"
        ShowPager="@ShowPager"
        PageSize="@PageSize"
        KeyFieldName="@KeyFieldName"
        RowClick="@(args => RowClick?.Invoke(args))">
    <Columns>
        @ColumnsContent
    </Columns>
</DxGrid>

@code {
    [Parameter]
    public IEnumerable<TItem> Data { get; set; } = null!;

    [Parameter]
    public bool ShowFilterRow { get; set; } = true;

    [Parameter]
    public bool ShowPager { get; set; } = true;

    [Parameter]
    public int PageSize { get; set; } = 10;

    [Parameter]
    public string KeyFieldName { get; set; } = "Id";

    [Parameter]
    public string CssClass { get; set; } = string.Empty;

    [Parameter]
    public RenderFragment ColumnsContent { get; set; } = null!;

    [Parameter]
    public EventCallback<GridRowClickEventArgs> RowClick { get; set; }
}
```

#### Tailwind CSS for DevExpress Components

Create a CSS file with Tailwind utility classes for DevExpress components.

```css
/* app-grid.css */
.app-grid :deep(.dxbl-grid-header) {
    @apply bg-white font-medium text-gray-500 border-b border-gray-100;
}

.app-grid :deep(.dxbl-grid-header-content) {
    @apply py-3 px-4;
}

.app-grid :deep(.dxbl-grid-data-row) {
    @apply border-b border-gray-50 hover:bg-gray-50;
}

.app-grid :deep(.dxbl-grid-cell-data) {
    @apply py-3 px-4 text-gray-600;
}

.app-grid :deep(.dxbl-grid-empty-data) {
    @apply p-8 text-gray-500 italic bg-white;
}
```

## DTO-Based Editing Workflow

### 1. DTO Structure

DTOs (Data Transfer Objects) are used to transfer data between the frontend and backend. They should be designed to be serializable and contain only the data needed for the UI.

```csharp
namespace YourApp.Shared.DTOs
{
    public class FeatureDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public bool IsActive { get; set; }

        // Navigation properties
        public List<ChildItemDto> ChildItems { get; set; } = new();
    }

    public class ChildItemDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public Guid ParentId { get; set; }
    }
}
```

### 2. DTO Service

Create services to handle DTO operations like copying, updating, and creating DTOs.

```csharp
namespace YourApp.Web.Server.Services.DTO
{
    public class DtoService
    {
        private readonly ILogger<DtoService> _logger;

        public DtoService(ILogger<DtoService> logger)
        {
            _logger = logger;
        }

        public TDto Copy<TDto>(TDto source) where TDto : class, new()
        {
            if (source == null)
            {
                _logger.LogWarning("Attempted to copy null DTO");
                return new TDto();
            }

            // Create a deep copy of the DTO
            var json = JsonSerializer.Serialize(source);
            return JsonSerializer.Deserialize<TDto>(json) ?? new TDto();
        }

        public TDto Update<TDto>(TDto target, TDto source) where TDto : class
        {
            if (target == null || source == null)
            {
                _logger.LogWarning("Attempted to update with null DTO");
                return target ?? new TDto();
            }

            // Update properties from source to target
            var sourceProps = typeof(TDto).GetProperties();
            foreach (var prop in sourceProps)
            {
                if (prop.CanWrite)
                {
                    var value = prop.GetValue(source);
                    prop.SetValue(target, value);
                }
            }

            return target;
        }
    }
}
```

### 3. DTO-Based Editing Pattern

The DTO-based editing pattern involves:

1. Loading DTOs from the backend
2. Making changes to the DTOs in the UI
3. Tracking changes with the `IsDirty` flag
4. Saving all changes at once when the user explicitly saves

```csharp
// In ViewModel
public async Task LoadDataAsync()
{
    try
    {
        IsLoading = true;

        // Load DTO from backend
        var dto = await _featureService.GetByIdAsync(Id);

        // Make a working copy of the DTO
        WorkingCopy = _dtoService.Copy(dto);

        MarkAsClean();
    }
    finally
    {
        IsLoading = false;
    }
}

public async Task SaveAsync()
{
    try
    {
        IsLoading = true;

        // Save the working copy back to the backend
        await _featureService.SaveAsync(WorkingCopy);

        MarkAsClean();
    }
    finally
    {
        IsLoading = false;
    }
}

// Track changes
public void OnPropertyChanged()
{
    MarkAsDirty();
}
```

## Component Communication

### 1. Parameter Binding

Use parameters to pass data from parent to child components:

```razor
<FeatureComponent
    Data="@ViewModel.Data"
    IsReadOnly="@ViewModel.IsReadOnly"
    OnDataChanged="@ViewModel.HandleDataChanged" />
```

### 2. Events and Callbacks

Use events and callbacks for child-to-parent communication:

```csharp
// In child component
[Parameter]
public EventCallback<DataChangedEventArgs> OnDataChanged { get; set; }

// In method
await OnDataChanged.InvokeAsync(new DataChangedEventArgs(data));

// In parent component
public void HandleDataChanged(DataChangedEventArgs args)
{
    // Update data
    Data = args.Data;

    // Mark as dirty
    MarkAsDirty();
}
```

### 3. Services for Cross-Component Communication

Use services for communication between components that are not directly related:

```csharp
// Service definition
public interface INotificationService
{
    event Action<string, NotificationType> OnNotification;
    void ShowNotification(string message, NotificationType type);
}

public class NotificationService : INotificationService
{
    public event Action<string, NotificationType> OnNotification;

    public void ShowNotification(string message, NotificationType type)
    {
        OnNotification?.Invoke(message, type);
    }
}

// In component that shows notifications
@implements IDisposable
@inject INotificationService NotificationService

@code {
    protected override void OnInitialized()
    {
        NotificationService.OnNotification += HandleNotification;
    }

    private void HandleNotification(string message, NotificationType type)
    {
        // Show notification
        // ...

        StateHasChanged();
    }

    public void Dispose()
    {
        NotificationService.OnNotification -= HandleNotification;
    }
}

// In component that triggers notifications
@inject INotificationService NotificationService

@code {
    private void SaveCompleted()
    {
        NotificationService.ShowNotification("Data saved successfully", NotificationType.Success);
    }
}
```

### 4. URL Generation with GetUri Methods

Implement a consistent approach to URL generation throughout the application using static GetUri methods defined in view components.

#### What are GetUri Methods?

GetUri methods are static utility methods defined in view components that generate consistent URLs for navigation. They provide a centralized place for URL generation logic, making it easier to maintain and update URL patterns.

#### Where to Define GetUri Methods

Define GetUri methods in the `@code` block of Razor pages, typically in both list and detail views:

```csharp
// In VehicleListView.razor
@code {
    // Static method for generating the URL to this view
    public static string GetUri()
    {
        return "/Vehicles/List";
    }
}

// In VehicleDetailsView.razor
@code {
    // Static method for generating the URL to this view with a parameter
    public static string GetUri(string vehicleId)
    {
        return $"/Vehicles/Details/{Base64UrlEncoder.Encode(vehicleId)}";
    }
}
```

#### Implementation Guidelines

1. **Parameter Encoding**: Always encode parameters for security:

```csharp
// Proper parameter encoding
public static string GetUri(string organizationId)
{
    return $"/Organizations/Details/{Base64UrlEncoder.Encode(organizationId)}";
}
```

2. **Overloads for Different Parameter Combinations**:

```csharp
// No parameters
public static string GetUri()
{
    return "/Vehicles/List";
}

// Single parameter
public static string GetUri(string vehicleId)
{
    return $"/Vehicles/Details/{Base64UrlEncoder.Encode(vehicleId)}";
}

// Multiple parameters
public static string GetUri(string vehicleId, string tabName)
{
    return $"/Vehicles/Details/{Base64UrlEncoder.Encode(vehicleId)}?tab={Uri.EscapeDataString(tabName)}";
}
```

3. **Query Parameters**:

```csharp
// With query parameters
public static string GetUri(string vehicleId, bool includeInactive = false)
{
    var baseUrl = $"/Vehicles/Details/{Base64UrlEncoder.Encode(vehicleId)}";

    if (includeInactive)
    {
        return $"{baseUrl}?includeInactive=true";
    }

    return baseUrl;
}
```

#### Usage Examples

1. **With NavigationManager**:

```csharp
@inject NavigationManager NavigationManager

@code {
    protected Task OnClick_ViewVehicles()
    {
        NavigationManager.NavigateTo(VehicleListView.GetUri());
        return Task.CompletedTask;
    }
}
```

2. **In Razor Markup**:

```razor
<a href="@OrganizationDetailsView.GetUri(item.OrganizationId)" class="btn btn-primary">
    View Details
</a>
```

3. **In Command Handlers**:

```csharp
private void OnRowClick(OrganizationListItem item)
{
    NavigationManager.NavigateTo(OrganizationDetailsView.GetUri(item.OrganizationId));
}
```

4. **In ReactiveUI Commands**:

```csharp
// In ViewModel
public ReactiveCommand<VehicleDto, Unit> ViewDetailsCommand { get; }

public VehicleListViewModel(NavigationManager navigationManager)
{
    ViewDetailsCommand = ReactiveCommand.Create<VehicleDto>(
        vehicle => navigationManager.NavigateTo(VehicleDetailsView.GetUri(vehicle.Id.ToString()))
    );
}
```

#### Benefits

1. **Centralized URL Logic**: All URL generation logic is in one place, making it easier to maintain.
2. **Type Safety**: Using static methods provides compile-time checking.
3. **Refactoring Support**: Changing a URL pattern only requires updating the GetUri method, not every usage.
4. **Security**: Consistent parameter encoding prevents security issues.
5. **Discoverability**: Developers can easily find and use the correct URL patterns.

## Error Handling, Validation, and Loading States

### 1. Error Handling

Implement consistent error handling throughout the application:

```csharp
// In ViewModel
private async Task ExecuteWithErrorHandlingAsync(Func<Task> operation, string errorMessage)
{
    try
    {
        await operation();
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, errorMessage);
        ErrorMessage = $"{errorMessage}: {ex.Message}";

        // Show notification
        _notificationService.ShowError(ErrorMessage);
    }
}

// Usage
public async Task SaveAsync()
{
    await ExecuteWithErrorHandlingAsync(
        async () =>
        {
            IsLoading = true;
            try
            {
                await _featureService.SaveAsync(WorkingCopy);
                MarkAsClean();
                _notificationService.ShowSuccess("Data saved successfully");
            }
            finally
            {
                IsLoading = false;
            }
        },
        "Failed to save data");
}
```

### 2. Validation Patterns

Implement consistent validation patterns throughout the application using a combination of data annotations, custom validation, and reactive validation.

#### Data Annotation Validation

Use data annotations on DTOs for basic validation:

```csharp
public class FeatureDto
{
    public Guid Id { get; set; }

    [Required(ErrorMessage = "Name is required")]
    [StringLength(100, ErrorMessage = "Name cannot exceed 100 characters")]
    public string Name { get; set; } = string.Empty;

    [StringLength(500, ErrorMessage = "Description cannot exceed 500 characters")]
    public string Description { get; set; } = string.Empty;

    public bool IsActive { get; set; }
}
```

#### ViewModel Validation

Implement validation in ViewModels using ReactiveUI:

```csharp
public class FeatureViewModel : ViewModelBase
{
    private readonly ObservableAsPropertyHelper<bool> _isValid;
    public bool IsValid => _isValid.Value;

    [Reactive]
    public string Name { get; set; } = string.Empty;

    [Reactive]
    public string Description { get; set; } = string.Empty;

    [Reactive]
    public Dictionary<string, string> ValidationErrors { get; private set; } = new();

    public FeatureViewModel(ILogger<FeatureViewModel> logger) : base(logger)
    {
        // Set up validation
        this.WhenAnyValue(x => x.Name)
            .Subscribe(name => ValidateName(name));

        // Derive IsValid from ValidationErrors
        this.WhenAnyValue(x => x.ValidationErrors)
            .Select(errors => errors.Count == 0)
            .ToProperty(this, x => x.IsValid, out _isValid);

        // Make SaveCommand depend on IsValid
        SaveCommand = ReactiveCommand.CreateFromTask(
            SaveAsync,
            this.WhenAnyValue(
                x => x.IsLoading,
                x => x.IsDirty,
                x => x.IsValid,
                (isLoading, isDirty, isValid) => !isLoading && isDirty && isValid)
        );
    }

    private void ValidateName(string name)
    {
        if (string.IsNullOrWhiteSpace(name))
        {
            AddValidationError("Name", "Name is required");
        }
        else if (name.Length > 100)
        {
            AddValidationError("Name", "Name cannot exceed 100 characters");
        }
        else
        {
            RemoveValidationError("Name");
        }
    }

    private void AddValidationError(string property, string error)
    {
        var errors = new Dictionary<string, string>(ValidationErrors)
        {
            [property] = error
        };
        ValidationErrors = errors;
    }

    private void RemoveValidationError(string property)
    {
        if (ValidationErrors.ContainsKey(property))
        {
            var errors = new Dictionary<string, string>(ValidationErrors);
            errors.Remove(property);
            ValidationErrors = errors;
        }
    }
}
```

#### Form Validation in Razor Components

Display validation errors in Razor components:

```razor
<div class="form-group">
    <label for="name">Name</label>
    <input id="name" class="form-control @(ViewModel.ValidationErrors.ContainsKey("Name") ? "is-invalid" : "")"
           @bind-value="ViewModel.Name" @bind-value:event="oninput" />
    @if (ViewModel.ValidationErrors.TryGetValue("Name", out var nameError))
    {
        <div class="invalid-feedback">@nameError</div>
    }
</div>

<button class="btn btn-primary" @onclick="Save" disabled="@(!ViewModel.IsValid)">Save</button>
```

#### Validation Service

For complex validation scenarios, create a validation service:

```csharp
public interface IValidationService
{
    Task<Dictionary<string, string>> ValidateAsync<T>(T entity);
    bool IsValid(Dictionary<string, string> errors);
}

public class ValidationService : IValidationService
{
    private readonly IServiceProvider _serviceProvider;

    public ValidationService(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
    }

    public async Task<Dictionary<string, string>> ValidateAsync<T>(T entity)
    {
        var errors = new Dictionary<string, string>();

        // Data annotation validation
        var validationContext = new ValidationContext(entity, _serviceProvider, null);
        var validationResults = new List<ValidationResult>();

        if (!Validator.TryValidateObject(entity, validationContext, validationResults, true))
        {
            foreach (var validationResult in validationResults)
            {
                foreach (var memberName in validationResult.MemberNames)
                {
                    errors[memberName] = validationResult.ErrorMessage;
                }
            }
        }

        // Custom validation logic
        await CustomValidateAsync(entity, errors);

        return errors;
    }

    private Task CustomValidateAsync<T>(T entity, Dictionary<string, string> errors)
    {
        // Implement custom validation logic here
        return Task.CompletedTask;
    }

    public bool IsValid(Dictionary<string, string> errors)
    {
        return errors.Count == 0;
    }
}
```

### 3. Loading States

Implement consistent loading states throughout the application:

```razor
@if (ViewModel.IsLoading)
{
    <div class="flex justify-center p-8">
        <div class="animate-spin rounded-full h-12 w-12 border-4 border-gray-300 border-t-gray-800"></div>
    </div>
}
else if (!string.IsNullOrEmpty(ViewModel.ErrorMessage))
{
    <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
        <p>@ViewModel.ErrorMessage</p>
    </div>
}
else
{
    <!-- Content -->
}
```

```csharp
// In ViewModel
[Reactive]
public bool IsLoading { get; private set; }

[Reactive]
public string ErrorMessage { get; private set; } = string.Empty;

public async Task LoadDataAsync()
{
    try
    {
        IsLoading = true;
        ErrorMessage = string.Empty;

        // Load data
        // ...
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "Error loading data");
        ErrorMessage = $"Failed to load data: {ex.Message}";
    }
    finally
    {
        IsLoading = false;
    }
}
```

## Best Practices and Recommendations

### 1. File Organization

- **Feature-Based Organization**: Organize code by feature rather than by type
- **Consistent File Structure**: Follow the same file structure for all features
- **Separation of Concerns**: Keep UI, logic, and data access separate
- **Clean Separation of Files**: Keep HTML, C#, and CSS in separate files

### 2. Naming Conventions

- **Consistent Naming**: Use consistent naming conventions throughout the project
- **Descriptive Names**: Use descriptive names for components, ViewModels, and services
- **File Naming**: Follow the naming conventions outlined in this guide

### 3. Component Design

- **Single Responsibility**: Each component should have a single responsibility
- **Composition**: Compose complex UIs from smaller components
- **Reusability**: Design components to be reusable where appropriate
- **DevExpress Integration**: Use DevExpress components with Tailwind CSS styling

### 4. ViewModel Design

- **Reactive Properties**: Use ReactiveUI's reactive properties for UI state
- **Commands**: Use ReactiveUI commands for user actions
- **Error Handling**: Implement consistent error handling
- **Loading States**: Implement consistent loading states
- **ReactiveUI-Blazor Synchronization**: Ensure proper synchronization between ReactiveUI properties and Blazor's rendering cycle

## Component Lifecycle Management

Proper management of the Blazor component lifecycle is crucial for building maintainable and performant applications. This section outlines best practices for handling component lifecycle events and common pitfalls to avoid.

### 1. Blazor Component Lifecycle Overview

Blazor components go through a series of lifecycle events:

1. **Construction**: The component is instantiated.
2. **Parameter Initialization**: `SetParametersAsync` is called with initial parameters.
3. **Initialization**: `OnInitialized`/`OnInitializedAsync` are called.
4. **Parameter Setting**: `OnParametersSet`/`OnParametersSetAsync` are called.
5. **Rendering**: The component is rendered.
6. **After Render**: `OnAfterRender`/`OnAfterRenderAsync` are called.
7. **Disposal**: `Dispose`/`DisposeAsync` are called when the component is removed.

### 2. Lifecycle Method Usage Guidelines

#### OnInitialized / OnInitializedAsync

Use for:
- One-time initialization
- Setting up subscriptions
- Initial data loading

```csharp
protected override async Task OnInitializedAsync()
{
    await base.OnInitializedAsync();

    // One-time initialization
    SetupSubscriptions();

    // Initial data loading
    await ViewModel.LoadDataAsync();
}
```

#### OnParametersSet / OnParametersSetAsync

Use for:
- Responding to parameter changes
- Validating parameters
- Updating state based on parameter changes

```csharp
protected override async Task OnParametersSetAsync()
{
    await base.OnParametersSetAsync();

    // Check if parameters have changed
    if (_previousId != Id)
    {
        _previousId = Id;
        await ViewModel.LoadDataAsync(Id);
    }
}
```

#### OnAfterRender / OnAfterRenderAsync

Use for:
- JavaScript interop
- DOM manipulation
- Measurements that require the component to be rendered

```csharp
protected override async Task OnAfterRenderAsync(bool firstRender)
{
    await base.OnAfterRenderAsync(firstRender);

    if (firstRender)
    {
        // JavaScript interop
        await JSRuntime.InvokeVoidAsync("initializeComponent", DotNetObjectReference.Create(this));
    }
}
```

#### Dispose / DisposeAsync

Use for:
- Cleaning up resources
- Unsubscribing from events
- Releasing JavaScript interop references

```csharp
public override void Dispose()
{
    // Unsubscribe from events
    _subscription?.Dispose();

    // Release JavaScript interop references
    _dotNetObjectReference?.Dispose();

    base.Dispose();
}

public override async ValueTask DisposeAsync()
{
    // Unregister JavaScript event handlers
    await JSRuntime.InvokeVoidAsync("unregisterEventHandlers");

    // Call synchronous dispose
    Dispose();
}
```

### 3. Common Lifecycle Pitfalls

#### Double Loading

Problem: Data is loaded in both `OnInitializedAsync` and `OnParametersSetAsync`.

Solution: Use a flag to track initialization or load data only in `OnParametersSetAsync`.

```csharp
private bool _isInitialized;

protected override async Task OnInitializedAsync()
{
    await base.OnInitializedAsync();
    _isInitialized = true;
    // Don't load data here
}

protected override async Task OnParametersSetAsync()
{
    await base.OnParametersSetAsync();

    // Load data only once or when parameters change
    if (!_isInitialized || _previousId != Id)
    {
        _previousId = Id;
        await ViewModel.LoadDataAsync(Id);
    }
}
```

#### Memory Leaks

Problem: Subscriptions and event handlers are not properly disposed.

Solution: Implement `IDisposable` and `IAsyncDisposable` and clean up resources.

```csharp
private readonly CompositeDisposable _disposables = new();

protected override void OnInitialized()
{
    // Subscribe to events
    Observable.FromEventPattern<PropertyChangedEventArgs>(
        h => SomeObject.PropertyChanged += h,
        h => SomeObject.PropertyChanged -= h)
        .Subscribe(_ => StateHasChanged())
        .DisposeWith(_disposables);
}

public void Dispose()
{
    _disposables.Dispose();
}
```

#### Infinite Rendering Loops

Problem: State changes in `OnAfterRender` trigger re-renders, causing an infinite loop.

Solution: Use a flag to prevent multiple updates or move state changes to appropriate lifecycle methods.

```csharp
private bool _hasUpdatedSize;

protected override void OnAfterRender(bool firstRender)
{
    if (firstRender && !_hasUpdatedSize)
    {
        _hasUpdatedSize = true;
        // Update state and trigger a re-render
        StateHasChanged();
    }
}
```

#### Prerendering Issues

Problem: JavaScript interop fails during prerendering.

Solution: Check if the component is prerendering before calling JavaScript.

```csharp
[Inject]
private IJSRuntime JSRuntime { get; set; } = null!;

[Inject]
private IComponentContext ComponentContext { get; set; } = null!;

protected override async Task OnAfterRenderAsync(bool firstRender)
{
    if (firstRender && !ComponentContext.IsPrerendering)
    {
        await JSRuntime.InvokeVoidAsync("initializeComponent");
    }
}
```

### 4. ReactiveUI-Blazor Synchronization

When using ReactiveUI with Blazor Server, changes to reactive properties might not automatically trigger UI updates unless properly managed. This is because ReactiveUI's property change notifications operate independently from Blazor's rendering cycle.

#### Synchronization Challenges

```csharp
// This might not update the UI automatically
[Reactive]
public string SearchText { get; set; }

// Later in code
SearchText = "New Value"; // UI might not update
```

#### Solutions

1. **Manual StateHasChanged**

```csharp
// In ViewModel
[Reactive]
public string SearchText { get; set; }

// In View code-behind
private IDisposable _searchTextSubscription;

protected override void OnInitialized()
{
    base.OnInitialized();

    // Subscribe to property changes and trigger StateHasChanged
    _searchTextSubscription = ViewModel.WhenAnyValue(x => x.SearchText)
        .Subscribe(_ => StateHasChanged());
}

public void Dispose()
{
    _searchTextSubscription?.Dispose();
}
```

2. **Helper Method in ViewBase**

```csharp
// In ViewBase
protected void WhenPropertyChanged<T>(Expression<Func<TViewModel, T>> property, Action? action = null)
{
    var subscription = ViewModel.WhenAnyValue(property)
        .Subscribe(_ =>
        {
            action?.Invoke();
            InvokeAsync(StateHasChanged);
        });

    Disposables.Add(subscription);
}

// Usage in View
protected override void OnInitialized()
{
    base.OnInitialized();

    // Subscribe to property changes
    WhenPropertyChanged(x => x.SearchText);
    WhenPropertyChanged(x => x.IsLoading);
}
```

3. **InvokeAsync for Thread Safety**

Always use `InvokeAsync` when calling `StateHasChanged` from reactive subscriptions to ensure thread safety:

```csharp
private async void HandlePropertyChanged(object sender, PropertyChangedEventArgs e)
{
    await InvokeAsync(StateHasChanged);
}
```

#### Best Practices

1. **Centralize Subscription Management**: Handle subscriptions in a base class to avoid boilerplate code
2. **Dispose Subscriptions**: Always dispose subscriptions to prevent memory leaks
3. **Batch Updates**: For multiple property changes, batch UI updates to improve performance
4. **Use ReactiveUI.Blazor**: Consider using the ReactiveUI.Blazor package which provides integration helpers

```csharp
// Example of batching updates
public void UpdateMultipleProperties()
{
    using (DelayChangeNotifications())
    {
        Property1 = "New Value 1";
        Property2 = "New Value 2";
        Property3 = "New Value 3";
    } // StateHasChanged will be called once after this block
}
```

### 5. Service Design

- **Interface-Based Design**: Define interfaces for services to enable testing and replacement
- **Single Responsibility**: Each service should have a single responsibility
- **Dependency Injection**: Use dependency injection for services

### 6. Testing

- **Component Testing**: Design components to be testable
- **ViewModel Testing**: Keep logic in ViewModels for easier testing
- **Service Testing**: Design services to be testable
- **Mocking**: Use interfaces to enable mocking for tests

## Data Flow Patterns

Efficient data flow is crucial for building performant and maintainable Blazor applications. This section outlines recommended patterns for managing data flow between components, ViewModels, and services.

### 1. Unidirectional Data Flow

Unidirectional data flow is a pattern where data flows in one direction, making the application state more predictable and easier to debug.

#### Implementation

1. **State Container**: Define a central state container (ViewModel) that holds the application state.
2. **Actions**: Define actions that can be performed on the state.
3. **Reducers**: Define reducers that update the state based on actions.
4. **Subscribers**: Components subscribe to state changes and update the UI accordingly.

```csharp
// State container
public class AppState
{
    private readonly BehaviorSubject<ImmutableList<TodoItem>> _todos =
        new(ImmutableList<TodoItem>.Empty);

    public IObservable<ImmutableList<TodoItem>> Todos => _todos.AsObservable();

    public void AddTodo(TodoItem todo)
    {
        var currentTodos = _todos.Value;
        var newTodos = currentTodos.Add(todo);
        _todos.OnNext(newTodos);
    }

    public void RemoveTodo(Guid id)
    {
        var currentTodos = _todos.Value;
        var newTodos = currentTodos.RemoveAll(t => t.Id == id);
        _todos.OnNext(newTodos);
    }
}

// Component
@inject AppState State
@implements IDisposable

@code {
    private ImmutableList<TodoItem> _todos = ImmutableList<TodoItem>.Empty;
    private IDisposable _subscription;

    protected override void OnInitialized()
    {
        _subscription = State.Todos.Subscribe(todos =>
        {
            _todos = todos;
            InvokeAsync(StateHasChanged);
        });
    }

    private void AddTodo()
    {
        var todo = new TodoItem { Id = Guid.NewGuid(), Title = "New Todo" };
        State.AddTodo(todo);
    }

    public void Dispose()
    {
        _subscription?.Dispose();
    }
}
```

### 2. Parent-Child Data Flow

Parent-child data flow is a pattern where data flows from parent components to child components through parameters, and changes flow back through events.

#### Implementation

1. **Parameters**: Pass data from parent to child through parameters.
2. **Events**: Use events to notify the parent of changes in the child.

```csharp
// Parent component
<ChildComponent Data="@data" OnDataChanged="@HandleDataChanged" />

@code {
    private MyData data = new();

    private void HandleDataChanged(MyData newData)
    {
        data = newData;
    }
}

// Child component
@code {
    [Parameter]
    public MyData Data { get; set; }

    [Parameter]
    public EventCallback<MyData> OnDataChanged { get; set; }

    private async Task UpdateData()
    {
        // Update data
        Data = new MyData { /* ... */ };

        // Notify parent
        await OnDataChanged.InvokeAsync(Data);
    }
}
```

### 3. Service-Based Data Flow

Service-based data flow is a pattern where data flows through services that act as intermediaries between components.

#### Implementation

1. **Services**: Define services that manage data and provide methods to access and update it.
2. **Observables**: Use observables to notify components of changes.

```csharp
// Service
public class DataService
{
    private readonly BehaviorSubject<ImmutableList<MyData>> _data =
        new(ImmutableList<MyData>.Empty);

    public IObservable<ImmutableList<MyData>> Data => _data.AsObservable();

    public async Task LoadDataAsync()
    {
        var data = await _apiClient.GetDataAsync();
        _data.OnNext(data.ToImmutableList());
    }

    public async Task UpdateDataAsync(MyData item)
    {
        await _apiClient.UpdateDataAsync(item);
        var currentData = _data.Value;
        var index = currentData.FindIndex(d => d.Id == item.Id);
        var newData = currentData.SetItem(index, item);
        _data.OnNext(newData);
    }
}

// Component
@inject DataService Service
@implements IDisposable

@code {
    private ImmutableList<MyData> _data = ImmutableList<MyData>.Empty;
    private IDisposable _subscription;

    protected override void OnInitialized()
    {
        _subscription = Service.Data.Subscribe(data =>
        {
            _data = data;
            InvokeAsync(StateHasChanged);
        });

        // Load data
        Service.LoadDataAsync();
    }

    private async Task UpdateItem(MyData item)
    {
        await Service.UpdateDataAsync(item);
    }

    public void Dispose()
    {
        _subscription?.Dispose();
    }
}
```

### 4. Cascading Parameters

Cascading parameters are a pattern where data flows from ancestor components to descendant components without having to pass parameters through intermediate components.

#### Implementation

1. **CascadingValue**: Use `CascadingValue` to provide data to descendant components.
2. **CascadingParameter**: Use `[CascadingParameter]` to receive data in descendant components.

```csharp
// Ancestor component
<CascadingValue Value="@theme">
    <ChildComponent />
</CascadingValue>

@code {
    private Theme theme = new();
}

// Descendant component (can be nested at any level)
@code {
    [CascadingParameter]
    private Theme Theme { get; set; }
}
```

### 5. State Management Libraries

For complex applications, consider using state management libraries like Fluxor, Blazor-State, or MediatR.

#### Fluxor Example

```csharp
// State
public record TodoState
{
    public ImmutableList<TodoItem> Todos { get; init; } = ImmutableList<TodoItem>.Empty;
}

// Action
public record AddTodoAction(TodoItem Todo);

// Reducer
public static class TodoReducer
{
    [ReducerMethod]
    public static TodoState ReduceAddTodoAction(TodoState state, AddTodoAction action)
    {
        return state with { Todos = state.Todos.Add(action.Todo) };
    }
}

// Component
@inherits FluxorComponent

@code {
    [Inject]
    private IState<TodoState> State { get; set; }

    [Inject]
    private IDispatcher Dispatcher { get; set; }

    private void AddTodo()
    {
        var todo = new TodoItem { Id = Guid.NewGuid(), Title = "New Todo" };
        Dispatcher.Dispatch(new AddTodoAction(todo));
    }
}
```

### 6. Best Practices for Data Flow

1. **Single Source of Truth**: Maintain a single source of truth for your data.
2. **Immutable Data**: Use immutable data structures to prevent unintended side effects.
3. **Minimize State**: Keep state to a minimum and derive values where possible.
4. **Predictable Updates**: Make state updates predictable and traceable.
5. **Optimize for Performance**: Use techniques like memoization and virtualization for large data sets.
6. **Proper Error Handling**: Handle errors gracefully and provide feedback to users.
7. **Loading States**: Implement loading states to provide feedback during asynchronous operations.

## Performance Optimization Techniques

Performance is a critical aspect of Blazor applications. This section outlines specific techniques for optimizing Blazor applications.

### 1. Component Rendering Optimization

#### Minimize Renders

Reduce unnecessary renders by:

1. **Using `@key` Directive**: Use the `@key` directive to help Blazor track components efficiently:

```razor
@foreach (var item in Items)
{
    <ItemComponent @key="item.Id" Item="@item" />
}
```

2. **Implementing `ShouldRender()`**: Override `ShouldRender()` to prevent unnecessary renders:

```csharp
protected override bool ShouldRender()
{
    return _shouldRender;
}

private void UpdateState()
{
    _shouldRender = true;
    StateHasChanged();
    _shouldRender = false;
}
```

3. **Using `@implements IHandleEvent`**: Implement custom event handling to control rendering:

```csharp
@implements IHandleEvent

@code {
    public Task HandleEventAsync(EventCallbackWorkItem callback, object arg)
    {
        var task = callback.InvokeAsync(arg);
        if (task.Status == TaskStatus.RanToCompletion)
        {
            StateHasChanged();
        }
        else
        {
            var _ = HandleEventAsyncCore(task);
        }
        return task;
    }

    private async Task HandleEventAsyncCore(Task task)
    {
        try
        {
            await task;
        }
        catch
        {
            // Handle exceptions
        }
        StateHasChanged();
    }
}
```

#### Component Virtualization

Use virtualization for large lists:

```razor
<Virtualize Items="@LargeDataSet" Context="item" OverscanCount="10">
    <ItemComponent Item="@item" />
</Virtualize>
```

### 2. State Management Optimization

#### Minimize State Changes

1. **Batch Updates**: Batch multiple state changes to trigger a single render:

```csharp
// In ViewModel
public void UpdateMultipleProperties()
{
    using (DelayChangeNotifications())
    {
        Property1 = "New Value 1";
        Property2 = "New Value 2";
        Property3 = "New Value 3";
    } // StateHasChanged will be called once after this block
}
```

2. **Use Immutable Collections**: Use immutable collections for efficient state updates:

```csharp
// Update a single item in an immutable list
var newList = _immutableList.Replace(oldItem, newItem);

// Add an item to an immutable list
var newList = _immutableList.Add(newItem);
```

### 3. Data Loading Optimization

#### Lazy Loading

Implement lazy loading for data:

```csharp
// In ViewModel
private async Task LoadDataAsync(int page, int pageSize)
{
    try
    {
        IsLoading = true;

        // Load only the data needed for the current page
        var pagedData = await _dataService.GetPagedDataAsync(page, pageSize);

        // Update the data source
        _dataSource.Edit(innerCache =>
        {
            if (page == 1)
            {
                innerCache.Clear();
            }
            innerCache.AddOrUpdate(pagedData);
        });
    }
    finally
    {
        IsLoading = false;
    }
}
```

#### Caching

Implement caching for frequently accessed data:

```csharp
// In DataService
private readonly Dictionary<string, (DateTime Expiry, object Data)> _cache = new();

public async Task<T> GetDataAsync<T>(string key, Func<Task<T>> dataLoader, TimeSpan? cacheExpiry = null)
{
    var expiry = cacheExpiry ?? TimeSpan.FromMinutes(5);

    if (_cache.TryGetValue(key, out var cachedItem) && cachedItem.Expiry > DateTime.UtcNow)
    {
        return (T)cachedItem.Data;
    }

    var data = await dataLoader();
    _cache[key] = (DateTime.UtcNow.Add(expiry), data);

    return data;
}
```

### 4. JavaScript Interop Optimization

#### Minimize JS Interop Calls

1. **Batch JS Calls**: Batch multiple JS calls into a single call:

```csharp
// Instead of multiple calls
await JSRuntime.InvokeVoidAsync("setProperty", "prop1", "value1");
await JSRuntime.InvokeVoidAsync("setProperty", "prop2", "value2");

// Use a single call
await JSRuntime.InvokeVoidAsync("setProperties", new Dictionary<string, string>
{
    ["prop1"] = "value1",
    ["prop2"] = "value2"
});
```

2. **Use JS Object References**: Use JS object references for multiple interactions:

```csharp
// Get a reference to a JS object
var jsObjectReference = await JSRuntime.InvokeAsync<IJSObjectReference>("getObject");

// Use the reference for multiple calls
await jsObjectReference.InvokeVoidAsync("method1");
await jsObjectReference.InvokeVoidAsync("method2");

// Dispose the reference when done
await jsObjectReference.DisposeAsync();
```

### 5. Network Optimization

#### Minimize Network Requests

1. **GraphQL for Efficient Data Fetching**: Use GraphQL to fetch only the data you need:

```csharp
// In DataService
public async Task<FeatureDto> GetFeatureAsync(Guid id, bool includeChildItems)
{
    var query = @"
        query GetFeature($id: ID!, $includeChildItems: Boolean!) {
            feature(id: $id) {
                id
                name
                description
                isActive
                childItems @include(if: $includeChildItems) {
                    id
                    name
                }
            }
        }
    ";

    var variables = new { id, includeChildItems };

    var result = await _graphQLClient.SendQueryAsync<FeatureDto>(query, variables);

    return result.Data;
}
```

2. **Compression**: Enable response compression:

```csharp
// In Startup.cs
public void ConfigureServices(IServiceCollection services)
{
    services.AddResponseCompression(options =>
    {
        options.Providers.Add<BrotliCompressionProvider>();
        options.Providers.Add<GzipCompressionProvider>();
    });
}

public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
{
    app.UseResponseCompression();
    // Other middleware
}
```

### 6. Debugging and Profiling

#### Browser Developer Tools

Use browser developer tools to identify performance issues:

1. **Performance Tab**: Record and analyze rendering performance
2. **Network Tab**: Analyze network requests and identify slow requests
3. **Memory Tab**: Identify memory leaks

#### Blazor-Specific Tools

1. **Blazor DevTools Extension**: Use the Blazor DevTools browser extension to inspect components and state
2. **Logging**: Implement performance logging for critical operations:

```csharp
public async Task<T> MeasureOperationAsync<T>(string operationName, Func<Task<T>> operation)
{
    var stopwatch = Stopwatch.StartNew();
    try
    {
        return await operation();
    }
    finally
    {
        stopwatch.Stop();
        _logger.LogInformation("{OperationName} took {ElapsedMilliseconds}ms", operationName, stopwatch.ElapsedMilliseconds);
    }
}
```

## Conclusion

This architecture guide provides a comprehensive approach to building Blazor Server applications using the MVVM pattern with ReactiveUI. By following these guidelines, you can create maintainable, testable, and scalable applications with a consistent structure and approach.

The key principles to remember are:

1. **Separation of Concerns**: Keep UI, logic, and data access separate
2. **Component-Based Development**: Build UIs from reusable components
3. **Reactive Programming**: Use ReactiveUI for responsive UIs
4. **DTO-Based Editing**: Use DTOs for data manipulation
5. **Consistent Structure**: Follow a consistent file structure and naming conventions
6. **ReactiveUI-Blazor Synchronization**: Ensure proper synchronization between ReactiveUI properties and Blazor's rendering cycle
7. **Proper Component Lifecycle Management**: Understand and properly manage the Blazor component lifecycle
8. **Efficient Data Flow**: Implement efficient data flow patterns for predictable state management
9. **Resource Management**: Properly manage resources to prevent memory leaks
10. **Performance Optimization**: Design components with performance in mind from the start
11. **Debugging and Troubleshooting**: Implement proper logging and diagnostics to facilitate debugging

By applying these principles consistently across your application, you can create a codebase that is easy to maintain, extend, and test.

## Debugging and Troubleshooting

Effective debugging and troubleshooting are essential skills for Blazor development. This section outlines strategies and techniques for identifying and resolving issues in Blazor applications.

### 1. Logging Best Practices

#### Structured Logging

Implement structured logging for better diagnostics:

```csharp
// In Program.cs
builder.Logging.AddJsonConsole(options =>
{
    options.IncludeScopes = true;
    options.TimestampFormat = "yyyy-MM-dd HH:mm:ss ";
    options.JsonWriterOptions = new JsonWriterOptions
    {
        Indented = true
    };
});

// In a component or service
private readonly ILogger<MyComponent> _logger;

public MyComponent(ILogger<MyComponent> logger)
{
    _logger = logger;
}

private void LogOperation()
{
    _logger.LogInformation("Operation {OperationName} started for {EntityType} with ID {EntityId}",
        "UpdateEntity", "Feature", 123);
}
```

#### Log Scopes

Use log scopes to group related log entries:

```csharp
public async Task ProcessEntityAsync(Guid entityId)
{
    using (_logger.BeginScope("Processing {EntityType} {EntityId}", "Feature", entityId))
    {
        _logger.LogInformation("Starting processing");

        // Process entity

        _logger.LogInformation("Processing completed");
    }
}
```

#### Log Levels

Use appropriate log levels:

- **Trace**: Detailed information for debugging
- **Debug**: Information useful for debugging
- **Information**: General information about application flow
- **Warning**: Potential issues that don't prevent the application from working
- **Error**: Errors that prevent a function from working
- **Critical**: Errors that prevent the application from working

```csharp
// In appsettings.json
{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information",
      "YourApp.Features.Critical": "Debug"
    }
  }
}
```

### 2. Browser Developer Tools

#### Blazor-Specific Debugging

1. **Enable Blazor Debug Mode**:

```csharp
// In appsettings.Development.json
{
  "DetailedErrors": true
}

// In Program.cs (for WebAssembly)
builder.WebAssemblyHostBuilder.Services.AddBlazorWebAssemblyDeveloperTools();
```

2. **Use Browser Console for Blazor Errors**:

Blazor logs errors to the browser console. Check the console for:
- JavaScript errors
- .NET exceptions
- WebAssembly errors

3. **Network Tab for SignalR Issues**:

Monitor SignalR connections in the Network tab:
- Look for failed WebSocket connections
- Check for long polling fallbacks
- Examine SignalR message payloads

### 3. Common Issues and Solutions

#### State Management Issues

1. **Component Not Updating**:

Problem: Changes to data don't reflect in the UI.

Solutions:
- Ensure `StateHasChanged()` is called after data changes
- Check if the component is properly subscribing to data changes
- Verify that ReactiveUI properties are properly implemented

```csharp
// Correct implementation
[Reactive]
public string Name { get; set; }

// Or manual implementation
private string _name;
public string Name
{
    get => _name;
    set => this.RaiseAndSetIfChanged(ref _name, value);
}
```

2. **Memory Leaks**:

Problem: Application performance degrades over time.

Solutions:
- Dispose subscriptions properly
- Implement `IDisposable` and `IAsyncDisposable` correctly
- Use weak event patterns for long-lived objects

```csharp
private readonly CompositeDisposable _disposables = new();

protected override void OnInitialized()
{
    // Subscribe to events
    Observable.FromEventPattern<PropertyChangedEventArgs>(
        h => SomeObject.PropertyChanged += h,
        h => SomeObject.PropertyChanged -= h)
        .Subscribe(_ => StateHasChanged())
        .DisposeWith(_disposables);
}

public void Dispose()
{
    _disposables.Dispose();
}
```

#### JavaScript Interop Issues

1. **JS Interop Not Working**:

Problem: JavaScript interop calls fail.

Solutions:
- Check if the JS function is properly defined
- Ensure the JS function is called after the component is rendered
- Verify that the JS function is accessible in the current scope

```csharp
// Correct implementation
protected override async Task OnAfterRenderAsync(bool firstRender)
{
    if (firstRender)
    {
        await JSRuntime.InvokeVoidAsync("initializeComponent", DotNetObjectReference.Create(this));
    }
}
```

2. **JS Interop Performance Issues**:

Problem: JS interop calls are slow.

Solutions:
- Minimize the number of JS interop calls
- Batch JS interop calls
- Use JS object references for multiple interactions

### 4. Debugging Tools and Extensions

#### Visual Studio Debugging

1. **Enable .NET Debugging**:

For Blazor Server:
- Set breakpoints in C# code
- Use the Debug menu to attach to the process

For Blazor WebAssembly:
- Enable WebAssembly debugging in project properties
- Use Chrome or Edge for debugging

2. **Debug in Visual Studio Code**:

- Install the C# extension
- Configure launch.json for Blazor debugging
- Use the JavaScript debugger for client-side code

#### Browser Extensions

1. **Blazor DevTools**:

- Inspect component hierarchy
- View component parameters
- Monitor component renders

2. **Redux DevTools** (for Fluxor):

- Monitor state changes
- Time-travel debugging
- Action replay