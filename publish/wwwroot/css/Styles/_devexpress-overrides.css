/*
 * DevExpress Component Overrides
 * This file contains overrides for DevExpress components to match our minimalist design
 */

/* Global DevExpress Tab Overrides - Extremely specific selectors to ensure they take precedence */
.dxbl-tabs,
div.dxbl-tabs,
.barret-tabs .dxbl-tabs,
.dxbl-tabs.dxbl-tabs-scrollable,
.dxbl-tabs.dxbl-tabs-scrollable.dxbl-tabs-top {
  background-color: transparent !important;
  background-image: none !important;
  border: none !important;
  box-shadow: none !important;
  border-radius: 0 !important;
}

.dxbl-tabs-header,
div.dxbl-tabs-header,
.barret-tabs .dxbl-tabs-header,
.dxbl-tabs .dxbl-tabs-header,
.dxbl-tabs-scrollable .dxbl-tabs-header,
.dxbl-tabs-top .dxbl-tabs-header {
  background-color: transparent !important;
  background-image: none !important;
  border: none !important;
  border-bottom: 1px solid var(--gray-200) !important;
  padding: 0 !important;
  box-shadow: none !important;
  border-radius: 0 !important;
}

.dxbl-tab-item,
div.dxbl-tab-item,
.barret-tabs .dxbl-tab-item,
.dxbl-tabs .dxbl-tab-item,
.dxbl-tabs-header .dxbl-tab-item {
  background-color: transparent !important;
  background-image: none !important;
  border: none !important;
  border-bottom: 1px solid transparent !important;
  border-radius: 0 !important;
  box-shadow: none !important;
  padding: var(--space-md) var(--space-lg) !important;
  margin-right: var(--space-md) !important;
  transition: all var(--transition-normal) !important;
  color: var(--gray-600) !important;
  font-weight: var(--font-weight-medium) !important;
}

.dxbl-tab-item.dxbl-active,
div.dxbl-tab-item.dxbl-active,
.barret-tabs .dxbl-tab-item.dxbl-active,
.dxbl-tabs .dxbl-tab-item.dxbl-active,
.dxbl-tabs-header .dxbl-tab-item.dxbl-active {
  background-color: transparent !important;
  background-image: none !important;
  border: none !important;
  border-bottom: 2px solid var(--gray-900) !important;
  box-shadow: none !important;
  color: var(--gray-900) !important;
  font-weight: var(--font-weight-semibold) !important;
}

.dxbl-tab-item:hover:not(.dxbl-active),
div.dxbl-tab-item:hover:not(.dxbl-active),
.barret-tabs .dxbl-tab-item:hover:not(.dxbl-active),
.dxbl-tabs .dxbl-tab-item:hover:not(.dxbl-active),
.dxbl-tabs-header .dxbl-tab-item:hover:not(.dxbl-active) {
  background-color: transparent !important;
  background-image: none !important;
  border: none !important;
  border-bottom-color: var(--gray-300) !important;
  box-shadow: none !important;
  color: var(--gray-900) !important;
}

.dxbl-tab-item-content,
div.dxbl-tab-item-content,
.barret-tabs .dxbl-tab-item-content,
.dxbl-tab-item .dxbl-tab-item-content,
.dxbl-tabs .dxbl-tab-item-content {
  background-color: transparent !important;
  background-image: none !important;
  padding: 0 !important;
  box-shadow: none !important;
  border: none !important;
}

.dxbl-tab-text,
div.dxbl-tab-text,
.barret-tabs .dxbl-tab-text,
.dxbl-tab-item .dxbl-tab-text,
.dxbl-tab-item-content .dxbl-tab-text {
  color: inherit !important;
  font-weight: inherit !important;
  padding: 0 !important;
  background-color: transparent !important;
  background-image: none !important;
  border: none !important;
  box-shadow: none !important;
}

/* DevExpress Button Overrides */
.dxbl-btn {
  transition: all var(--transition-normal) !important;
}

/* DevExpress Dialog Overrides */
.dxbl-popup-content {
  background-color: #ffffff !important;
}

.dxbl-popup-header {
  background-color: #ffffff !important;
  border-bottom: var(--border-width-thin) solid var(--gray-200) !important;
}

.dxbl-popup-footer {
  background-color: #ffffff !important;
  border-top: var(--border-width-thin) solid var(--gray-200) !important;
}
