/*
 * DevExpress Component Styles
 * This file contains styles for DevExpress components
 */

/* DevExpress Grid */
.dxbl-grid {
  border: var(--border-width-thin) solid var(--color-border);
  border-radius: var(--border-radius-md);
  overflow: hidden;
  box-shadow: none;
}

/* DevExpress form controls */
.dxbl-textbox,
.dxbl-combobox,
.dxbl-dropdown,
.dxbl-spin-edit,
.dxbl-date-edit,
.dxbl-memo {
  border-radius: var(--border-radius-md) !important;
}

.dxbl-textbox-input,
.dxbl-combobox-input,
.dxbl-dropdown-input,
.dxbl-spin-edit-input,
.dxbl-date-edit-input,
.dxbl-memo-input {
  border: var(--border-width-thin) solid var(--color-border) !important;
  border-radius: var(--border-radius-md) !important;
  padding: var(--space-2) var(--space-3) !important;
  background: var(--color-background) !important;
}

/* DevExpress popup */
.dxbl-popup {
  border-radius: var(--border-radius-lg);
  overflow: hidden;
}

.dxbl-popup-header {
  background-color: var(--color-background);
  border-bottom: var(--border-width-thin) solid var(--color-border);
  padding: var(--space-3) var(--space-4);
}

.dxbl-popup-content {
  padding: var(--space-4);
}

.dxbl-popup-footer {
  background-color: var(--color-background-muted);
  border-top: var(--border-width-thin) solid var(--color-border);
  padding: var(--space-3) var(--space-4);
}

/* DevExpress tabs */
.dxbl-tabs {
  border-bottom: var(--border-width-thin) solid var(--color-border);
  margin-bottom: var(--space-4);
}

.dxbl-tab {
  padding: var(--space-2) var(--space-4);
  border-radius: var(--border-radius-md) var(--border-radius-md) 0 0;
}

.dxbl-tab-active {
  background-color: var(--color-background);
  border: var(--border-width-thin) solid var(--color-border);
  border-bottom: none;
}

.dxbl-tabs-header .dxbl-tabs-header-item.dxbl-tabs-header-item-active {
  border-bottom-color: var(--color-primary);
  border-bottom-width: var(--border-width-medium);
  color: var(--color-primary);
}

/* DevExpress buttons */
.dxbl-btn {
  border-radius: var(--border-radius-md);
  padding: var(--space-2) var(--space-3);
  min-width: 100px;
  font-weight: var(--font-weight-medium);
  box-shadow: none;
}

/* DevExpress message box */
.dxbl-msgbox {
  border-radius: var(--border-radius-lg);
  overflow: hidden;
}

.dxbl-msgbox-header {
  background-color: var(--color-background);
  border-bottom: var(--border-width-thin) solid var(--color-border);
  padding: var(--space-3) var(--space-4);
}

.dxbl-msgbox-content {
  padding: var(--space-4);
}

.dxbl-msgbox-footer {
  background-color: var(--color-background-muted);
  border-top: var(--border-width-thin) solid var(--color-border);
  padding: var(--space-3) var(--space-4);
}

/* DevExpress button overrides */
.dx-button-mode-contained.dx-button-default,
.dx-button-mode-contained.dx-button-success,
.dx-button-mode-contained.dx-buttonmode-primary {
  background-color: var(--color-primary);
  border-color: var(--color-primary);
}

.dx-button-mode-contained.dx-button-default:hover,
.dx-button-mode-contained.dx-button-success:hover,
.dx-button-mode-contained.dx-buttonmode-primary:hover {
  background-color: var(--color-primary-hover);
}

.dxbl-dropdownlist-popup .dx-item.dx-item-selected {
  background-color: var(--color-secondary);
  color: var(--color-secondary-foreground);
}

/* Custom DevExpress components */
.device-editor-tabs .tab-content {
  padding: var(--space-4);
  background-color: var(--color-background);
  border: var(--border-width-thin) solid var(--color-border);
  border-top: none;
  border-radius: 0 0 var(--border-radius-md) var(--border-radius-md);
}

/* Vehicle list grid styling */
.vehicle-list-grid {
  border-radius: var(--border-radius-md);
  overflow: hidden;
  border: var(--border-width-thin) solid var(--color-border);
}

/* Device editor tabs */
.device-editor-tabs {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
}

.device-editor-tabs .dxbl-tabs {
  display: flex;
  flex-direction: row;
  height: 450px;
}

.device-editor-tabs .dxbl-tabs-header {
  flex-direction: column;
  border-bottom: none;
  border-right: var(--border-width-thin) solid var(--color-border);
  background-color: var(--color-background);
  width: 200px;
  min-width: 200px;
  overflow-y: auto;
}

.device-editor-tabs .dxbl-tabs-header-item {
  border-right: none;
  border-bottom: var(--border-width-thin) solid var(--color-border);
  margin-right: 0;
  padding: var(--space-3);
  text-align: left;
}

.device-editor-tabs .dxbl-tabs-header-active-item {
  border-bottom-color: var(--color-border);
  border-left: var(--border-width-medium) solid var(--color-primary);
  background-color: var(--color-secondary);
  box-shadow: none;
}

.device-editor-tabs .dxbl-tabs-content {
  flex: 1;
  padding: 0;
  overflow-y: auto;
}

.device-editor-tabs .dxbl-tab-content-wrapper {
  height: 100%;
}

.tab-content-panel {
  padding: var(--space-4);
  min-height: 450px;
}

/* Form styling */
.form-group {
  margin-bottom: var(--space-3);
}

.form-label {
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--space-2);
}

.border-danger {
  border-color: var(--color-danger) !important;
}

.text-danger {
  color: var(--color-danger) !important;
}

/* Custom tab control */
.dx-tab-control .tab-content {
  padding: var(--space-4);
  background-color: var(--color-background);
  border: var(--border-width-thin) solid var(--color-border);
  border-top: none;
  border-radius: 0 0 var(--border-radius-md) var(--border-radius-md);
}
