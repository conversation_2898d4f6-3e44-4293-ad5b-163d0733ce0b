using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.Devices.Models.Antennas;
using Barret.Core.Areas.Devices.Models.Audio;
using Barret.Core.Areas.Devices.Models.Autopilots;
using Barret.Core.Areas.Devices.Models.Cameras;
using Barret.Core.Areas.Devices.Models.Engines;
using Barret.Core.Areas.Devices.Models.GenericDevices;
using Barret.Core.Areas.Devices.Models.Network;
using Barret.Core.Areas.Devices.Models.NVR;
using Barret.Core.Areas.Devices.Models.Radars;
using Barret.Core.Areas.Devices.Models.Radios;
using Barret.Core.Areas.Devices.Models.Sensors;
using Barret.Core.Areas.Devices.Models.Thrusters;
using Barret.Core.Areas.Devices.Models.Trackpilots;
using Barret.Core.Areas.Devices.Models.VCS;
using Barret.Services.Core.Areas.Devices.Factories;
using Microsoft.Extensions.Logging;

namespace Barret.Services.Areas.Devices.Factories
{
    /// <summary>
    /// Factory for creating device instances based on device roles.
    /// </summary>
    /// <param name="logger">Logger for the factory.</param>
    public class DeviceFactory(ILogger<DeviceFactory> logger) : IDeviceFactory
    {
        private readonly ILogger<DeviceFactory> _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        private readonly Dictionary<DeviceRole, Func<string, GenericDevice>> _deviceFactories = InitializeDeviceFactories();

        /// <summary>
        /// Creates a device with the specified role.
        /// </summary>
        /// <param name="role">The role of the device.</param>
        /// <returns>A new device instance with the specified role.</returns>
        public GenericDevice CreateDevice(DeviceRole role)
        {
            return CreateDevice(role, "New Device");
        }

        /// <summary>
        /// Creates a device with the specified role and name.
        /// </summary>
        /// <param name="role">The role of the device.</param>
        /// <param name="name">The name of the device.</param>
        /// <returns>A new device instance with the specified role and name.</returns>
        public GenericDevice CreateDevice(DeviceRole role, string name)
        {
            _logger.LogDebug("Creating device of role {Role} with name '{Name}'", role, name);

            if (_deviceFactories.TryGetValue(role, out var factory))
            {
                return factory(name);
            }

            _logger.LogWarning("No specific factory found for role {Role}, creating generic device", role);
            return new GenericDevice(name);
        }

        /// <summary>
        /// Initializes the device factories dictionary.
        /// </summary>
        private static Dictionary<DeviceRole, Func<string, GenericDevice>> InitializeDeviceFactories()
        {
            return new Dictionary<DeviceRole, Func<string, GenericDevice>>()
            {
                // Camera System
                { DeviceRole.Camera, name => new Camera(name) }, // Use default constructor which respects user selection
                { DeviceRole.NVRScreen, name => new NVRScreen(name) },
                { DeviceRole.NVRRecording, name => new NVRRecording(name) },

                // Propulsion
                { DeviceRole.Engine, name => new Engine(name) },
                { DeviceRole.Thruster, name => new Thruster(name) },

                // Navigation
                { DeviceRole.Radar, name => new Radar(name) },
                { DeviceRole.Antenna, name => new Antenna(name) },
                { DeviceRole.Autopilot, name => new Autopilot(name) },
                { DeviceRole.Trackpilot, name => new Trackpilot(name) },

                // Radios
                { DeviceRole.VHFMariphone, name => new VHFMariphone(name) },
                { DeviceRole.VHFNetworkInterface, name => new VHFNetworkInterface(name) },

                // Audio
                { DeviceRole.AudioHub, name => new AudioHub(name) },
                { DeviceRole.PAAudio, name => new PAAudio(name) },
                { DeviceRole.AMP, name => new AMP(name) },
                { DeviceRole.SPAP, name => new SPAP(name) },

                // VCS
                { DeviceRole.HMI, name => new HMI(name) },
                { DeviceRole.CabinetReadoutIO, name => new CabinetReadoutIO(name) },
                { DeviceRole.OperatorPanelIO, name => new OperatorPanelIO(name) },
                { DeviceRole.GPU, name => new GPU(name) },
                { DeviceRole.SafetySystemHead, name => new SafetySystemHead(name) },

                // Network
                { DeviceRole.Firewall, name => new Firewall(name) },
                { DeviceRole.Switch, name => new Switch(name) },
                { DeviceRole.Gateway, name => new Gateway(name) },
                { DeviceRole.Plc, name => new Plc(name) },

                // Sensors
                { DeviceRole.NavData, name => new NavData(name) },
                { DeviceRole.Sensor, name => new Sensor(name) },
            };
        }
    }
}
