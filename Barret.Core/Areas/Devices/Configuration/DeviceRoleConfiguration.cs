using Barret.Core.Areas.Devices.Enums;

namespace Barret.Core.Areas.Devices.Configuration
{
    /// <summary>
    /// Defines the configuration for a specific device role, determining what features and requirements apply.
    /// </summary>
    /// <remarks>
    /// Initializes a new instance of the <see cref="DeviceRoleConfiguration"/> class.
    /// </remarks>
    /// <param name="role">The device role this configuration applies to.</param>
    /// <param name="requiresModel">Whether devices with this role require a device model.</param>
    /// <param name="requiresConnection">Whether devices with this role require connection information.</param>
    /// <param name="supportsConnections">Whether devices with this role support connections with other devices.</param>
    /// <param name="supportsAlarms">Whether devices with this role support alarms.</param>
    /// <param name="supportsPosition">Whether devices with this role support position information.</param>
    public class DeviceRoleConfiguration(
        DeviceRole role,
        bool requiresModel,
        bool requiresConnection,
        bool supportsConnections,
        bool supportsAlarms,
        bool supportsPosition)
    {
        /// <summary>
        /// Gets the device role this configuration applies to.
        /// </summary>
        public DeviceRole Role { get; } = role;

        /// <summary>
        /// Gets whether devices with this role require a device model.
        /// </summary>
        public bool RequiresModel { get; } = requiresModel;

        /// <summary>
        /// Gets whether devices with this role require connection information.
        /// </summary>
        public bool RequiresConnection { get; } = requiresConnection;

        /// <summary>
        /// Gets whether devices with this role support connections with other devices.
        /// </summary>
        public bool SupportsConnections { get; } = supportsConnections;

        /// <summary>
        /// Gets whether devices with this role support interfaces with other devices.
        /// </summary>
        /// <remarks>
        /// This property is deprecated. Use SupportsConnections instead.
        /// </remarks>
        [Obsolete("Use SupportsConnections instead")]
        public bool SupportsInterfaces => SupportsConnections;

        /// <summary>
        /// Gets whether devices with this role support alarms.
        /// </summary>
        public bool SupportsAlarms { get; } = supportsAlarms;

        /// <summary>
        /// Gets whether devices with this role support position information.
        /// </summary>
        public bool SupportsPosition { get; } = supportsPosition;
    }
}
