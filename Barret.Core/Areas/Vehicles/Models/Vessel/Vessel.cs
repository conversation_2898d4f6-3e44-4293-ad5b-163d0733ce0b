using Barret.Core.Areas.DeviceGroups;
using Barret.Core.Areas.DeviceGroups.AntennaGroup;
using Barret.Core.Areas.DeviceGroups.AudioGroup;
using Barret.Core.Areas.DeviceGroups.AutopilotGroup;
using Barret.Core.Areas.DeviceGroups.HornGroup;
using Barret.Core.Areas.DeviceGroups.LightGroup;
using Barret.Core.Areas.DeviceGroups.Propulsion.ThrusterGroup;
using Barret.Core.Areas.DeviceGroups.RadarGroup;
using Barret.Core.Areas.DeviceGroups.RadioGroup;
using Barret.Core.Areas.DeviceGroups.RudderGroup;
using Barret.Core.Areas.DeviceGroups.Seafar.NetworkGroup;
using Barret.Core.Areas.DeviceGroups.Seafar.VCSGroup;
using Barret.Core.Areas.DeviceGroups.SensorGroup;
using Barret.Core.Areas.DeviceGroups.TrackpilotGroup;
using System.Text.RegularExpressions;

namespace Barret.Core.Areas.Vehicles.Models.Vessel
{
    /// <summary>
    /// Represents a vessel (ship) that can be configured in the system.
    /// </summary>
    public class Vessel : Vehicle
    {
        /// <summary>
        /// Gets or sets the vessel's identifier.
        /// </summary>
        public override string VehicleId { get; set; } = string.Empty;

        private string _name = string.Empty;

        /// <summary>
        /// Gets or sets the name of the vessel.
        /// </summary>
        /// <remarks>
        /// This is a vessel-specific name, distinct from the VehicleName which is inherited from the Vehicle base class.
        /// </remarks>
        public string Name
        {
            get => _name;
            set
            {
                if (string.IsNullOrWhiteSpace(value))
                    throw new ArgumentException("Vessel name cannot be empty", nameof(value));

                _name = value;
            }
        }

        private string _eni = string.Empty;
        private string _mmsi = string.Empty;

        /// <summary>
        /// Gets or sets the vessel ENI (European Number of Identification).
        /// </summary>
        /// <remarks>
        /// The ENI is a unique European identification number for inland vessels.
        /// Format: 8 digits, usually starting with country code (e.g., "12345678").
        /// </remarks>
        public string ENI
        {
            get => _eni;
            set
            {
                // ENI should be 8 digits
                if (value != string.Empty && !Regex.IsMatch(value, @"^\d{8}$"))
                    throw new ArgumentException("ENI must be 8 digits", nameof(value));

                _eni = value;
            }
        }

        /// <summary>
        /// Gets or sets the vessel MMSI (Maritime Mobile Service Identity).
        /// </summary>
        /// <remarks>
        /// The MMSI is a unique 9-digit number for identifying vessels in maritime radio communications.
        /// Format: 9 digits, usually starting with Maritime Identification Digits (e.g., "123456789").
        /// </remarks>
        public string MMSI
        {
            get => _mmsi;
            set
            {
                // MMSI should be 9 digits
                if (value != string.Empty && !Regex.IsMatch(value, @"^\d{9}$"))
                    throw new ArgumentException("MMSI must be 9 digits", nameof(value));

                _mmsi = value;
            }
        }

        // Vessel-specific device groups
        public ThrusterGroup ThrusterGroup { get; private set; } = new();
        public AntennaGroup AntennaGroup { get; private set; } = new();
        public AudioGroup AudioGroup { get; private set; } = new();
        public AutopilotGroup AutopilotGroup { get; private set; } = new();
        public HornGroup HornGroup { get; private set; } = new();
        public LightGroup LightGroup { get; private set; } = new();
        public RadarGroup RadarGroup { get; private set; } = new();
        public RadioGroup RadioGroup { get; private set; } = new();
        public RudderGroup RudderGroup { get; private set; } = new();
        public SeafarNetworkGroup SeafarNetworkGroup { get; private set; } = new();
        public SeafarVCSGroup SeafarVCSGroup { get; private set; } = new();
        public SensorGroup SensorGroup { get; private set; } = new();
        public TrackpilotGroup TrackpilotGroup { get; private set; } = new();

        /// <summary>
        /// Default constructor for EF Core and for creating a new vessel with default values.
        /// </summary>
        public Vessel() : base()
        {
            // Initialize with default values
            VehicleId = $"V000000";
            Name = "New Vessel";
            MMSI = "000000000"; // Default MMSI (will need to be changed for real vessels)
        }

        /// <summary>
        /// Initializes a new instance of the Vessel class with specified values.
        /// </summary>
        /// <param name="vehicleId">The ID of the vessel.</param>
        /// <param name="eni">Optional ENI number.</param>
        /// <param name="mmsi">Optional MMSI number.</param>
        /// <param name="name">Optional name of the vessel.</param>
        public Vessel(string vehicleId, string? eni = null, string? mmsi = null, string? name = null)
            : this()
        {
            VehicleId = vehicleId;

            if (eni != null)
                ENI = eni;

            if (mmsi != null)
                MMSI = mmsi;

            if (name != null)
                Name = name;
        }

        /// <summary>
        /// Copy constructor that creates a new vessel as a copy of the source vessel.
        /// Copies all devices and their connections.
        /// </summary>
        /// <param name="source">The source vessel to copy from.</param>
        /// <exception cref="ArgumentNullException">Thrown when source is null.</exception>
        public Vessel(Vessel source)
            : base(source)
        {
            ArgumentNullException.ThrowIfNull(source, nameof(source));

            // Copy vessel-specific properties
            _name = source.Name;
            _eni = source.ENI;
            _mmsi = source.MMSI;
        }



        /// <summary>
        /// Gets all device groups in this vessel.
        /// </summary>
        /// <returns>An enumerable of device groups.</returns>
        public override IEnumerable<IDeviceGroup> GetAllDeviceGroups()
        {
            // First get the base vehicle device groups
            foreach (var group in base.GetAllDeviceGroups())
            {
                yield return group;
            }

            // Then return the vessel-specific device groups
            yield return ThrusterGroup;
            yield return AntennaGroup;
            yield return AudioGroup;
            yield return AutopilotGroup;
            yield return HornGroup;
            yield return LightGroup;
            yield return RadarGroup;
            yield return RadioGroup;
            yield return RudderGroup;
            yield return SeafarNetworkGroup;
            yield return SeafarVCSGroup;
            yield return SensorGroup;
            yield return TrackpilotGroup;
        }

        /// <summary>
        /// Validates that the vessel configuration is valid.
        /// </summary>
        /// <returns>True if valid.</returns>
        /// <exception cref="InvalidOperationException">Thrown when validation fails.</exception>
        public override bool Validate()
        {
            // First validate base class
            if (!base.Validate())
                return false;

            // Validate vessel-specific properties
            if (string.IsNullOrEmpty(VehicleId))
                throw new InvalidOperationException("Vessel validation failed: VehicleId cannot be empty");

            return true;
        }

        /// <summary>
        /// Provides a string representation of this vessel.
        /// </summary>
        /// <returns>A string representation of this vessel.</returns>
        public override string ToString() =>
            $"Vessel {VehicleId} (ID: {Id}, ENI: {ENI}, MMSI: {MMSI})";

        /// <summary>
        /// Creates a clone of this vessel.
        /// </summary>
        /// <returns>A new vessel that is a copy of this vessel.</returns>
        public override Vehicle Clone()
        {
            return new Vessel(this);
        }
    }
}